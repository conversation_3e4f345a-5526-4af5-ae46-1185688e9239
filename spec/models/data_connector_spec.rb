require 'rails_helper'

RSpec.describe DataConnector, type: :model do
  let(:account) { create(:account) }
  let(:user) { create(:user, account: account) }

  describe 'associations' do
    it { should belong_to(:account) }
    it { should belong_to(:created_by).class_name('User') }
  end

  describe 'validations' do
    it { should validate_presence_of(:name) }
    it { should validate_length_of(:name).is_at_least(2).is_at_most(100) }
    it { should validate_presence_of(:connector_type) }
    it { should validate_presence_of(:connection_config) }
    it { should validate_inclusion_of(:connector_type).in_array(DataConnector::CONNECTOR_TYPES) }
  end

  describe 'enums' do
    it { should define_enum_for(:status).with_values(inactive: 0, active: 1, error: 2, testing: 3) }
    it { should define_enum_for(:test_status).with_values(never_tested: 0, test_passed: 1, test_failed: 2, test_in_progress: 3) }
  end

  describe 'constants' do
    it 'has correct connector types' do
      expected_types = %w[
        postgresql mysql sqlite csv_file json_file rest_api graphql_api
        webhook shopify salesforce hubspot mailchimp stripe google_sheets airtable
      ]
      expect(DataConnector::CONNECTOR_TYPES).to eq(expected_types)
    end
  end

  describe 'scopes' do
    let!(:active_connector) { create(:data_connector, account: account, created_by: user, status: :active) }
    let!(:inactive_connector) { create(:data_connector, account: account, created_by: user, status: :inactive) }

    it 'returns only active connectors' do
      expect(DataConnector.active).to include(active_connector)
      expect(DataConnector.active).not_to include(inactive_connector)
    end
  end

  describe 'instance methods' do
    let(:data_connector) { create(:data_connector, account: account, created_by: user) }

    describe '#display_name' do
      it 'combines name and humanized type' do
        data_connector.update!(name: 'Test DB', connector_type: 'postgresql')
        expect(data_connector.display_name).to eq('Test DB (Postgresql)')
      end
    end

    describe '#connection_healthy?' do
      it 'returns true for recently passed test' do
        data_connector.update!(
          test_status: :test_passed,
          last_tested_at: 1.hour.ago
        )
        expect(data_connector.connection_healthy?).to be true
      end

      it 'returns false for old passed test' do
        data_connector.update!(
          test_status: :test_passed,
          last_tested_at: 2.days.ago
        )
        expect(data_connector.connection_healthy?).to be false
      end

      it 'returns false for failed test' do
        data_connector.update!(
          test_status: :test_failed,
          last_tested_at: 1.hour.ago
        )
        expect(data_connector.connection_healthy?).to be false
      end
    end

    describe '#needs_testing?' do
      it 'returns true for never tested connector' do
        data_connector.update!(test_status: :never_tested)
        expect(data_connector.needs_testing?).to be true
      end

      it 'returns true for old test' do
        data_connector.update!(
          test_status: :test_passed,
          last_tested_at: 2.days.ago
        )
        expect(data_connector.needs_testing?).to be true
      end

      it 'returns false for recent test' do
        data_connector.update!(
          test_status: :test_passed,
          last_tested_at: 1.hour.ago
        )
        expect(data_connector.needs_testing?).to be false
      end
    end

    describe '#database_connector?' do
      it 'returns true for database types' do
        data_connector.update!(connector_type: 'postgresql')
        expect(data_connector.database_connector?).to be true

        data_connector.update!(connector_type: 'mysql')
        expect(data_connector.database_connector?).to be true

        data_connector.update!(connector_type: 'sqlite')
        expect(data_connector.database_connector?).to be true
      end

      it 'returns false for non-database types' do
        data_connector.update!(
          connector_type: 'rest_api',
          connection_config: { 'base_url' => 'https://api.example.com' }
        )
        expect(data_connector.database_connector?).to be false
      end
    end

    describe '#api_connector?' do
      it 'returns true for API types' do
        data_connector.update!(
          connector_type: 'rest_api',
          connection_config: { 'base_url' => 'https://api.example.com' }
        )
        expect(data_connector.api_connector?).to be true

        # Shopify connectors don't have specific validation yet
        data_connector.update!(
          connector_type: 'shopify',
          connection_config: { 'shop_domain' => 'test' }
        )
        expect(data_connector.api_connector?).to be true
      end

      it 'returns false for non-API types' do
        data_connector.update!(connector_type: 'postgresql')
        expect(data_connector.api_connector?).to be false
      end
    end

    describe '#connection_summary' do
      it 'returns database connection string' do
        data_connector.update!(
          connector_type: 'postgresql',
          connection_config: {
            'host' => 'localhost',
            'port' => '5432',
            'database' => 'test_db',
            'username' => 'test_user'
          }
        )
        expect(data_connector.connection_summary).to eq('localhost:5432/test_db')
      end

      it 'returns API URL' do
        data_connector.update!(
          connector_type: 'rest_api',
          connection_config: { 'base_url' => 'https://api.example.com' }
        )
        expect(data_connector.connection_summary).to eq('https://api.example.com')
      end
    end

    describe '#masked_config' do
      it 'masks sensitive information' do
        data_connector.update!(
          connection_config: {
            'host' => 'localhost',
            'port' => '5432',
            'database' => 'test_db',
            'username' => 'test_user',
            'password' => 'secret123',
            'api_key' => 'abc123'
          }
        )

        masked = data_connector.masked_config
        expect(masked['host']).to eq('localhost')
        expect(masked['password']).to eq('***')
        expect(masked['api_key']).to eq('***')
      end
    end
  end

  describe 'class methods' do
    describe '.available_types' do
      it 'returns grouped connector types' do
        types = DataConnector.available_types
        expect(types).to have_key('Databases')
        expect(types).to have_key('Files')
        expect(types).to have_key('APIs')
        expect(types).to have_key('SaaS Platforms')

        expect(types['Databases']).to include(hash_including(value: 'postgresql', label: 'Postgresql'))
      end
    end

    describe '.health_summary' do
      let!(:healthy_connector) do
        create(:data_connector,
               account: account,
               created_by: user,
               status: :active,
               test_status: :test_passed,
               last_tested_at: 1.hour.ago)
      end

      let!(:unhealthy_connector) do
        create(:data_connector,
               account: account,
               created_by: user,
               status: :error,
               test_status: :test_failed,
               last_tested_at: 1.hour.ago)
      end

      it 'returns correct health statistics' do
        summary = DataConnector.health_summary(account)

        expect(summary[:total]).to eq(2)
        expect(summary[:active]).to eq(1)
        expect(summary[:healthy]).to eq(1)
        expect(summary[:needs_attention]).to eq(1)
      end
    end
  end
end
