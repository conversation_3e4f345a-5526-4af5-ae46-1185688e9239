require 'rails_helper'

RSpec.describe Pipeline, type: :model do
  let(:account) { create(:account) }
  let(:user) { create(:user, account: account) }

  describe 'associations' do
    it { should belong_to(:account) }
    it { should belong_to(:created_by).class_name('User') }
    it { should have_many(:pipeline_executions).dependent(:destroy) }
  end

  describe 'validations' do
    it { should validate_presence_of(:name) }
    it { should validate_length_of(:name).is_at_least(3).is_at_most(100) }
    it { should validate_presence_of(:status) }
    it { should validate_presence_of(:schedule_type) }
    it { should validate_presence_of(:source_config) }
    it { should validate_presence_of(:destination_config) }
  end

  describe 'enums' do
    it { should define_enum_for(:status).with_values(draft: 0, active: 1, paused: 2, error: 3, archived: 4) }
    it { should define_enum_for(:schedule_type).with_values(manual: 0, real_time: 1, hourly: 2, daily: 3, weekly: 4, cron: 5) }
  end

  describe 'scopes' do
    let!(:active_pipeline) { create(:pipeline, account: account, created_by: user, status: :active) }
    let!(:draft_pipeline) { create(:pipeline, account: account, created_by: user, status: :draft) }
    let!(:scheduled_pipeline) { create(:pipeline, account: account, created_by: user, schedule_type: :daily) }
    let!(:manual_pipeline) { create(:pipeline, account: account, created_by: user, schedule_type: :manual) }

    it 'returns only active pipelines' do
      expect(Pipeline.active).to include(active_pipeline)
      expect(Pipeline.active).not_to include(draft_pipeline)
    end

    it 'returns only scheduled pipelines' do
      expect(Pipeline.scheduled).to include(scheduled_pipeline)
      expect(Pipeline.scheduled).not_to include(manual_pipeline)
    end
  end

  describe 'instance methods' do
    let(:pipeline) { create(:pipeline, account: account, created_by: user) }

    describe '#can_execute?' do
      it 'returns true for active pipeline with valid configs' do
        pipeline.update!(status: :active, source_config: { type: 'test' }, destination_config: { type: 'test' })
        expect(pipeline.can_execute?).to be true
      end

      it 'returns false for draft pipeline' do
        pipeline.update!(status: :draft)
        expect(pipeline.can_execute?).to be false
      end
    end

    describe '#source_type' do
      it 'returns the source type from config' do
        pipeline.update!(source_config: { 'type' => 'postgresql' })
        expect(pipeline.source_type).to eq('postgresql')
      end
    end

    describe '#destination_type' do
      it 'returns the destination type from config' do
        pipeline.update!(destination_config: { 'type' => 'mysql' })
        expect(pipeline.destination_type).to eq('mysql')
      end
    end

    describe '#schedule_description' do
      it 'returns correct description for manual' do
        pipeline.update!(schedule_type: :manual)
        expect(pipeline.schedule_description).to eq('Manual execution only')
      end

      it 'returns correct description for real-time' do
        pipeline.update!(schedule_type: :real_time)
        expect(pipeline.schedule_description).to eq('Real-time (as data arrives)')
      end

      it 'returns correct description for daily' do
        pipeline.update!(schedule_type: :daily, schedule_config: { 'hour' => 9, 'minute' => 30 })
        expect(pipeline.schedule_description).to eq('Daily at 9:30')
      end
    end
  end

  describe 'callbacks' do
    let(:pipeline) { create(:pipeline, account: account, created_by: user) }

    it 'sets default configs on validation' do
      new_pipeline = build(:pipeline,
                          account: account,
                          created_by: user,
                          source_config: nil,
                          destination_config: nil,
                          transformation_rules: nil,
                          schedule_config: nil)
      new_pipeline.valid?
      expect(new_pipeline.source_config).to eq({})
      expect(new_pipeline.destination_config).to eq({})
      expect(new_pipeline.transformation_rules).to eq({})
      expect(new_pipeline.schedule_config).to eq({})
    end
  end
end
