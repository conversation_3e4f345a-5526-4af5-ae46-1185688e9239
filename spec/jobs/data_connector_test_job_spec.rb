require 'rails_helper'

RSpec.describe DataConnectorTestJob, type: :job do
  let(:account) { create(:account) }
  let(:user) { create(:user, account: account) }
  let(:data_connector) { create(:data_connector, account: account, created_by: user) }

  describe '#perform' do
    context 'when connector exists' do
      it 'calls test_connection! on the connector' do
        expect_any_instance_of(DataConnector).to receive(:test_connection!).and_return(true)

        described_class.new.perform(data_connector.id)
      end

      it 'handles successful connection test' do
        # Mock the test_connection! method directly on the instance
        allow(DataConnector).to receive(:find).with(data_connector.id).and_return(data_connector)
        allow(data_connector).to receive(:test_connection!).and_return(true)

        expect {
          described_class.new.perform(data_connector.id)
        }.not_to change { data_connector.reload.test_status }
      end

      it 'handles failed connection test' do
        # Mock the test_connection! method directly on the instance
        allow(DataConnector).to receive(:find).with(data_connector.id).and_return(data_connector)
        allow(data_connector).to receive(:test_connection!).and_return(false)

        expect {
          described_class.new.perform(data_connector.id)
        }.not_to change { data_connector.reload.test_status }
      end
    end

    context 'when connector does not exist' do
      it 'handles missing connector gracefully' do
        expect {
          described_class.new.perform(999999)
        }.not_to raise_error
      end
    end

    context 'when test raises exception' do
      it 'updates connector with error status and re-raises' do
        # Mock the test_connection! method to raise an exception
        allow(DataConnector).to receive(:find).with(data_connector.id).and_return(data_connector)
        allow(data_connector).to receive(:test_connection!).and_raise(StandardError.new('Connection timeout'))

        expect {
          described_class.new.perform(data_connector.id)
        }.to raise_error(StandardError, 'Connection timeout')
      end
    end
  end
end
