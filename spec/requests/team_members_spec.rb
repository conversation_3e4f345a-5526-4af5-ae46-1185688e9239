require 'rails_helper'

RSpec.describe "TeamMembers", type: :request do
  let(:account) { create(:account) }
  let(:user) { create(:user, account: account, role: :owner) }

  before do
    sign_in user
    # Set up subdomain for multi-tenant routing
    host! "#{account.subdomain}.test"
  end

  describe "GET /team_members" do
    xit "returns http success" do
      # TODO: Fix authentication/subdomain handling in request specs
      get "/team_members", headers: { "Host" => "#{account.subdomain}.test" }
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /team_members/:id" do
    xit "returns http success" do
      # TODO: Fix authentication/subdomain handling in request specs
      team_member = create(:user, account: account)
      get "/team_members/#{team_member.id}"
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /team_members/new" do
    xit "returns http success" do
      # TODO: Fix authentication/subdomain handling in request specs
      get "/team_members/new"
      expect(response).to have_http_status(:success)
    end
  end

  describe "POST /team_members" do
    xit "creates a new team invitation" do
      # TODO: Fix authentication/subdomain handling in request specs
      expect {
        post "/team_members", params: {
          team_invitation: {
            email: '<EMAIL>',
            role: 'member'
          }
        }
      }.to change(TeamInvitation, :count).by(1)
    end
  end

  describe "GET /team_members/:id/edit" do
    xit "returns http success" do
      # TODO: Fix authentication/subdomain handling in request specs
      team_member = create(:user, account: account)
      get "/team_members/#{team_member.id}/edit"
      expect(response).to have_http_status(:success)
    end
  end

  describe "PATCH /team_members/:id" do
    xit "updates the team member" do
      # TODO: Fix authentication/subdomain handling in request specs
      team_member = create(:user, account: account, first_name: 'Old Name')
      patch "/team_members/#{team_member.id}", params: {
        user: { role: 'admin' }
      }
      expect(team_member.reload.role).to eq('admin')
    end
  end

  describe "DELETE /team_members/:id" do
    xit "destroys the team member" do
      # TODO: Fix authentication/subdomain handling in request specs
      team_member = create(:user, account: account)
      expect {
        delete "/team_members/#{team_member.id}"
      }.to change(User, :count).by(-1)
    end
  end
end
