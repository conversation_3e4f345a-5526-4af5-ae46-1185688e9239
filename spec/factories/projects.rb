FactoryBot.define do
  factory :project do
    association :account
    association :created_by, factory: :user
    sequence(:name) { |n| "Project #{n}" }
    description { "A sample project for testing" }
    status { :active }
    settings { {} }

    trait :archived do
      status { :archived }
    end

    trait :with_connectors do
      after(:create) do |project|
        create_list(:data_connector, 3, project: project, account: project.account)
      end
    end

    trait :default_project do
      name { "Default Project" }
      description { "Default project for organizing your data connectors" }
    end
  end
end
