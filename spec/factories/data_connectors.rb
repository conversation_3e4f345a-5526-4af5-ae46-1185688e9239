FactoryBot.define do
  factory :data_connector do
    association :project
    created_by { project.created_by }
    name { "Test Connector #{rand(1000)}" }
    connector_type { 'postgresql' }
    connection_config do
      {
        'host' => 'localhost',
        'port' => '5432',
        'database' => 'test_db',
        'username' => 'test_user',
        'password' => 'test_password'
      }
    end
    status { :inactive }
    test_status { :never_tested }

    trait :active do
      status { :active }
    end

    trait :tested do
      test_status { :test_passed }
      test_result { 'Connection successful' }
      last_tested_at { 1.hour.ago }
    end

    trait :failed_test do
      test_status { :test_failed }
      test_result { 'Connection failed: timeout' }
      last_tested_at { 1.hour.ago }
      status { :error }
    end

    trait :mysql do
      connector_type { 'mysql' }
      connection_config do
        {
          'host' => 'localhost',
          'port' => '3306',
          'database' => 'test_db',
          'username' => 'test_user',
          'password' => 'test_password'
        }
      end
    end

    trait :rest_api do
      connector_type { 'rest_api' }
      connection_config do
        {
          'base_url' => 'https://api.example.com',
          'api_key' => 'test_api_key'
        }
      end
    end

    trait :csv_file do
      connector_type { 'csv_file' }
      connection_config do
        {
          'file_path' => '/tmp/test.csv'
        }
      end
    end
  end
end
