FactoryBot.define do
  factory :pipeline do
    association :account
    association :created_by, factory: :user
    name { "Test Pipeline #{rand(1000)}" }
    description { "A test pipeline for integration testing" }
    status { :draft }
    schedule_type { :manual }
    source_config { { 'type' => 'postgresql', 'table' => 'users' } }
    destination_config { { 'type' => 'mysql', 'table' => 'user_sync' } }
    transformation_rules { {} }
    schedule_config { {} }

    trait :active do
      status { :active }
    end

    trait :with_schedule do
      schedule_type { :daily }
      schedule_config { { 'hour' => 9, 'minute' => 0 } }
    end

    trait :with_transformations do
      transformation_rules do
        {
          'field_mappings' => {
            'user_id' => 'id',
            'full_name' => 'name'
          },
          'filters' => [
            {
              'active' => {
                'operator' => 'equals',
                'value' => true
              }
            }
          ]
        }
      end
    end
  end
end
