# frozen_string_literal: true

FactoryBot.define do
  factory :pipeline_execution do
    pipeline { association :pipeline }

    status { 'success' }
    started_at { 1.hour.ago }
    completed_at { 30.minutes.ago }
    execution_time { 1800.0 } # 30 minutes in seconds
    records_processed { 10000 }
    records_success { 9800 }
    records_failed { 200 }
    execution_log { "[#{started_at.strftime('%Y-%m-%d %H:%M:%S')}] INFO: Execution started\n[#{completed_at.strftime('%Y-%m-%d %H:%M:%S')}] INFO: Execution completed successfully" }
    metadata { { source_records: records_processed, transformations_applied: 5 } }

    trait :failed do
      status { 'failed' }
      records_success { 0 }
      records_failed { 0 }
      error_message { 'Connection timeout to data source' }
    end

    trait :pending do
      status { 'pending' }
      completed_at { nil }
      execution_time { nil }
      records_processed { 0 }
      records_success { 0 }
      records_failed { 0 }
    end

    trait :running do
      status { 'running' }
      completed_at { nil }
      execution_time { nil }
      records_processed { 0 }
      records_success { 0 }
      records_failed { 0 }
    end

    trait :high_volume do
      records_processed { 100000 }
      records_success { 98500 }
      records_failed { 1500 }
    end

    trait :low_volume do
      records_processed { 500 }
      records_success { 490 }
      records_failed { 10 }
    end

    trait :recent do
      started_at { 10.minutes.ago }
      completed_at { 5.minutes.ago }
    end

    trait :old do
      started_at { 2.months.ago }
      completed_at { 2.months.ago + 30.minutes }
    end
  end
end
