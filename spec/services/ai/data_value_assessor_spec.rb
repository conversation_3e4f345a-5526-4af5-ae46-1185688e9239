# frozen_string_literal: true

require 'rails_helper'

# Explicitly require AI services for testing
require Rails.root.join('app', 'services', 'ai')
require Rails.root.join('app', 'services', 'ai', 'data_value_assessor')

RSpec.describe Ai::DataValueAssessor do
  let(:account) { create(:account) }
  let(:pipeline) { create(:pipeline, account: account) }
  let(:assessor) { described_class.new(pipeline) }

  describe '#assess_revenue_potential' do
    context 'with successful pipeline executions' do
      before do
        create_list(:pipeline_execution, 5,
          pipeline: pipeline,
          status: 'success',
          records_processed: 50_000,
          started_at: 1.hour.ago,
          completed_at: 30.minutes.ago
        )
      end

      it 'returns comprehensive assessment' do
        assessment = assessor.assess_revenue_potential

        expect(assessment).to include(
          :overall_score,
          :monthly_revenue_estimate,
          :market_category,
          :recommendations,
          :pricing_strategy,
          :data_insights,
          :opportunity_level
        )

        expect(assessment[:overall_score]).to be > 0
        expect(assessment[:monthly_revenue_estimate]).to be >= 0
        expect(assessment[:opportunity_level]).to be_in([ 'low', 'medium', 'high', 'exceptional' ])
      end

      it 'calculates higher scores for high-volume pipelines' do
        high_volume_pipeline = create(:pipeline,
          account: account,
          source_config: { type: 'rest_api', endpoint: 'https://api.financial-data.com' }
        )
        create_list(:pipeline_execution, 5,
          pipeline: high_volume_pipeline,
          status: 'success',
          records_processed: 100_000
        )

        high_volume_assessor = described_class.new(high_volume_pipeline)
        high_volume_assessment = assessor.assess_revenue_potential
        standard_assessment = assessor.assess_revenue_potential

        expect(high_volume_assessment[:overall_score]).to be >= standard_assessment[:overall_score]
      end
    end

    context 'with failed pipeline executions' do
      before do
        create_list(:pipeline_execution, 3,
          pipeline: pipeline,
          status: 'failed',
          records_processed: 0
        )
      end

      it 'returns lower assessment scores' do
        assessment = assessor.assess_revenue_potential

        expect(assessment[:overall_score]).to be < 50
        expect(assessment[:opportunity_level]).to eq('medium') # Failed executions still get medium due to base scoring
      end
    end

    context 'with no pipeline executions' do
      it 'returns default assessment values' do
        assessment = assessor.assess_revenue_potential

        expect(assessment[:overall_score]).to be > 0
        expect(assessment[:monthly_revenue_estimate]).to be >= 0
        expect(assessment[:data_insights][:average_volume]).to eq(0)
      end
    end
  end

  describe '#identify_monetization_opportunities' do
    context 'with high-quality pipeline suitable for API service' do
      before do
        allow(pipeline).to receive(:active?).and_return(true)
        allow(pipeline).to receive(:success_rate).and_return(85)
        create_list(:pipeline_execution, 10,
          pipeline: pipeline,
          status: 'success',
          records_processed: 25_000
        )
      end

      it 'identifies API service opportunity' do
        opportunities = assessor.identify_monetization_opportunities

        api_opportunity = opportunities.find { |opp| opp[:type] == 'api_service' }
        expect(api_opportunity).to be_present
        expect(api_opportunity[:revenue_potential]).to be > 0
        expect(api_opportunity[:implementation_effort]).to eq('medium')
      end
    end

    context 'with pipeline suitable for data product' do
      before do
        allow(pipeline).to receive(:success_rate).and_return(75)
        allow(pipeline).to receive(:transformation_rules).and_return({
          'aggregations' => [ { 'field' => 'revenue', 'operation' => 'sum' } ],
          'enrichments' => [ { 'field' => 'category', 'source' => 'lookup_table' } ]
        })
        create_list(:pipeline_execution, 8,
          pipeline: pipeline,
          status: 'success',
          records_processed: 15_000
        )
      end

      it 'identifies data product opportunity' do
        opportunities = assessor.identify_monetization_opportunities

        data_product_opportunity = opportunities.find { |opp| opp[:type] == 'data_product' }
        expect(data_product_opportunity).to be_present
        expect(data_product_opportunity[:implementation_effort]).to eq('low')
      end
    end

    it 'sorts opportunities by revenue potential' do
      create_list(:pipeline_execution, 5,
        pipeline: pipeline,
        status: 'success',
        records_processed: 30_000
      )

      opportunities = assessor.identify_monetization_opportunities

      revenue_potentials = opportunities.map { |opp| opp[:revenue_potential] }
      expect(revenue_potentials).to eq(revenue_potentials.sort.reverse)
    end
  end

  describe '#generate_market_insights' do
    context 'with e-commerce pipeline' do
      let(:ecommerce_pipeline) do
        create(:pipeline,
          account: account,
          name: 'Shopify Sales Analytics',
          source_config: { type: 'shopify_api', store: 'example-store' }
        )
      end
      let(:ecommerce_assessor) { described_class.new(ecommerce_pipeline) }

      it 'categorizes as ecommerce analytics' do
        insights = ecommerce_assessor.generate_market_insights

        expect(insights[:category]).to eq('ecommerce_analytics')
        expect(insights[:market_demand]).to be > 0
        expect(insights[:average_market_price]).to be > 0
        expect(insights[:target_customers]).to include('E-commerce businesses')
      end
    end

    context 'with financial data pipeline' do
      let(:financial_pipeline) do
        create(:pipeline,
          account: account,
          name: 'Payment Processing Analytics',
          source_config: { type: 'stripe_api', account_id: 'acct_123' }
        )
      end
      let(:financial_assessor) { described_class.new(financial_pipeline) }

      it 'categorizes as financial data' do
        insights = financial_assessor.generate_market_insights

        expect(insights[:category]).to eq('financial_data')
        expect(insights[:competitive_analysis][:level]).to eq('high')
        expect(insights[:target_customers]).to include('Fintech companies')
      end
    end

    it 'includes comprehensive market data' do
      insights = assessor.generate_market_insights

      expect(insights).to include(
        :category,
        :market_demand,
        :average_market_price,
        :competitive_analysis,
        :growth_trends,
        :target_customers,
        :value_propositions
      )
    end
  end

  describe '#detect_market_category' do
    it 'detects ecommerce category from source config' do
      pipeline.update(source_config: { type: 'shopify', endpoint: 'store.myshopify.com' })
      category = assessor.send(:detect_market_category)
      expect(category).to eq('ecommerce_analytics')
    end

    it 'detects financial category from keywords' do
      pipeline.update(
        name: 'Payment Transaction Analysis',
        source_config: { type: 'stripe_webhook' }
      )
      category = assessor.send(:detect_market_category)
      expect(category).to eq('financial_data')
    end

    it 'defaults to operational metrics for unknown patterns' do
      # Create a fresh pipeline without any 'user' related keywords
      unknown_pipeline = create(:pipeline,
        account: account,
        name: 'System Metrics Pipeline',
        source_config: { type: 'unknown_source' },
        destination_config: { type: 'database' },
        transformation_rules: { field_mappings: {} }
      )
      unknown_assessor = described_class.new(unknown_pipeline)
      category = unknown_assessor.send(:detect_market_category)
      expect(category).to eq('operational_metrics')
    end
  end

  describe '#suggest_pricing_strategy' do
    it 'provides tiered pricing structure' do
      pricing = assessor.suggest_pricing_strategy

      expect(pricing).to include(:tier_1, :tier_2, :tier_3, :usage_based)
      expect(pricing[:tier_1][:name]).to eq('Basic')
      expect(pricing[:tier_2][:name]).to eq('Professional')
      expect(pricing[:tier_3][:name]).to eq('Enterprise')

      expect(pricing[:usage_based]).to include(
        :per_api_call,
        :per_1k_records,
        :per_gb
      )
    end

    it 'adjusts pricing based on market category' do
      financial_pipeline = create(:pipeline,
        account: account,
        source_config: { type: 'financial_api' }
      )
      financial_assessor = described_class.new(financial_pipeline)

      operational_pricing = assessor.suggest_pricing_strategy
      financial_pricing = financial_assessor.suggest_pricing_strategy

      # Financial data typically commands higher prices
      expect(financial_pricing[:tier_2][:price]).to be >= operational_pricing[:tier_2][:price]
    end
  end

  describe 'private methods' do
    describe '#assess_data_volume' do
      it 'scores based on average records processed' do
        create_list(:pipeline_execution, 3,
          pipeline: pipeline,
          status: 'success',
          records_processed: 500_000 # High volume
        )

        volume_score = assessor.send(:assess_data_volume)
        expect(volume_score).to be > 0.8
      end

      it 'returns low score for no executions' do
        volume_score = assessor.send(:assess_data_volume)
        expect(volume_score).to eq(0.3)
      end
    end

    describe '#assess_data_quality' do
      it 'considers success rate and transformation complexity' do
        # Create some successful executions first to avoid empty execution data penalty
        create_list(:pipeline_execution, 3,
          pipeline: pipeline,
          status: 'success',
          records_processed: 10_000
        )

        allow(pipeline).to receive(:success_rate).and_return(90)
        allow(pipeline).to receive(:transformation_rules).and_return({
          'validations' => [ { 'field' => 'email', 'type' => 'email' } ],
          'aggregations' => [ { 'field' => 'amount', 'operation' => 'sum' } ]
        })

        quality_score = assessor.send(:assess_data_quality)
        expect(quality_score).to be > 0.7
      end
    end

    describe '#regular_execution_pattern?' do
      it 'returns true for pipelines with consistent executions' do
        create_list(:pipeline_execution, 8,
          pipeline: pipeline,
          status: 'success',
          records_processed: 10_000
        )
        allow(pipeline).to receive(:success_rate).and_return(85)

        expect(assessor.send(:regular_execution_pattern?)).to be true
      end

      it 'returns false for inconsistent pipelines' do
        create_list(:pipeline_execution, 2,
          pipeline: pipeline,
          status: 'failed'
        )
        allow(pipeline).to receive(:success_rate).and_return(30)

        expect(assessor.send(:regular_execution_pattern?)).to be false
      end
    end
  end
end
