# Technical Implementation Plan (TIP) - DataReflow Platform

## Overview
Building a multi-tenant SaaS data integration platform using Rails 8 with the Solid trifecta (SolidCache, SolidQueue, SolidCable).

## Tech Stack
- **Framework**: Rails 8.0.2
- **Database**: PostgreSQL 15+ with Row-Level Security (RLS)
- **Caching**: SolidCache (database-backed)
- **Background Jobs**: SolidQueue (database-backed)
- **WebSockets**: SolidCable (database-backed)
- **Authentication**: Devise + JWT for API
- **Authorization**: Pundit with role-based access
- **Frontend**: Hotwire (Turbo + Stimulus) + TailwindCSS
- **Payments**: Stripe
- **File Storage**: AWS S3

## Implementation Progress

### ✅ Completed
1. **Rails 8 Foundation**
   - Configured multi-database setup for Solid trifecta
   - Set up PostgreSQL with separate databases for cache, queue, and cable
   - Updated Gemfile with necessary dependencies

2. **Authentication Setup**
   - Installed Devise
   - Added devise-jwt for API authentication
   - Added JWT gem for token handling

### 🚧 In Progress
3. **Multi-Tenant Architecture**
   - Next: Create Account model with subdomain support
   - Next: Implement PostgreSQL RLS policies
   - Next: Set up tenant switching mechanism

### 📋 Upcoming Tasks
4. **Core Models**
   - Account model with subscription support
   - User model with roles (Owner, Admin, Member, Viewer)
   - Team invitation system

5. **Subscription & Billing**
   - Stripe integration
   - Plan management (Free, Starter, Professional, Enterprise)
   - Usage tracking and limits

6. **Pipeline System**
   - Pipeline configuration models
   - Connector framework
   - Execution engine with SolidQueue
   - Visual pipeline builder with Hotwire

7. **API Development**
   - JWT authentication for API
   - RESTful endpoints
   - Rate limiting per plan
   - API documentation with OpenAPI

## Key Features

### Multi-Tenancy Strategy
- **Subdomain-based routing**: company.datareflow.io
- **PostgreSQL RLS**: Complete data isolation at database level
- **Current.account pattern**: Thread-safe tenant context

### Authentication Flow
1. **Web Authentication**: Devise with email/password
2. **API Authentication**: JWT tokens with refresh mechanism
3. **2FA Support**: TOTP-based two-factor authentication
4. **API Keys**: Long-lived tokens for programmatic access

### Subscription Plans
| Plan | Price | Pipelines | Executions/mo | Support |
|------|-------|-----------|---------------|---------|
| Free | $0 | 2 | 1,000 | Community |
| Starter | $99 | 10 | 10,000 | Email |
| Professional | $299 | 50 | 100,000 | Priority |
| Enterprise | $599+ | Unlimited | Unlimited | Dedicated |

### Security Measures
- End-to-end encryption for sensitive data
- API rate limiting based on plan
- Audit logging for all actions
- SOC2 Type II compliance ready
- GDPR/CCPA data handling

## Development Workflow

### Git Strategy
```bash
main (production)
├── staging (pre-production)
│   └── develop (integration)
│       ├── feature/multi-tenancy
│       ├── feature/authentication
│       └── feature/subscription
```

### Branch Naming
- `feature/DFLOW-XXX-description`
- `bugfix/DFLOW-XXX-description`
- `hotfix/DFLOW-XXX-description`

### Commit Convention
```
type(scope): subject

Types: feat, fix, docs, style, refactor, test, chore
```

## Database Schema

### Core Tables
```sql
-- Multi-tenant foundation
accounts (id, name, subdomain, plan, status)
users (id, account_id, email, role)
subscriptions (id, account_id, plan, status)

-- Pipeline domain
pipeline_configurations (id, account_id, name, config)
pipeline_executions (id, pipeline_id, status)
data_source_connections (id, account_id, type)

-- Monitoring
audit_logs (id, account_id, user_id, action)
usage_records (id, account_id, resource_type, quantity)
```

## Performance Targets
- Page load: <2 seconds (P95)
- Pipeline execution: <30 seconds for 10K records
- API response: <200ms (P95)
- Uptime: 99.9%

## Testing Strategy
- RSpec for unit and integration tests
- Target: >80% code coverage
- Factory Bot for test data
- VCR for external API testing

## Deployment
- GitHub Actions for CI/CD
- Kamal for deployment
- CloudFlare for CDN and DDoS protection
- PostgreSQL with automated backups

## Next Steps
1. Configure Devise for multi-tenancy
2. Create Account and User models
3. Implement subdomain routing
4. Set up PostgreSQL RLS
5. Create first migration with tenant isolation

## Notes
- Using Rails 8 native Solid trifecta eliminates need for Redis/Sidekiq
- All background jobs, caching, and WebSocket connections use PostgreSQL
- This simplifies infrastructure and reduces operational complexity
- Cost-effective for SME target market

## Resources
- [Rails 8 Documentation](https://guides.rubyonrails.org/)
- [Devise JWT Documentation](https://github.com/waiting-for-dev/devise-jwt)
- [PostgreSQL RLS Guide](https://www.postgresql.org/docs/current/ddl-rowsecurity.html)
- [Stripe Integration](https://stripe.com/docs/api)

---
Last Updated: January 2025