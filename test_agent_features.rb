#!/usr/bin/env ruby

# Test script for AI Agent features
puts '=== Testing AI Agent Models ==='

# Create test account with default subscription
account = Account.create!(
  name: 'Test Company',
  subdomain: 'testai' + rand(1000).to_s
)
puts "Account created: #{account.name} (#{account.subdomain})"

# Check default subscription
subscription = account.subscription
puts "Default subscription: #{subscription.plan} - Agent features enabled: #{subscription.agent_features_enabled?}"

# Upgrade to starter plan to enable agent features
subscription.update!(plan: :starter, stripe_customer_id: "cus_test_#{rand(10000)}")
puts "Upgraded to starter plan - Agent features enabled: #{subscription.agent_features_enabled?}"
puts "Recommendations per month limit: #{subscription.recommendations_per_month_limit}"

# Test agent recommendation creation
recommendation = account.agent_recommendations.create!(
  recommendation_type: :optimization,
  title: 'Optimize Data Pipeline Performance',
  description: 'Your data pipeline could benefit from indexing improvements',
  estimated_value: 250.0, # $250/month
  confidence_score: 85, # 85%
  ai_analysis: {
    insights: [ 'Database query optimization needed', 'Index missing on user_id column' ]
  },
  implementation_steps: {
    steps: [ 'Add database index', 'Optimize query structure' ],
    estimated_hours: 4
  }
)
puts "Agent recommendation created: #{recommendation.title} (#{recommendation.status})"

# Test revenue tracking
revenue = account.agent_revenues.create!(
  revenue_source: :optimization_fee,
  amount_cents: 15000, # $150
  description: 'Revenue generated from pipeline optimization recommendation',
  agent_recommendation: recommendation
)
puts "Agent revenue created: $#{revenue.amount_cents / 100.0} from #{revenue.revenue_source}"

# Test account methods
puts "Account agent revenue this month: $#{account.agent_revenue_this_month}"
puts "Total agent revenue: $#{account.total_agent_revenue}"
puts "Agent adoption rate: #{account.agent_adoption_rate}%"
puts "Can generate recommendations: #{account.can_generate_agent_recommendations?}"
puts "Recommendations remaining: #{account.agent_recommendations_remaining_this_month}"

puts '=== AI Agent Integration Test Complete ==='

# Clean up
account.destroy!
puts "Test account cleaned up"
