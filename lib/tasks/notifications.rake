namespace :notifications do
  desc "Create sample notifications for testing"
  task seed: :environment do
    puts "Creating sample notifications..."

    Account.find_each do |account|
      user = account.users.first
      next unless user

      # Create various types of notifications
      notifications = [
        {
          title: "Pipeline Execution Completed",
          message: "Your data pipeline 'Customer Analytics' has completed successfully. 1,250 records processed.",
          notification_type: "pipeline_success",
          priority: "low"
        },
        {
          title: "Connection Issue Detected",
          message: "Database connection 'Production DB' is experiencing connectivity issues. Please check your credentials.",
          notification_type: "connector_issue",
          priority: "medium"
        },
        {
          title: "Storage Usage Warning",
          message: "You're using 85% of your storage limit. Consider upgrading your plan or cleaning up old data.",
          notification_type: "storage_warning",
          priority: "medium",
          action_url: "/subscription"
        },
        {
          title: "New Team Member Added",
          message: "<PERSON> has been added to your team as a Member.",
          notification_type: "team_update",
          priority: "low",
          action_url: "/team_members"
        },
        {
          title: "Security Alert",
          message: "Unusual login activity detected from a new location. If this wasn't you, please secure your account.",
          notification_type: "security_alert",
          priority: "critical"
        },
        {
          title: "New Feature Available",
          message: "Advanced Analytics is now available! Explore detailed insights and performance metrics for your pipelines.",
          notification_type: "feature_announcement",
          priority: "low",
          action_url: "/analytics"
        }
      ]

      notifications.each_with_index do |notification_data, index|
        Notification.create!(
          account: account,
          user: (index.even? ? user : nil), # Some account-wide, some user-specific
          title: notification_data[:title],
          message: notification_data[:message],
          notification_type: notification_data[:notification_type],
          priority: notification_data[:priority],
          action_url: notification_data[:action_url],
          created_at: rand(1..24).hours.ago,
          read_at: (index > 2 ? nil : rand(1..12).hours.ago) # Some unread
        )
      end

      puts "Created #{notifications.count} notifications for account: #{account.name}"
    end

    puts "Sample notifications created successfully!"
  end

  desc "Clear all notifications"
  task clear: :environment do
    count = Notification.count
    Notification.destroy_all
    puts "Cleared #{count} notifications"
  end

  desc "Show notification statistics"
  task stats: :environment do
    puts "Notification Statistics:"
    puts "Total notifications: #{Notification.count}"
    puts "Unread notifications: #{Notification.unread.count}"
    puts "Read notifications: #{Notification.read.count}"
    puts ""

    puts "By type:"
    Notification.group(:notification_type).count.each do |type, count|
      puts "  #{type}: #{count}"
    end
    puts ""

    puts "By priority:"
    Notification.group(:priority).count.each do |priority, count|
      puts "  #{priority}: #{count}"
    end
  end
end
