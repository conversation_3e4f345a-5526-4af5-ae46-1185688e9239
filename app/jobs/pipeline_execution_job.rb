class PipelineExecutionJob < ApplicationJob
  queue_as :default

  # Retry with exponential backoff for transient failures
  retry_on StandardError, wait: :exponentially_longer, attempts: 3

  def perform(pipeline)
    # Create execution record
    execution = pipeline.pipeline_executions.create!(
      status: :pending,
      started_at: Time.current,
      metadata: {
        job_id: job_id,
        queue: queue_name,
        priority: priority
      }
    )

    begin
      Rails.logger.info "Starting execution #{execution.id} for pipeline #{pipeline.id} (#{pipeline.name})"

      # Start the execution
      execution.start!

      # Validate pipeline can execute
      unless pipeline.can_execute?
        raise "Pipeline cannot execute - status: #{pipeline.status}"
      end

      # Extract data from source
      source_data = extract_from_source(pipeline, execution)

      # Apply transformations
      transformed_data = apply_transformations(
        source_data,
        pipeline.transformation_rules,
        execution
      )

      # Load data to destination
      load_result = load_to_destination(
        pipeline,
        transformed_data,
        execution
      )

      # Mark execution as successful
      execution.complete_success!(
        processed: load_result[:processed] || transformed_data.size,
        successful: load_result[:successful] || transformed_data.size,
        failed: load_result[:failed] || 0
      )

      Rails.logger.info "Execution #{execution.id} completed successfully"

      # Send success notification
      notify_execution_complete(pipeline, execution, success: true)

    rescue StandardError => e
      Rails.logger.error "Execution #{execution.id} failed: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      # Mark execution as failed
      execution.complete_failure!(e.message)

      # Send failure notification
      notify_execution_complete(pipeline, execution, success: false, error: e.message)

      # Re-raise to trigger retry if applicable
      raise e
    end
  end

  private

  def extract_from_source(pipeline, execution)
    execution.add_log_entry("Starting data extraction from #{pipeline.source_type}")

    source_config = pipeline.source_config

    case source_config["type"]
    when "postgresql", "mysql"
      extract_from_database(source_config, execution)
    when "rest_api"
      extract_from_api(source_config, execution)
    when "csv_file"
      extract_from_csv(source_config, execution)
    when "manual"
      # For manual sources, data is provided in the config
      source_config["data"] || []
    else
      # Try to use a connector if specified
      if source_config["connector_id"]
        extract_using_connector(source_config["connector_id"], source_config, execution)
      else
        raise "Unsupported source type: #{source_config['type']}"
      end
    end
  end

  def extract_from_database(config, execution)
    connector_id = config["connector_id"]
    raise "No connector specified for database source" unless connector_id

    connector = DataConnector.find(connector_id)
    service = connector.connector_service_class.new(connector)

    query_config = {
      "table_name" => config["table"],
      "columns" => config["columns"],
      "where" => config["where"],
      "limit" => config["limit"],
      "order" => config["order"]
    }

    result = service.extract_data(query_config)

    if result[:success]
      execution.add_log_entry("Extracted #{result[:row_count]} rows from database")
      result[:data]
    else
      raise "Database extraction failed: #{result[:error]}"
    end
  end

  def extract_from_api(config, execution)
    # Simplified API extraction - would need proper implementation
    execution.add_log_entry("Extracting data from API: #{config['url']}")

    # TODO: Implement proper API extraction with authentication, pagination, etc.
    []
  end

  def extract_from_csv(config, execution)
    # Simplified CSV extraction - would need proper implementation
    execution.add_log_entry("Extracting data from CSV: #{config['file_path']}")

    # TODO: Implement proper CSV parsing
    []
  end

  def extract_using_connector(connector_id, config, execution)
    connector = DataConnector.find(connector_id)
    service = connector.connector_service_class.new(connector)

    result = service.extract_data(config["extract_config"] || {})

    if result[:success]
      execution.add_log_entry("Extracted #{result[:data]&.size || 0} records using connector #{connector.name}")
      result[:data] || []
    else
      raise "Extraction failed: #{result[:error]}"
    end
  end

  def apply_transformations(data, rules, execution)
    return data if rules.blank? || data.empty?

    execution.add_log_entry("Applying transformations to #{data.size} records")
    transformed = data

    # Field mapping
    if rules["field_mappings"].present?
      transformed = apply_field_mappings(transformed, rules["field_mappings"])
      execution.add_log_entry("Applied field mappings")
    end

    # Filtering
    if rules["filters"].present?
      original_count = transformed.size
      transformed = apply_filters(transformed, rules["filters"])
      execution.add_log_entry("Filtered records: #{original_count} -> #{transformed.size}")
    end

    # Validation
    if rules["validations"].present?
      transformed = apply_validations(transformed, rules["validations"], execution)
    end

    # Aggregation
    if rules["aggregations"].present?
      transformed = apply_aggregations(transformed, rules["aggregations"])
      execution.add_log_entry("Applied aggregations")
    end

    execution.save!
    transformed
  end

  def apply_field_mappings(data, mappings)
    data.map do |record|
      mapped = {}
      mappings.each do |target_field, source_field|
        mapped[target_field] = record[source_field]
      end
      mapped
    end
  end

  def apply_filters(data, filters)
    data.select do |record|
      filters.all? do |field, condition|
        value = record[field]

        case condition["operator"]
        when "equals"
          value == condition["value"]
        when "not_equals"
          value != condition["value"]
        when "contains"
          value.to_s.include?(condition["value"].to_s)
        when "greater_than"
          value.to_f > condition["value"].to_f
        when "less_than"
          value.to_f < condition["value"].to_f
        when "not_null"
          !value.nil?
        else
          true
        end
      end
    end
  end

  def apply_validations(data, validations, execution)
    valid_records = []
    invalid_count = 0

    data.each do |record|
      valid = validations.all? do |field, rules|
        value = record[field]

        rules.all? do |rule|
          case rule["type"]
          when "required"
            !value.nil? && !value.to_s.empty?
          when "email"
            value.to_s.match?(/\A[\w+\-.]+@[a-z\d\-]+(\.[a-z\d\-]+)*\.[a-z]+\z/i)
          when "numeric"
            value.to_s.match?(/\A\d+\.?\d*\z/)
          when "min_length"
            value.to_s.length >= rule["value"].to_i
          when "max_length"
            value.to_s.length <= rule["value"].to_i
          else
            true
          end
        end
      end

      if valid
        valid_records << record
      else
        invalid_count += 1
      end
    end

    if invalid_count > 0
      execution.add_log_entry("Validation removed #{invalid_count} invalid records", level: "WARN")
    end

    valid_records
  end

  def apply_aggregations(data, aggregations)
    # Simple aggregation implementation
    # Would need more sophisticated handling for complex aggregations
    grouped = {}

    group_by = aggregations["group_by"]
    return data unless group_by

    data.each do |record|
      key = record[group_by]
      grouped[key] ||= []
      grouped[key] << record
    end

    # Apply aggregation functions
    result = []
    grouped.each do |key, records|
      aggregated = { group_by => key }

      aggregations["functions"]&.each do |func|
        field = func["field"]
        operation = func["operation"]

        values = records.map { |r| r[field].to_f }

        aggregated[func["alias"] || "#{operation}_#{field}"] = case operation
        when "sum"
          values.sum
        when "avg"
          values.sum / values.size
        when "count"
          values.size
        when "min"
          values.min
        when "max"
          values.max
        else
          nil
        end
      end

      result << aggregated
    end

    result
  end

  def load_to_destination(pipeline, data, execution)
    execution.add_log_entry("Loading #{data.size} records to #{pipeline.destination_type}")

    return { processed: 0, successful: 0, failed: 0 } if data.empty?

    destination_config = pipeline.destination_config

    case destination_config["type"]
    when "postgresql", "mysql"
      load_to_database(destination_config, data, execution)
    when "rest_api"
      load_to_api(destination_config, data, execution)
    when "csv_file"
      load_to_csv(destination_config, data, execution)
    else
      # Try to use a connector if specified
      if destination_config["connector_id"]
        load_using_connector(destination_config["connector_id"], destination_config, data, execution)
      else
        raise "Unsupported destination type: #{destination_config['type']}"
      end
    end
  end

  def load_to_database(config, data, execution)
    connector_id = config["connector_id"]
    raise "No connector specified for database destination" unless connector_id

    connector = DataConnector.find(connector_id)
    service = connector.connector_service_class.new(connector)

    load_config = {
      "table_name" => config["table"],
      "mode" => config["mode"] || "insert",
      "upsert_key" => config["upsert_key"]
    }

    result = service.load_data(data, load_config)

    if result[:success]
      execution.add_log_entry("Loaded #{result[:rows_loaded]} rows to database")
      {
        processed: data.size,
        successful: result[:rows_loaded] || data.size,
        failed: 0
      }
    else
      raise "Database load failed: #{result[:error]}"
    end
  end

  def load_to_api(config, data, execution)
    # Simplified API loading - would need proper implementation
    execution.add_log_entry("Loading data to API: #{config['url']}")

    # TODO: Implement proper API loading with authentication, batching, etc.
    {
      processed: data.size,
      successful: data.size,
      failed: 0
    }
  end

  def load_to_csv(config, data, execution)
    # Simplified CSV writing - would need proper implementation
    execution.add_log_entry("Writing data to CSV: #{config['file_path']}")

    # TODO: Implement proper CSV writing
    {
      processed: data.size,
      successful: data.size,
      failed: 0
    }
  end

  def load_using_connector(connector_id, config, data, execution)
    connector = DataConnector.find(connector_id)
    service = connector.connector_service_class.new(connector)

    result = service.load_data(data, config["load_config"] || {})

    if result[:success]
      execution.add_log_entry("Loaded #{result[:rows_loaded] || data.size} records using connector #{connector.name}")
      {
        processed: data.size,
        successful: result[:rows_loaded] || data.size,
        failed: 0
      }
    else
      raise "Load failed: #{result[:error]}"
    end
  end

  def notify_execution_complete(pipeline, execution, success:, error: nil)
    # Send ActionCable notification to user
    ActionCable.server.broadcast(
      "user_#{pipeline.created_by.id}",
      {
        type: "pipeline_execution_complete",
        pipeline: {
          id: pipeline.id,
          name: pipeline.name
        },
        execution: {
          id: execution.id,
          status: execution.status,
          success: success,
          records_processed: execution.records_processed,
          duration: execution.formatted_duration,
          error: error
        }
      }
    )

    # Update usage metrics
    begin
      UsageTrackingService.new(pipeline.account).record_pipeline_execution(
        pipeline,
        execution,
        success: success
      )
    rescue StandardError => e
      Rails.logger.warn "Failed to record execution metrics: #{e.message}"
    end
  end
end
