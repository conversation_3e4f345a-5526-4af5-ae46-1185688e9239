class NotificationJob < ApplicationJob
  queue_as :notifications

  def perform(account_id:, type:, data: {})
    account = Account.find(account_id)

    Rails.logger.info "Sending notification type '#{type}' to account #{account_id}"

    case type
    when "new_recommendations"
      send_new_recommendations_notification(account, data)
    when "recommendation_implemented"
      send_implementation_notification(account, data)
    when "recommendation_implementation_failed"
      send_implementation_failure_notification(account, data)
    when "revenue_milestone"
      send_revenue_milestone_notification(account, data)
    when "template_purchased"
      send_template_purchase_notification(account, data)
    when "quality_alert"
      send_quality_alert_notification(account, data)
    else
      Rails.logger.warn "Unknown notification type: #{type}"
      return
    end

    Rails.logger.info "Notification '#{type}' sent successfully to account #{account_id}"
  end

  private

  def send_new_recommendations_notification(account, data)
    count = data[:count]

    # Create in-app notification
    create_in_app_notification(
      account: account,
      title: "New AI Recommendations Available",
      message: "#{count} new recommendation#{count > 1 ? 's' : ''} #{count > 1 ? 'are' : 'is'} ready for your review",
      type: "info",
      action_url: "/agent/recommendations",
      metadata: { recommendation_count: count }
    )

    # Send email if user preferences allow
    if should_send_email?(account, "new_recommendations")
      AgentMailer.new_recommendations_email(account.users.first, count).deliver_later
    end
  end

  def send_implementation_notification(account, data)
    recommendation_title = data[:recommendation_title]
    implementation_method = data[:implementation_method]

    create_in_app_notification(
      account: account,
      title: "Recommendation Implemented",
      message: "Successfully #{implementation_method}ally implemented: #{recommendation_title}",
      type: "success",
      action_url: "/agent/recommendations/#{data[:recommendation_id]}",
      metadata: {
        recommendation_id: data[:recommendation_id],
        implementation_method: implementation_method
      }
    )

    if should_send_email?(account, "implementation_success")
      AgentMailer.recommendation_implemented_email(
        account.users.first,
        data[:recommendation_id]
      ).deliver_later
    end
  end

  def send_implementation_failure_notification(account, data)
    recommendation_title = data[:recommendation_title]
    error_message = data[:error_message]

    create_in_app_notification(
      account: account,
      title: "Implementation Failed",
      message: "Failed to implement '#{recommendation_title}': #{truncate_error(error_message)}",
      type: "error",
      action_url: "/agent/recommendations/#{data[:recommendation_id]}",
      metadata: {
        recommendation_id: data[:recommendation_id],
        error_message: error_message
      }
    )

    # Always send email for failures
    AgentMailer.implementation_failed_email(
      account.users.first,
      data[:recommendation_id],
      error_message
    ).deliver_later
  end

  def send_revenue_milestone_notification(account, data)
    milestone_amount = data[:milestone_amount]
    total_revenue = data[:total_revenue]

    create_in_app_notification(
      account: account,
      title: "Revenue Milestone Reached!",
      message: "Congratulations! You've reached $#{milestone_amount} in AI agent revenue (Total: $#{total_revenue})",
      type: "celebration",
      action_url: "/agent/revenue",
      metadata: {
        milestone_amount: milestone_amount,
        total_revenue: total_revenue
      }
    )

    if should_send_email?(account, "revenue_milestones")
      AgentMailer.revenue_milestone_email(
        account.users.first,
        milestone_amount,
        total_revenue
      ).deliver_later
    end
  end

  def send_template_purchase_notification(account, data)
    template_name = data[:template_name]
    price = data[:price]

    create_in_app_notification(
      account: account,
      title: "Template Purchased",
      message: "Successfully purchased template: #{template_name} for $#{price}",
      type: "success",
      action_url: "/agent/templates/#{data[:template_id]}",
      metadata: {
        template_id: data[:template_id],
        template_name: template_name,
        price: price
      }
    )
  end

  def send_quality_alert_notification(account, data)
    pipeline_name = data[:pipeline_name]
    alert_type = data[:alert_type]
    severity = data[:severity] || "medium"

    create_in_app_notification(
      account: account,
      title: "Data Quality Alert",
      message: "#{alert_type.humanize} detected in pipeline: #{pipeline_name}",
      type: severity == "high" ? "error" : "warning",
      action_url: "/pipelines/#{data[:pipeline_id]}",
      metadata: {
        pipeline_id: data[:pipeline_id],
        pipeline_name: pipeline_name,
        alert_type: alert_type,
        severity: severity
      }
    )

    # Send email for high severity alerts
    if severity == "high" && should_send_email?(account, "quality_alerts")
      AgentMailer.quality_alert_email(
        account.users.first,
        data[:pipeline_id],
        alert_type
      ).deliver_later
    end
  end

  def create_in_app_notification(account:, title:, message:, type:, action_url: nil, metadata: {})
    # Placeholder for in-app notification creation
    # This would integrate with your notification system
    Rails.logger.info "In-app notification created: #{title} for account #{account.id}"

    # You could create a Notification model here
    # Notification.create!(
    #   account: account,
    #   title: title,
    #   message: message,
    #   notification_type: type,
    #   action_url: action_url,
    #   metadata: metadata,
    #   read_at: nil
    # )
  end

  def should_send_email?(account, notification_type)
    # Check user email preferences
    # This would integrate with your user preferences system
    user = account.users.first
    return false unless user&.email.present?

    # Placeholder logic - in production, check user notification preferences
    case notification_type
    when "new_recommendations"
      true # Default to sending new recommendation emails
    when "implementation_success"
      true # Default to sending success notifications
    when "revenue_milestones"
      true # Default to sending milestone celebrations
    when "quality_alerts"
      true # Default to sending quality alerts
    else
      false # Default to not sending unknown types
    end
  end

  def truncate_error(error_message, length = 100)
    return error_message if error_message.length <= length
    "#{error_message[0...length]}..."
  end
end
