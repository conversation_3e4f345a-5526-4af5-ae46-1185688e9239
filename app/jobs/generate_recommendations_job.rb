class GenerateRecommendationsJob < ApplicationJob
  queue_as :agent_processing

  def perform(account_id)
    account = Account.find(account_id)

    Rails.logger.info "Starting recommendation generation for account #{account_id}"

    generated_count = 0

    # Generate recommendations for each active pipeline
    account.pipelines.active.find_each do |pipeline|
      # Pipeline Intelligence recommendations
      intelligence_service = PipelineIntelligenceService.new(
        pipeline: pipeline,
        account: account
      )

      begin
        intelligence_recommendations = intelligence_service.analyze_pipeline_performance
        if intelligence_recommendations.respond_to?(:count)
          generated_count += intelligence_recommendations.count
          Rails.logger.info "Generated #{intelligence_recommendations.count} intelligence recommendations for pipeline #{pipeline.id}"
        end
      rescue => e
        Rails.logger.error "Failed to generate intelligence recommendations for pipeline #{pipeline.id}: #{e.message}"
      end

      # Data Quality recommendations
      quality_service = DataQualityGuardService.new(
        pipeline: pipeline,
        account: account
      )

      begin
        quality_recommendations = quality_service.analyze_data_quality
        if quality_recommendations.respond_to?(:count)
          generated_count += quality_recommendations.count
          Rails.logger.info "Generated #{quality_recommendations.count} quality recommendations for pipeline #{pipeline.id}"
        end
      rescue => e
        Rails.logger.error "Failed to generate quality recommendations for pipeline #{pipeline.id}: #{e.message}"
      end
    end

    # Template marketplace recommendations
    template_service = TemplateMarketplaceService.new(account: account)

    begin
      template_recommendations = template_service.recommend_templates_for_account(5)
      if template_recommendations.respond_to?(:count)
        generated_count += template_recommendations.count
        Rails.logger.info "Generated #{template_recommendations.count} template recommendations for account #{account_id}"
      end
    rescue => e
      Rails.logger.error "Failed to generate template recommendations for account #{account_id}: #{e.message}"
    end

    # Update account with generation timestamp
    account.update!(
      agent_last_recommendation_at: Time.current
    )

    Rails.logger.info "Completed recommendation generation for account #{account_id}. Total generated: #{generated_count}"

    # Optionally send notification about new recommendations
    if generated_count > 0
      NotificationJob.perform_later(
        account_id: account_id,
        type: "new_recommendations",
        data: { count: generated_count }
      )
    end

    generated_count
  end
end
