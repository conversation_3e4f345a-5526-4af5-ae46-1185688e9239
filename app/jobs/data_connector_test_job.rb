class DataConnectorTestJob < ApplicationJob
  queue_as :default

  # Retry failed connection tests with exponential backoff
  retry_on StandardError, wait: :exponentially_longer, attempts: 3

  def perform(data_connector_id)
    data_connector = DataConnector.find(data_connector_id)

    Rails.logger.info "Testing connection for DataConnector #{data_connector.id} (#{data_connector.display_name})"

    # Test the connection
    success = data_connector.test_connection!

    if success
      Rails.logger.info "Connection test successful for DataConnector #{data_connector.id}"

      # Notify user via ActionCable if they're online
      ActionCable.server.broadcast(
        "user_#{data_connector.created_by.id}",
        {
          type: "connector_test_success",
          connector: {
            id: data_connector.id,
            name: data_connector.name,
            status: data_connector.status,
            test_status: data_connector.test_status
          }
        }
      )
    else
      Rails.logger.warn "Connection test failed for DataConnector #{data_connector.id}: #{data_connector.test_result}"

      # Notify user of failure
      ActionCable.server.broadcast(
        "user_#{data_connector.created_by.id}",
        {
          type: "connector_test_failure",
          connector: {
            id: data_connector.id,
            name: data_connector.name,
            status: data_connector.status,
            test_status: data_connector.test_status,
            error: data_connector.test_result
          }
        }
      )

      # For critical failures, create a system notification
      if data_connector.test_result&.include?("timeout") || data_connector.test_result&.include?("refused")
        create_system_notification(data_connector)
      end
    end

    # Update connector health metrics
    update_connector_health_metrics(data_connector)

  rescue ActiveRecord::RecordNotFound
    Rails.logger.error "DataConnector with ID #{data_connector_id} not found"
  rescue StandardError => e
    Rails.logger.error "Unexpected error testing DataConnector #{data_connector_id}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")

    # Update connector with error status
    if data_connector
      data_connector.update!(
        test_status: :test_failed,
        test_result: "Job error: #{e.message}",
        last_tested_at: Time.current,
        status: :error
      )
    end

    # Re-raise to trigger retry mechanism
    raise e
  end

  private

  def create_system_notification(data_connector)
    # TODO: Implement notification system
    # For now, just log critical issues
    Rails.logger.error "CRITICAL: Connection persistently failing for #{data_connector.display_name} - #{data_connector.test_result}"

    # Could integrate with:
    # - Email notifications
    # - Slack webhooks
    # - PagerDuty alerts
    # - Internal notification system
  end

  def update_connector_health_metrics(data_connector)
    # Record usage metrics for the account
    if data_connector.account.present?
      begin
        UsageTrackingService.new(data_connector.account).record_connector_test(
          data_connector,
          success: data_connector.test_status == "test_passed"
        )
      rescue StandardError => e
        Rails.logger.warn "Failed to record connector test metrics: #{e.message}"
      end
    end
  end
end
