class RevenueTrackingJob < ApplicationJob
  queue_as :analytics

  def perform(account_id)
    account = Account.find(account_id)

    Rails.logger.info "Starting revenue tracking analysis for account #{account_id}"

    begin
      # Calculate current revenue metrics
      current_revenue = calculate_current_revenue(account)
      previous_revenue = calculate_previous_revenue(account)

      # Check for revenue milestones
      check_revenue_milestones(account, current_revenue, previous_revenue)

      # Update account revenue metrics cache
      update_revenue_cache(account, current_revenue)

      # Generate revenue insights
      generate_revenue_insights(account, current_revenue)

      # Schedule next tracking if account is active
      schedule_next_tracking(account) if account.active?

      Rails.logger.info "Completed revenue tracking for account #{account_id}"

    rescue => e
      Rails.logger.error "Failed to track revenue for account #{account_id}: #{e.message}"
      raise e
    end
  end

  private

  def calculate_current_revenue(account)
    {
      total: account.agent_revenues.sum(:amount_cents) / 100.0,
      monthly: account.agent_revenue_this_month,
      weekly: account.agent_revenues.where(created_at: 1.week.ago..).sum(:amount_cents) / 100.0,
      daily: account.agent_revenues.where(created_at: 1.day.ago..).sum(:amount_cents) / 100.0,
      by_source: calculate_revenue_by_source(account),
      trend: calculate_revenue_trend(account)
    }
  end

  def calculate_previous_revenue(account)
    {
      monthly: account.agent_revenues
                      .where(created_at: 2.months.ago..1.month.ago)
                      .sum(:amount_cents) / 100.0,
      weekly: account.agent_revenues
                     .where(created_at: 2.weeks.ago..1.week.ago)
                     .sum(:amount_cents) / 100.0,
      daily: account.agent_revenues
                    .where(created_at: 2.days.ago..1.day.ago)
                    .sum(:amount_cents) / 100.0
    }
  end

  def calculate_revenue_by_source(account)
    account.agent_revenues
           .group(:revenue_source)
           .sum(:amount_cents)
           .transform_values { |cents| cents / 100.0 }
  end

  def calculate_revenue_trend(account)
    # Calculate 7-day moving average trend
    recent_revenue = []

    7.times do |i|
      date = i.days.ago.to_date
      daily_revenue = account.agent_revenues
                             .where(created_at: date.beginning_of_day..date.end_of_day)
                             .sum(:amount_cents) / 100.0
      recent_revenue << daily_revenue
    end

    # Simple trend calculation (positive if recent days > earlier days)
    recent_avg = recent_revenue.first(3).sum / 3.0
    earlier_avg = recent_revenue.last(3).sum / 3.0

    if recent_avg > earlier_avg * 1.1
      "increasing"
    elsif recent_avg < earlier_avg * 0.9
      "decreasing"
    else
      "stable"
    end
  end

  def check_revenue_milestones(account, current_revenue, previous_revenue)
    # Define milestone amounts
    milestones = [ 10, 25, 50, 100, 250, 500, 1000, 2500, 5000 ]

    current_total = current_revenue[:total]
    previous_total = previous_revenue[:monthly] || 0 # Using monthly as approximation

    # Check if we've crossed any milestones
    milestones.each do |milestone|
      if current_total >= milestone && previous_total < milestone
        Rails.logger.info "Revenue milestone reached: $#{milestone} for account #{account.id}"

        # Send milestone notification
        NotificationJob.perform_later(
          account_id: account.id,
          type: "revenue_milestone",
          data: {
            milestone_amount: milestone,
            total_revenue: current_total.round(2)
          }
        )

        # Track milestone achievement
        track_milestone_achievement(account, milestone, current_total)
      end
    end
  end

  def update_revenue_cache(account, current_revenue)
    # Update cached revenue metrics for faster dashboard loading
    cache_key = "agent_revenue_metrics:#{account.id}"
    cache_data = {
      total_revenue: current_revenue[:total],
      monthly_revenue: current_revenue[:monthly],
      weekly_revenue: current_revenue[:weekly],
      daily_revenue: current_revenue[:daily],
      revenue_by_source: current_revenue[:by_source],
      trend: current_revenue[:trend],
      last_updated: Time.current,
      growth_rate: calculate_growth_rate(current_revenue[:monthly], current_revenue)
    }

    Rails.cache.write(cache_key, cache_data, expires_in: 1.hour)
  end

  def calculate_growth_rate(current_monthly, current_revenue)
    # Simple month-over-month growth calculation
    previous_monthly = current_revenue[:total] - current_monthly # Rough approximation
    return 0 if previous_monthly <= 0

    ((current_monthly - previous_monthly) / previous_monthly * 100).round(2)
  end

  def generate_revenue_insights(account, current_revenue)
    insights = []

    # Trend insights
    case current_revenue[:trend]
    when "increasing"
      insights << {
        type: "positive",
        title: "Revenue Growing",
        description: "Your AI agent revenue is trending upward over the past week.",
        priority: "medium"
      }
    when "decreasing"
      insights << {
        type: "warning",
        title: "Revenue Declining",
        description: "Your AI agent revenue has decreased recently. Consider generating new recommendations.",
        priority: "high",
        action: "generate_recommendations"
      }
    end

    # Source diversity insights
    source_count = current_revenue[:by_source].keys.count
    if source_count == 1
      insights << {
        type: "suggestion",
        title: "Diversify Revenue Sources",
        description: "You currently have revenue from only one source. Consider exploring other AI features.",
        priority: "low",
        action: "explore_features"
      }
    end

    # Performance insights
    total_revenue = current_revenue[:total]
    if total_revenue > 100
      insights << {
        type: "celebration",
        title: "Strong Performance",
        description: "Excellent! You've generated over $#{total_revenue.round} in AI agent revenue.",
        priority: "low"
      }
    elsif total_revenue < 5
      insights << {
        type: "encouragement",
        title: "Getting Started",
        description: "Your AI agent is just getting started. Keep implementing recommendations to increase revenue.",
        priority: "medium",
        action: "view_recommendations"
      }
    end

    # Cache insights for dashboard
    insights_cache_key = "agent_revenue_insights:#{account.id}"
    Rails.cache.write(insights_cache_key, insights, expires_in: 4.hours)

    Rails.logger.info "Generated #{insights.count} revenue insights for account #{account.id}"
  end

  def track_milestone_achievement(account, milestone, total_revenue)
    UsageMetric.create!(
      account: account,
      metric_type: "revenue_milestone_achieved",
      value: milestone,
      recorded_at: Time.current,
      metadata: {
        milestone_amount: milestone,
        total_revenue: total_revenue,
        achievement_date: Date.current
      }
    )
  end

  def schedule_next_tracking(account)
    # Schedule next revenue tracking in 24 hours for active accounts
    RevenueTrackingJob.set(wait: 24.hours).perform_later(account.id)
  end
end
