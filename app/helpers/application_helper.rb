module ApplicationHelper
  def nav_link_classes(active = false)
    base_classes = "group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150"
    if active
      "#{base_classes} bg-indigo-50 border-r-2 border-indigo-500 text-indigo-700"
    else
      "#{base_classes} text-gray-700 hover:bg-gray-50 hover:text-gray-900"
    end
  end

  # Role-based UI helpers
  def can_manage_account?(user = current_user)
    user&.can_manage_account?
  end

  def can_manage_team?(user = current_user)
    user&.can_manage_team?
  end

  def can_execute_pipelines?(user = current_user)
    user&.can_execute_pipelines?
  end

  def show_for_roles(*roles, user: current_user)
    return false unless user
    roles.map(&:to_s).include?(user.role)
  end

  # Plan-based feature helpers
  def feature_available?(feature, account = current_account)
    return false unless account&.subscription

    case feature.to_sym
    when :advanced_analytics
      %w[professional enterprise].include?(account.subscription.plan)
    when :team_management
      %w[starter professional enterprise].include?(account.subscription.plan)
    when :api_access
      %w[professional enterprise].include?(account.subscription.plan)
    when :custom_integrations
      account.subscription.plan == "enterprise"
    when :priority_support
      %w[professional enterprise].include?(account.subscription.plan)
    else
      true
    end
  end

  # Tenant customization helpers
  def account_branding_color(account = current_account)
    account&.settings&.dig("branding", "primary_color") || "#4F46E5"
  end

  def account_logo_url(account = current_account)
    account&.settings&.dig("branding", "logo_url") || nil
  end

  def account_custom_domain(account = current_account)
    account&.settings&.dig("domain", "custom_domain") || nil
  end

  # Usage limit helpers
  def usage_percentage(current, limit)
    return 0 if limit.zero? || limit == -1
    [ (current.to_f / limit * 100).round(1), 100 ].min
  end

  def usage_status_class(percentage)
    case percentage
    when 0..70
      "text-green-600 bg-green-100"
    when 71..90
      "text-yellow-600 bg-yellow-100"
    else
      "text-red-600 bg-red-100"
    end
  end

  # Business metrics formatting
  def format_metric_value(value, type = :number)
    case type
    when :currency
      number_to_currency(value)
    when :percentage
      number_to_percentage(value, precision: 1)
    when :bytes
      number_to_human_size(value)
    when :duration
      format_duration(value)
    when :compact
      number_to_human(value, precision: 1)
    else
      number_with_delimiter(value)
    end
  end

  def format_duration(seconds)
    return "0s" if seconds.zero?

    if seconds < 60
      "#{seconds.round}s"
    elsif seconds < 3600
      "#{(seconds / 60).round}m"
    else
      hours = seconds / 3600
      minutes = (seconds % 3600) / 60
      "#{hours.round}h #{minutes.round}m"
    end
  end

  # Status indicators
  def status_badge(status, text = nil)
    text ||= status.to_s.humanize

    case status.to_s.downcase
    when "active", "healthy", "success", "completed"
      content_tag :span, text, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
    when "warning", "pending", "in_progress"
      content_tag :span, text, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
    when "error", "failed", "inactive", "suspended"
      content_tag :span, text, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
    else
      content_tag :span, text, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
    end
  end

  # Pipeline template helpers
  def template_icon_bg_class(template_id)
    case template_id.to_s
    when 'etl'
      'bg-gradient-to-br from-blue-500 to-indigo-600'
    when 'sync'
      'bg-gradient-to-br from-green-500 to-emerald-600'
    when 'stream'
      'bg-gradient-to-br from-purple-500 to-violet-600'
    when 'batch'
      'bg-gradient-to-br from-orange-500 to-amber-600'
    when 'migration'
      'bg-gradient-to-br from-red-500 to-pink-600'
    when 'custom'
      'bg-gradient-to-br from-gray-500 to-slate-600'
    else
      'bg-gradient-to-br from-indigo-500 to-blue-600'
    end
  end

  def render_template_icon(icon_name, template_id)
    icon_class = "h-8 w-8 text-white"

    case icon_name.to_s
    when 'refresh'
      content_tag :svg, class: icon_class, fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" do
        content_tag :path, "", "stroke-linecap": "round", "stroke-linejoin": "round", "stroke-width": "2",
                    d: "M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
      end
    when 'sync'
      content_tag :svg, class: icon_class, fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" do
        content_tag :path, "", "stroke-linecap": "round", "stroke-linejoin": "round", "stroke-width": "2",
                    d: "M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
      end
    when 'lightning'
      content_tag :svg, class: icon_class, fill: "currentColor", viewBox: "0 0 20 20" do
        content_tag :path, "", "fill-rule": "evenodd",
                    d: "M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z",
                    "clip-rule": "evenodd"
      end
    when 'database'
      content_tag :svg, class: icon_class, fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" do
        content_tag :path, "", "stroke-linecap": "round", "stroke-linejoin": "round", "stroke-width": "2",
                    d: "M4 7v10c0 2.21 3.79 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.79 4 8 4s8-1.79 8-4M4 7c0-2.21 3.79-4 8-4s8 1.79 8 4"
      end
    when 'transfer'
      content_tag :svg, class: icon_class, fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" do
        content_tag :path, "", "stroke-linecap": "round", "stroke-linejoin": "round", "stroke-width": "2",
                    d: "M8 9l4-4 4 4m0 6l-4 4-4-4"
      end
    when 'cog'
      content_tag :svg, class: icon_class, fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" do
        content_tag :path, "", "stroke-linecap": "round", "stroke-linejoin": "round", "stroke-width": "2",
                    d: "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
      end
    else
      # Default icon - use a generic pipeline icon
      content_tag :svg, class: icon_class, fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" do
        content_tag :path, "", "stroke-linecap": "round", "stroke-linejoin": "round", "stroke-width": "2",
                    d: "M13 10V3L4 14h7v7l9-11h-7z"
      end
    end
  end

  def template_tag_class(tag)
    base_classes = "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"

    case tag.to_s.downcase
    when 'extract', 'load'
      "#{base_classes} bg-blue-100 text-blue-800"
    when 'transform', 'mapping'
      "#{base_classes} bg-purple-100 text-purple-800"
    when 'real-time', 'continuous'
      "#{base_classes} bg-green-100 text-green-800"
    when 'batch', 'scheduled'
      "#{base_classes} bg-orange-100 text-orange-800"
    when 'bi-directional', 'sync'
      "#{base_classes} bg-indigo-100 text-indigo-800"
    when 'incremental', 'delta'
      "#{base_classes} bg-teal-100 text-teal-800"
    when 'event-driven', 'trigger'
      "#{base_classes} bg-pink-100 text-pink-800"
    when 'scalable', 'high-volume'
      "#{base_classes} bg-emerald-100 text-emerald-800"
    when 'legacy', 'migration'
      "#{base_classes} bg-red-100 text-red-800"
    when 'custom', 'advanced'
      "#{base_classes} bg-gray-100 text-gray-800"
    else
      "#{base_classes} bg-gray-100 text-gray-800"
    end
  end
end
