module DataConnectors
  class PostgresqlService < BaseService
    REQUIRED_FIELDS = %w[host port database username].freeze
    DEFAULT_PORT = 5432
    DEFAULT_TIMEOUT = 30

    def test_connection
      log_connection_attempt("Testing PostgreSQL connection")

      # Validate required configuration
      validation_error = validate_required_config(REQUIRED_FIELDS)
      return validation_error if validation_error

      with_timeout(DEFAULT_TIMEOUT) do
        connection = PG.connect(connection_params)

        # Test with a simple query
        result = connection.exec("SELECT version(), current_database(), current_user")
        version_info = result.first

        connection.close

        success_result(
          "Connected to PostgreSQL #{extract_version(version_info['version'])} " \
          "database '#{version_info['current_database']}' as '#{version_info['current_user']}'"
        )
      end
    end

    def get_schema
      log_connection_attempt("Fetching PostgreSQL schema")

      with_timeout(DEFAULT_TIMEOUT) do
        connection = PG.connect(connection_params)

        # Get all tables with their columns
        schema_query = <<~SQL
          SELECT#{' '}
            t.table_name,
            t.table_type,
            c.column_name,
            c.data_type,
            c.is_nullable,
            c.column_default,
            c.ordinal_position
          FROM information_schema.tables t
          LEFT JOIN information_schema.columns c ON t.table_name = c.table_name
          WHERE t.table_schema = 'public'
            AND t.table_type IN ('BASE TABLE', 'VIEW')
          ORDER BY t.table_name, c.ordinal_position
        SQL

        result = connection.exec(schema_query)
        schema = build_schema_from_results(result)

        connection.close

        {
          success: true,
          schema: schema
        }
      end
    end

    def extract_data(query_config = {})
      log_connection_attempt("Extracting data from PostgreSQL")

      # Build query from config
      query = build_query(query_config)
      return error_result("Invalid query configuration") unless query

      with_timeout(query_config["timeout"] || DEFAULT_TIMEOUT) do
        connection = PG.connect(connection_params)

        result = connection.exec(query)

        # Convert result to array of hashes
        data = result.map { |row| row.to_h }

        connection.close

        {
          success: true,
          data: data,
          row_count: data.size,
          columns: result.fields
        }
      end
    end

    def load_data(data, load_config = {})
      log_connection_attempt("Loading data to PostgreSQL")

      table_name = load_config["table_name"]
      return error_result("Table name is required") unless table_name

      return error_result("No data provided") if data.empty?

      with_timeout(load_config["timeout"] || DEFAULT_TIMEOUT) do
        connection = PG.connect(connection_params)

        # Begin transaction
        connection.transaction do
          case load_config["mode"] || "insert"
          when "insert"
            insert_data(connection, table_name, data)
          when "upsert"
            upsert_data(connection, table_name, data, load_config["upsert_key"])
          when "truncate_insert"
            connection.exec("TRUNCATE TABLE #{connection.escape_identifier(table_name)}")
            insert_data(connection, table_name, data)
          else
            raise "Unsupported load mode: #{load_config['mode']}"
          end
        end

        connection.close

        {
          success: true,
          rows_loaded: data.size,
          message: "Successfully loaded #{data.size} rows to #{table_name}"
        }
      end
    end

    private

    def connection_params
      {
        host: config["host"],
        port: (config["port"] || DEFAULT_PORT).to_i,
        dbname: config["database"],
        user: config["username"],
        password: config["password"],
        sslmode: config["sslmode"] || "prefer",
        connect_timeout: DEFAULT_TIMEOUT
      }
    end

    def extract_version(version_string)
      # Extract version number from PostgreSQL version string
      # e.g., "PostgreSQL 14.9 on x86_64-pc-linux-gnu" -> "14.9"
      version_string.match(/PostgreSQL (\d+\.\d+)/)[1] rescue "Unknown"
    end

    def build_schema_from_results(result)
      tables = {}

      result.each do |row|
        table_name = row["table_name"]

        tables[table_name] ||= {
          type: row["table_type"],
          columns: []
        }

        if row["column_name"]
          tables[table_name][:columns] << {
            name: row["column_name"],
            type: row["data_type"],
            nullable: row["is_nullable"] == "YES",
            default: row["column_default"],
            position: row["ordinal_position"].to_i
          }
        end
      end

      tables
    end

    def build_query(query_config)
      if query_config["raw_sql"]
        query_config["raw_sql"]
      elsif query_config["table_name"]
        build_select_query(query_config)
      else
        nil
      end
    end

    def build_select_query(config)
      table = config["table_name"]
      columns = config["columns"] || "*"
      where_clause = config["where"] ? "WHERE #{config['where']}" : ""
      limit_clause = config["limit"] ? "LIMIT #{config['limit'].to_i}" : ""
      order_clause = config["order"] ? "ORDER BY #{config['order']}" : ""

      "SELECT #{columns} FROM #{table} #{where_clause} #{order_clause} #{limit_clause}".strip
    end

    def insert_data(connection, table_name, data)
      return if data.empty?

      columns = data.first.keys
      escaped_table = connection.escape_identifier(table_name)
      escaped_columns = columns.map { |col| connection.escape_identifier(col) }.join(", ")

      data.each do |row|
        values = columns.map { |col| connection.escape_literal(row[col]) }.join(", ")
        query = "INSERT INTO #{escaped_table} (#{escaped_columns}) VALUES (#{values})"
        connection.exec(query)
      end
    end

    def upsert_data(connection, table_name, data, upsert_key)
      return if data.empty?

      columns = data.first.keys
      escaped_table = connection.escape_identifier(table_name)
      escaped_columns = columns.map { |col| connection.escape_identifier(col) }.join(", ")

      data.each do |row|
        values = columns.map { |col| connection.escape_literal(row[col]) }.join(", ")

        # Use ON CONFLICT for upsert
        update_clause = columns.reject { |col| col == upsert_key }
                              .map { |col| "#{connection.escape_identifier(col)} = EXCLUDED.#{connection.escape_identifier(col)}" }
                              .join(", ")

        query = <<~SQL
          INSERT INTO #{escaped_table} (#{escaped_columns})#{' '}
          VALUES (#{values})
          ON CONFLICT (#{connection.escape_identifier(upsert_key)})#{' '}
          DO UPDATE SET #{update_clause}
        SQL

        connection.exec(query)
      end
    end
  end
end
