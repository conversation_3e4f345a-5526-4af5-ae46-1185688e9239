module DataConnectors
  class BaseService
    attr_reader :data_connector, :config

    def initialize(data_connector)
      @data_connector = data_connector
      @config = data_connector.connection_config || {}
    end

    # Override in subclasses to implement specific connection testing
    def test_connection
      raise NotImplementedError, "Subclasses must implement test_connection method"
    end

    # Override in subclasses to implement data extraction
    def extract_data(query_config = {})
      raise NotImplementedError, "Subclasses must implement extract_data method"
    end

    # Override in subclasses to implement data loading
    def load_data(data, load_config = {})
      raise NotImplementedError, "Subclasses must implement load_data method"
    end

    # Override in subclasses to get schema information
    def get_schema
      raise NotImplementedError, "Subclasses must implement get_schema method"
    end

    protected

    # Common validation helpers
    def validate_required_config(required_fields)
      missing_fields = required_fields.select { |field| config[field].blank? }

      if missing_fields.any?
        return {
          success: false,
          error: "Missing required configuration: #{missing_fields.join(', ')}"
        }
      end

      nil
    end

    # Common timeout wrapper
    def with_timeout(seconds = 30)
      Timeout.timeout(seconds) do
        yield
      end
    rescue Timeout::Error
      {
        success: false,
        error: "Connection timed out after #{seconds} seconds"
      }
    rescue StandardError => e
      {
        success: false,
        error: "Connection failed: #{e.message}"
      }
    end

    # Common connection result formatting
    def success_result(message = "Connection successful")
      {
        success: true,
        message: message
      }
    end

    def error_result(error_message)
      {
        success: false,
        error: error_message
      }
    end

    # Logging helper
    def log_connection_attempt(action)
      Rails.logger.info "[#{data_connector.connector_type.upcase}] #{action} for connector #{data_connector.id} (#{data_connector.name})"
    end
  end
end
