class TemplateMarketplaceService
  include ActiveModel::Model
  include ActiveModel::Attributes

  attr_accessor :account, :pipeline

  # Marketplace analytics and recommendation thresholds
  PERFORMANCE_THRESHOLDS = {
    minimum_executions: 10,
    success_rate: 0.85,
    performance_percentile: 0.75,
    user_rating: 3.5
  }.freeze

  # Template categories and their market demand multipliers
  CATEGORY_DEMAND = {
    "e_commerce" => 1.5,
    "marketing" => 1.3,
    "finance" => 1.2,
    "analytics" => 1.4,
    "operations" => 1.1,
    "customer_service" => 1.0
  }.freeze

  def initialize(attributes = {})
    super
    @template_cache = {}
    @recommendation_cache = {}
  end

  # Analyze successful pipelines and generate templates
  def analyze_and_create_templates
    successful_pipelines = identify_successful_pipelines
    templates_created = 0

    successful_pipelines.each do |pipeline|
      next if template_already_exists?(pipeline)

      template = create_template_from_pipeline(pipeline)
      if template&.persisted?
        templates_created += 1
        track_template_creation_revenue(template)
      end
    end

    {
      analyzed_pipelines: successful_pipelines.count,
      templates_created: templates_created,
      total_templates: PipelineTemplate.published.count
    }
  end

  # Recommend templates to accounts based on their pipeline patterns
  def recommend_templates_for_account(limit = 10)
    return [] unless account

    # Get account's pipeline patterns
    account_patterns = analyze_account_patterns

    # Find matching templates
    recommended_templates = find_matching_templates(account_patterns)

    # Score and rank templates
    scored_templates = score_template_relevance(recommended_templates, account_patterns)

    # Return top recommendations
    top_templates = scored_templates.first(limit)

    # Create recommendations in database
    create_template_recommendations(top_templates)

    top_templates
  end

  # Process template purchase and handle revenue distribution
  def process_template_purchase(template, purchasing_account)
    return { success: false, error: "Invalid template" } unless template&.published?
    return { success: false, error: "Cannot purchase own template" } if template.creator_account_id == purchasing_account.id

    begin
      ActiveRecord::Base.transaction do
        # Create pipeline from template
        new_pipeline = create_pipeline_from_template(template, purchasing_account)

        # Handle revenue distribution if paid template
        if template.price_cents > 0
          distribute_template_revenue(template, purchasing_account)
        end

        # Update template metrics
        template.increment!(:purchases_count)

        # Track successful purchase
        track_purchase_metrics(template, purchasing_account, new_pipeline)

        {
          success: true,
          pipeline: new_pipeline,
          template: template,
          message: "Template '#{template.name}' successfully purchased and pipeline created"
        }
      end
    rescue StandardError => e
      Rails.logger.error "Template purchase failed: #{e.message}"
      { success: false, error: e.message }
    end
  end

  # Analyze template marketplace performance
  def analyze_marketplace_performance(period = 30.days)
    templates = PipelineTemplate.published.includes(:agent_revenues)

    performance_data = {
      period: { start: period.ago, end: Time.current },
      total_templates: templates.count,
      total_purchases: templates.sum(:purchases_count),
      total_revenue: 0,
      platform_revenue: 0,
      creator_revenue: 0,
      top_performing_templates: [],
      category_performance: {},
      trending_templates: [],
      revenue_trends: {}
    }

    # Calculate revenue metrics
    template_revenues = AgentRevenue.where(
      revenue_source: :template_sale,
      created_at: period.ago..Time.current
    )

    total_revenue = template_revenues.sum(:amount_cents) / 100.0
    performance_data[:total_revenue] = total_revenue
    performance_data[:platform_revenue] = total_revenue * 0.7 # Platform takes 70%
    performance_data[:creator_revenue] = total_revenue * 0.3  # Creators get 30%

    # Top performing templates
    performance_data[:top_performing_templates] = templates
      .joins(:agent_revenues)
      .group("pipeline_templates.id")
      .order("SUM(agent_revenues.amount_cents) DESC")
      .limit(10)
      .pluck("pipeline_templates.name", "SUM(agent_revenues.amount_cents)")
      .map { |name, revenue| { name: name, revenue: revenue / 100.0 } }

    # Category performance analysis
    PipelineTemplate.categories.each do |category, _|
      category_templates = templates.where(category: category)
      performance_data[:category_performance][category] = {
        template_count: category_templates.count,
        total_purchases: category_templates.sum(:purchases_count),
        average_rating: category_templates.average(:average_rating)&.round(2) || 0,
        total_revenue: category_templates.joins(:agent_revenues)
                                      .where(agent_revenues: { created_at: period.ago..Time.current })
                                      .sum("agent_revenues.amount_cents") / 100.0
      }
    end

    # Trending templates (high recent purchase velocity)
    performance_data[:trending_templates] = identify_trending_templates(period)

    performance_data
  end

  # Get personalized template recommendations
  def get_personalized_recommendations(account, options = {})
    limit = options[:limit] || 5
    category = options[:category]
    industry = options[:industry]

    # Build base scope
    scope = PipelineTemplate.published.includes(:creator_account)
    scope = scope.where(category: category) if category
    scope = scope.where(industry: industry) if industry

    # Exclude templates from the same account
    scope = scope.where.not(creator_account_id: account.id)

    # Get account's integration patterns
    account_integrations = get_account_integration_patterns(account)

    # Score templates based on relevance
    recommendations = scope.map do |template|
      relevance_score = calculate_template_relevance(template, account, account_integrations)
      {
        template: template,
        relevance_score: relevance_score,
        reasons: generate_recommendation_reasons(template, account, account_integrations)
      }
    end

    # Sort by relevance and return top results
    recommendations.sort_by { |r| -r[:relevance_score] }.first(limit)
  end

  # Track template usage and performance
  def track_template_performance(template, pipeline)
    return unless template && pipeline

    # Get pipeline performance metrics
    executions = pipeline.pipeline_executions.recent.limit(20)
    return if executions.empty?

    performance_metrics = {
      total_executions: executions.count,
      success_rate: calculate_success_rate(executions),
      avg_execution_time: executions.average(:execution_time)&.round(2) || 0,
      avg_records_processed: executions.average(:records_processed)&.round(0) || 0,
      last_updated: Time.current
    }

    # Update template performance metrics
    current_metrics = template.performance_metrics || {}
    updated_metrics = merge_performance_metrics(current_metrics, performance_metrics)

    template.update!(performance_metrics: updated_metrics)

    # Create usage tracking record
    track_template_usage(template, pipeline, performance_metrics)

    performance_metrics
  end

  private

  def identify_successful_pipelines
    Pipeline.joins(:pipeline_executions)
           .where(
             pipeline_executions: {
               status: :success,
               created_at: 30.days.ago..Time.current
             }
           )
           .group("pipelines.id")
           .having("COUNT(pipeline_executions.id) >= ?", PERFORMANCE_THRESHOLDS[:minimum_executions])
           .having("(COUNT(CASE WHEN pipeline_executions.status = 0 THEN 1 END)::float / COUNT(*)) >= ?", PERFORMANCE_THRESHOLDS[:success_rate])
           .includes(:pipeline_executions, :account)
  end

  def template_already_exists?(pipeline)
    PipelineTemplate.exists?(
      source_pipeline_id: pipeline.id,
      source_type: pipeline.source_config["type"],
      destination_type: pipeline.destination_config["type"]
    )
  end

  def create_template_from_pipeline(pipeline)
    template_data = extract_template_data(pipeline)

    PipelineTemplate.create!(
      creator_account: pipeline.account,
      source_pipeline: pipeline,
      name: template_data[:name],
      description: template_data[:description],
      category: infer_category(pipeline),
      industry: infer_industry(pipeline),
      source_type: pipeline.source_config["type"],
      destination_type: pipeline.destination_config["type"],
      source_config_template: anonymize_config(pipeline.source_config),
      destination_config_template: anonymize_config(pipeline.destination_config),
      transformation_template: pipeline.transformation_rules,
      schedule_template: extract_schedule_template(pipeline),
      price_cents: calculate_suggested_price(pipeline),
      performance_metrics: calculate_initial_performance_metrics(pipeline),
      use_cases: generate_use_cases(pipeline),
      requirements: generate_requirements(pipeline),
      status: :published # Auto-publish high-quality templates
    )
  rescue StandardError => e
    Rails.logger.error "Failed to create template from pipeline #{pipeline.id}: #{e.message}"
    nil
  end

  def extract_template_data(pipeline)
    {
      name: generate_template_name(pipeline),
      description: generate_template_description(pipeline)
    }
  end

  def generate_template_name(pipeline)
    source_type = pipeline.source_config["type"]&.humanize || "Data"
    dest_type = pipeline.destination_config["type"]&.humanize || "Storage"
    category = infer_category(pipeline)&.humanize || "Integration"

    "#{source_type} to #{dest_type} #{category} Pipeline"
  end

  def generate_template_description(pipeline)
    source_type = pipeline.source_config["type"]&.humanize || "data source"
    dest_type = pipeline.destination_config["type"]&.humanize || "destination"

    performance = calculate_initial_performance_metrics(pipeline)
    success_rate = (performance["success_rate"] || 0).round(1)

    description = "Proven #{source_type} to #{dest_type} integration template"
    description += " with #{success_rate}% success rate" if success_rate > 0
    description += " and automated data transformations."

    if pipeline.transformation_rules.present?
      transformation_count = pipeline.transformation_rules.keys.count
      description += " Includes #{transformation_count} pre-configured transformation rules."
    end

    description += " Perfect for teams looking to replicate successful data pipeline patterns."

    description
  end

  def infer_category(pipeline)
    # Analyze pipeline characteristics to infer category
    source_type = pipeline.source_config["type"]&.downcase
    dest_type = pipeline.destination_config["type"]&.downcase
    name = pipeline.name&.downcase || ""
    description = pipeline.description&.downcase || ""

    # Category inference rules
    return :e_commerce if [ source_type, dest_type, name, description ].any? { |s| s&.match?(/shopify|magento|woocommerce|ecommerce|order|product|customer/) }
    return :marketing if [ source_type, dest_type, name, description ].any? { |s| s&.match?(/mailchimp|hubspot|salesforce|marketing|campaign|lead/) }
    return :finance if [ source_type, dest_type, name, description ].any? { |s| s&.match?(/stripe|paypal|quickbooks|accounting|financial|payment|invoice/) }
    return :analytics if [ source_type, dest_type, name, description ].any? { |s| s&.match?(/google_analytics|mixpanel|amplitude|analytics|reporting|dashboard/) }
    return :customer_service if [ source_type, dest_type, name, description ].any? { |s| s&.match?(/zendesk|intercom|freshdesk|support|ticket|helpdesk/) }

    :operations # Default category
  end

  def infer_industry(pipeline)
    # Analyze account and pipeline data to infer industry
    account_name = pipeline.account.name&.downcase || ""
    pipeline_name = pipeline.name&.downcase || ""

    return :retail if [ account_name, pipeline_name ].any? { |s| s&.match?(/retail|shop|store|ecommerce/) }
    return :saas if [ account_name, pipeline_name ].any? { |s| s&.match?(/saas|software|app|platform/) }
    return :healthcare if [ account_name, pipeline_name ].any? { |s| s&.match?(/health|medical|patient|clinic/) }
    return :finance_services if [ account_name, pipeline_name ].any? { |s| s&.match?(/bank|financial|fintech|payment/) }
    return :education if [ account_name, pipeline_name ].any? { |s| s&.match?(/school|university|education|learning/) }

    :technology # Default industry
  end

  def anonymize_config(config)
    safe_config = config.deep_dup

    # Remove sensitive fields
    sensitive_fields = %w[password api_key secret token connection_string private_key access_token refresh_token]
    sensitive_fields.each { |field| safe_config.delete(field) }

    # Replace specific values with placeholders
    field_mappings = {
      "host" => "{{HOST}}",
      "hostname" => "{{HOSTNAME}}",
      "server" => "{{SERVER}}",
      "database" => "{{DATABASE}}",
      "username" => "{{USERNAME}}",
      "user" => "{{USER}}",
      "bucket" => "{{BUCKET_NAME}}",
      "endpoint" => "{{ENDPOINT_URL}}",
      "region" => "{{AWS_REGION}}",
      "project_id" => "{{PROJECT_ID}}",
      "workspace_id" => "{{WORKSPACE_ID}}"
    }

    field_mappings.each do |field, placeholder|
      safe_config[field] = placeholder if safe_config[field]
    end

    safe_config
  end

  def extract_schedule_template(pipeline)
    {
      "type" => pipeline.schedule_type,
      "config" => pipeline.schedule_config.except("webhook_url"),
      "frequency" => infer_frequency_description(pipeline)
    }
  end

  def infer_frequency_description(pipeline)
    case pipeline.schedule_type
    when "real_time" then "Real-time processing"
    when "hourly" then "Every hour"
    when "daily" then "Once daily"
    when "weekly" then "Weekly"
    else "Manual trigger"
    end
  end

  def calculate_suggested_price(pipeline)
    # Base pricing on complexity and demand
    complexity_score = calculate_complexity_score(pipeline)
    category_multiplier = CATEGORY_DEMAND[infer_category(pipeline).to_s] || 1.0

    base_price = case complexity_score
    when 0..3 then 5.00   # Simple pipelines
    when 4..7 then 10.00  # Moderate complexity
    when 8..10 then 20.00 # Complex pipelines
    else 15.00            # Default
    end

    suggested_price = (base_price * category_multiplier).round(2)
    (suggested_price * 100).to_i # Convert to cents
  end

  def calculate_complexity_score(pipeline)
    score = 0

    # Source/destination complexity
    score += 1 if pipeline.source_config.keys.count > 3
    score += 1 if pipeline.destination_config.keys.count > 3

    # Transformation complexity
    score += pipeline.transformation_rules.keys.count if pipeline.transformation_rules.present?

    # Schedule complexity
    score += 1 if pipeline.schedule_type != "manual"
    score += 1 if pipeline.schedule_config.present? && pipeline.schedule_config.keys.count > 2

    # Performance indicators
    if pipeline.pipeline_executions.any?
      avg_time = pipeline.pipeline_executions.average(:execution_time) || 0
      score += 1 if avg_time > 60  # Complex if takes > 1 minute
      score += 1 if avg_time > 300 # Very complex if > 5 minutes
    end

    [ score, 10 ].min # Cap at 10
  end

  def calculate_initial_performance_metrics(pipeline)
    executions = pipeline.pipeline_executions.recent.limit(50)

    return {} if executions.empty?

    {
      "total_runs" => executions.count,
      "success_rate" => calculate_success_rate(executions),
      "avg_execution_time" => executions.average(:execution_time)&.round(2) || 0,
      "avg_records_processed" => executions.average(:records_processed)&.round(0) || 0,
      "error_rate" => calculate_error_rate(executions),
      "last_updated" => Time.current
    }
  end

  def calculate_success_rate(executions)
    return 0 if executions.empty?

    successful_count = executions.where(status: :success).count
    (successful_count.to_f / executions.count * 100).round(1)
  end

  def calculate_error_rate(executions)
    return 0 if executions.empty?

    failed_count = executions.where.not(status: :success).count
    (failed_count.to_f / executions.count * 100).round(1)
  end

  def generate_use_cases(pipeline)
    use_cases = []

    source_type = pipeline.source_config["type"]
    dest_type = pipeline.destination_config["type"]
    category = infer_category(pipeline)

    case category
    when :e_commerce
      use_cases = [
        "Sync product data from #{source_type} to #{dest_type}",
        "Track order fulfillment and inventory updates",
        "Generate sales reports and analytics"
      ]
    when :marketing
      use_cases = [
        "Sync customer data for marketing campaigns",
        "Track campaign performance metrics",
        "Generate lead scoring and nurture workflows"
      ]
    when :analytics
      use_cases = [
        "Aggregate data from multiple sources for reporting",
        "Create real-time dashboards and KPI tracking",
        "Generate automated business intelligence reports"
      ]
    else
      use_cases = [
        "Automate data synchronization between systems",
        "Ensure data consistency and quality",
        "Reduce manual data entry and processing time"
      ]
    end

    use_cases
  end

  def generate_requirements(pipeline)
    requirements = []

    # Source requirements
    source_type = pipeline.source_config["type"]
    requirements << "Access to #{source_type} with appropriate permissions"

    # Destination requirements
    dest_type = pipeline.destination_config["type"]
    requirements << "#{dest_type} destination with write permissions"

    # Transformation requirements
    if pipeline.transformation_rules.present?
      requirements << "Understanding of data transformation requirements"
    end

    # Schedule requirements
    unless pipeline.schedule_type == "manual"
      requirements << "Automated scheduling capabilities"
    end

    requirements << "Basic understanding of data pipeline concepts"

    requirements
  end

  def track_template_creation_revenue(template)
    # Track potential revenue from template creation
    # This helps measure the value of our template generation system
    UsageMetric.create!(
      account: template.creator_account,
      metric_type: "template_created",
      value: 1,
      recorded_at: Time.current,
      metadata: {
        template_id: template.id,
        template_name: template.name,
        category: template.category,
        suggested_price: template.price
      }
    )
  end

  def analyze_account_patterns(account_param = nil)
    target_account = account_param || account
    return {} unless target_account

    pipelines = target_account.pipelines.includes(:pipeline_executions)

    {
      common_sources: extract_common_sources(pipelines),
      common_destinations: extract_common_destinations(pipelines),
      preferred_schedule: extract_preferred_schedule(pipelines),
      complexity_preference: calculate_complexity_preference(pipelines),
      industry_focus: infer_account_industry(target_account),
      success_patterns: analyze_success_patterns(pipelines)
    }
  end

  def extract_common_sources(pipelines)
    pipelines.pluck(:source_config)
             .map { |config| config["type"] }
             .compact
             .tally
             .sort_by { |_, count| -count }
             .first(3)
             .to_h
  end

  def extract_common_destinations(pipelines)
    pipelines.pluck(:destination_config)
             .map { |config| config["type"] }
             .compact
             .tally
             .sort_by { |_, count| -count }
             .first(3)
             .to_h
  end

  def extract_preferred_schedule(pipelines)
    pipelines.group(:schedule_type).count.max_by { |_, count| count }&.first || "manual"
  end

  def calculate_complexity_preference(pipelines)
    return "medium" if pipelines.empty?

    complexity_scores = pipelines.map { |p| calculate_complexity_score(p) }
    avg_complexity = complexity_scores.sum / complexity_scores.count.to_f

    case avg_complexity
    when 0..3 then "low"
    when 4..7 then "medium"
    else "high"
    end
  end

  def infer_account_industry(target_account)
    account_name = target_account.name&.downcase || ""

    # Industry inference based on account name and pipeline patterns
    return "retail" if account_name.match?(/retail|shop|store|commerce/)
    return "saas" if account_name.match?(/saas|software|tech/)
    return "healthcare" if account_name.match?(/health|medical|clinic/)
    return "finance" if account_name.match?(/bank|financial|fintech/)

    "technology" # Default
  end

  def analyze_success_patterns(pipelines)
    successful_pipelines = pipelines.joins(:pipeline_executions)
                                  .where(pipeline_executions: { status: :success })
                                  .distinct

    return {} if successful_pipelines.empty?

    {
      preferred_transformations: extract_common_transformations(successful_pipelines),
      optimal_schedules: extract_successful_schedules(successful_pipelines),
      best_integration_pairs: extract_successful_integrations(successful_pipelines)
    }
  end

  def extract_common_transformations(pipelines)
    transformations = pipelines.pluck(:transformation_rules)
                              .reject(&:blank?)
                              .flat_map(&:keys)

    transformations.tally.sort_by { |_, count| -count }.first(5).to_h
  end

  def extract_successful_schedules(pipelines)
    pipelines.group(:schedule_type).count
  end

  def extract_successful_integrations(pipelines)
    pipelines.map do |p|
      "#{p.source_config['type']}_to_#{p.destination_config['type']}"
    end.tally.sort_by { |_, count| -count }.first(3).to_h
  end

  def find_matching_templates(account_patterns)
    templates = PipelineTemplate.published.includes(:creator_account)

    # Filter by account's common sources/destinations
    if account_patterns[:common_sources].any?
      source_types = account_patterns[:common_sources].keys
      templates = templates.where(source_type: source_types)
    end

    if account_patterns[:common_destinations].any?
      dest_types = account_patterns[:common_destinations].keys
      templates = templates.where(destination_type: dest_types)
    end

    # Filter by complexity preference
    complexity_pref = account_patterns[:complexity_preference]
    templates = filter_by_complexity(templates, complexity_pref)

    templates
  end

  def filter_by_complexity(templates, complexity_pref)
    case complexity_pref
    when "low"
      templates.where(price_cents: 0..1000) # Simpler templates tend to be cheaper
    when "high"
      templates.where(price_cents: 1500..) # More complex templates are pricier
    else
      templates # Medium complexity - no filter
    end
  end

  def score_template_relevance(templates, account_patterns)
    templates.map do |template|
      score = 0

      # Source/destination match bonus
      score += 3 if account_patterns[:common_sources]&.key?(template.source_type)
      score += 3 if account_patterns[:common_destinations]&.key?(template.destination_type)

      # Category/industry match
      account_industry = account_patterns[:industry_focus]
      score += 2 if template.industry.to_s == account_industry

      # Performance bonus
      score += 1 if template.average_rating && template.average_rating >= 4.0
      score += 1 if template.purchases_count > 10

      # Popularity bonus
      score += 1 if template.featured?

      {
        template: template,
        relevance_score: score,
        match_reasons: generate_match_reasons(template, account_patterns)
      }
    end
  end

  def generate_match_reasons(template, account_patterns)
    reasons = []

    if account_patterns[:common_sources]&.key?(template.source_type)
      reasons << "Matches your #{template.source_type} data source"
    end

    if account_patterns[:common_destinations]&.key?(template.destination_type)
      reasons << "Integrates with your #{template.destination_type} destination"
    end

    if template.average_rating && template.average_rating >= 4.0
      reasons << "Highly rated (#{template.average_rating}/5.0)"
    end

    if template.purchases_count > 20
      reasons << "Popular choice (#{template.purchases_count} purchases)"
    end

    if template.performance_metrics["success_rate"].to_f > 90
      reasons << "#{template.performance_metrics['success_rate']}% success rate"
    end

    reasons
  end

  def create_template_recommendations(scored_templates)
    scored_templates.each do |scored_template|
      next unless account

      AgentRecommendation.create!(
        account: account,
        recommendation_type: :template,
        title: "Recommended Template: #{scored_template[:template].name}",
        description: "#{scored_template[:template].description}\n\nReasons: #{scored_template[:match_reasons].join(', ')}",
        estimated_value: calculate_template_value(scored_template[:template]),
        confidence_score: [ scored_template[:relevance_score] * 10, 100 ].min,
        priority: scored_template[:relevance_score] >= 5 ? :high : :medium,
        implementation_steps: {
          "estimated_hours" => 0.5,
          "steps" => [
            "Review template details and requirements",
            "Purchase template from marketplace",
            "Configure template with your credentials",
            "Test pipeline with sample data",
            "Deploy to production"
          ]
        },
        ai_analysis: {
          "template_id" => scored_template[:template].id,
          "relevance_score" => scored_template[:relevance_score],
          "match_reasons" => scored_template[:match_reasons],
          "generated_at" => Time.current
        }
      )
    end
  end

  def calculate_template_value(template)
    # Estimate monthly value based on time savings and efficiency
    base_value = 50.0 # Base monthly value

    # Adjust based on template complexity and performance
    if template.performance_metrics["success_rate"].to_f > 90
      base_value *= 1.2 # 20% bonus for high reliability
    end

    if template.purchases_count > 50
      base_value *= 1.1 # 10% bonus for proven popularity
    end

    # Adjust based on price (assuming ROI of 5x template cost per month)
    template_cost_value = (template.price || 0) * 5

    [ base_value + template_cost_value, 200 ].min # Cap at $200/month estimated value
  end

  def create_pipeline_from_template(template, purchasing_account)
    # Use the PipelineTemplate's built-in method
    template.create_pipeline_for(purchasing_account)
  end

  def distribute_template_revenue(template, purchasing_account)
    platform_share = template.price * 0.7
    creator_share = template.price * 0.3

    # Platform revenue
    AgentRevenue.create!(
      account: Account.find_by(name: "Platform") || Account.first, # Fallback to first account for demo
      revenue_source: :template_sale,
      amount_cents: (platform_share * 100).to_i,
      description: "Platform commission: #{template.name}",
      performance_metrics: {
        "template_id" => template.id,
        "buyer_account_id" => purchasing_account.id,
        "commission_rate" => 0.7
      }
    )

    # Creator revenue (if template has a creator)
    if template.creator_account_id.present?
      AgentRevenue.create!(
        account_id: template.creator_account_id,
        revenue_source: :template_sale,
        amount_cents: (creator_share * 100).to_i,
        description: "Template sale: #{template.name}",
        performance_metrics: {
          "template_id" => template.id,
          "buyer_account_id" => purchasing_account.id,
          "royalty_rate" => 0.3
        }
      )
    end
  end

  def track_purchase_metrics(template, purchasing_account, new_pipeline)
    # Track successful purchase for analytics
    UsageMetric.create!(
      account: purchasing_account,
      metric_type: "template_purchase",
      value: template.price,
      recorded_at: Time.current,
      metadata: {
        template_id: template.id,
        template_name: template.name,
        template_category: template.category,
        pipeline_id: new_pipeline.id,
        purchase_type: template.price_cents > 0 ? "paid" : "free"
      }
    )
  end

  def identify_trending_templates(period)
    # Templates with high purchase velocity in the recent period
    PipelineTemplate.published
                   .joins(:agent_revenues)
                   .where(agent_revenues: {
                     revenue_source: :template_sale,
                     created_at: period.ago..Time.current
                   })
                   .group("pipeline_templates.id")
                   .order("COUNT(agent_revenues.id) DESC")
                   .limit(5)
                   .pluck("pipeline_templates.name", "COUNT(agent_revenues.id)")
                   .map { |name, purchases| { name: name, recent_purchases: purchases } }
  end

  def get_account_integration_patterns(target_account)
    # Get the most common integration patterns for this account
    pipelines = target_account.pipelines.includes(:pipeline_executions)

    integration_patterns = pipelines.map do |pipeline|
      {
        source_type: pipeline.source_config["type"],
        destination_type: pipeline.destination_config["type"],
        success_rate: calculate_pipeline_success_rate(pipeline),
        complexity: calculate_complexity_score(pipeline)
      }
    end

    # Group by integration type and calculate average performance
    grouped_patterns = integration_patterns.group_by do |pattern|
      "#{pattern[:source_type]}_to_#{pattern[:destination_type]}"
    end

    grouped_patterns.transform_values do |patterns|
      {
        count: patterns.count,
        avg_success_rate: patterns.sum { |p| p[:success_rate] } / patterns.count.to_f,
        avg_complexity: patterns.sum { |p| p[:complexity] } / patterns.count.to_f
      }
    end
  end

  def calculate_pipeline_success_rate(pipeline)
    executions = pipeline.pipeline_executions.recent.limit(20)
    return 0 if executions.empty?

    successful = executions.where(status: :success).count
    (successful.to_f / executions.count * 100).round(1)
  end

  def calculate_template_relevance(template, target_account, account_integrations)
    score = 0

    # Integration match
    template_pattern = "#{template.source_type}_to_#{template.destination_type}"
    if account_integrations[template_pattern]
      score += 5
      # Bonus for successful integration history
      if account_integrations[template_pattern][:avg_success_rate] > 80
        score += 2
      end
    end

    # Industry match
    account_industry = infer_account_industry(target_account)
    score += 3 if template.industry.to_s == account_industry

    # Category relevance based on account's pipeline categories
    account_categories = target_account.pipelines.map { |p| infer_category(p) }.tally
    if account_categories[template.category]
      score += 2
    end

    # Template quality indicators
    score += 1 if template.average_rating && template.average_rating >= 4.0
    score += 1 if template.purchases_count >= 10
    score += 1 if template.featured?

    # Performance indicators
    if template.performance_metrics["success_rate"].to_f >= 90
      score += 2
    end

    score
  end

  def generate_recommendation_reasons(template, target_account, account_integrations)
    reasons = []

    # Integration compatibility
    template_pattern = "#{template.source_type}_to_#{template.destination_type}"
    if account_integrations[template_pattern]
      reasons << "You have experience with #{template.source_type} → #{template.destination_type} integrations"
    end

    # Quality indicators
    if template.average_rating && template.average_rating >= 4.0
      reasons << "Highly rated template (#{template.average_rating}/5.0 stars)"
    end

    if template.purchases_count >= 20
      reasons << "Popular choice with #{template.purchases_count} successful purchases"
    end

    # Performance indicators
    success_rate = template.performance_metrics["success_rate"].to_f
    if success_rate >= 95
      reasons << "Excellent reliability with #{success_rate}% success rate"
    end

    # Industry relevance
    account_industry = infer_account_industry(target_account)
    if template.industry.to_s == account_industry
      reasons << "Designed for #{account_industry} industry use cases"
    end

    # Cost effectiveness
    if template.price_cents == 0
      reasons << "Free template with immediate value"
    elsif template.price <= 10
      reasons << "Cost-effective solution under $10"
    end

    reasons
  end

  def merge_performance_metrics(current_metrics, new_metrics)
    merged = current_metrics.dup

    # Update or add new metrics
    new_metrics.each do |key, value|
      if key == "total_executions" && current_metrics[key]
        merged[key] = current_metrics[key] + value
      else
        merged[key] = value
      end
    end

    merged
  end

  def track_template_usage(template, pipeline, performance_metrics)
    UsageMetric.create!(
      account: pipeline.account,
      metric_type: "template_performance",
      value: performance_metrics[:success_rate] || 0,
      recorded_at: Time.current,
      metadata: {
        template_id: template.id,
        pipeline_id: pipeline.id,
        execution_time: performance_metrics[:avg_execution_time],
        records_processed: performance_metrics[:avg_records_processed]
      }
    )
  end
end
