# frozen_string_literal: true

module Ai
  class DataValueAssessor
    REVENUE_POTENTIAL_WEIGHTS = {
      data_volume: 0.25,        # Higher volume = higher value
      data_quality: 0.30,       # Clean, structured data worth more
      market_demand: 0.25,      # Popular data types command premium
      uniqueness: 0.20          # Rare data sources are more valuable
    }.freeze

    MARKET_DEMAND_CATEGORIES = {
      "customer_behavior" => { demand: 0.9, avg_price_per_1k: 15.0 },
      "financial_data" => { demand: 0.95, avg_price_per_1k: 25.0 },
      "ecommerce_analytics" => { demand: 0.8, avg_price_per_1k: 12.0 },
      "social_media_insights" => { demand: 0.75, avg_price_per_1k: 8.0 },
      "iot_sensor_data" => { demand: 0.85, avg_price_per_1k: 18.0 },
      "web_analytics" => { demand: 0.7, avg_price_per_1k: 10.0 },
      "marketing_performance" => { demand: 0.8, avg_price_per_1k: 14.0 },
      "operational_metrics" => { demand: 0.6, avg_price_per_1k: 7.0 },
      "user_engagement" => { demand: 0.75, avg_price_per_1k: 11.0 },
      "api_usage_data" => { demand: 0.65, avg_price_per_1k: 9.0 }
    }.freeze

    def initialize(pipeline)
      @pipeline = pipeline
      @execution_data = pipeline.pipeline_executions.recent.limit(10)
    end

    def assess_revenue_potential
      {
        overall_score: calculate_overall_score,
        monthly_revenue_estimate: estimate_monthly_revenue,
        market_category: detect_market_category,
        recommendations: generate_recommendations,
        pricing_strategy: suggest_pricing_strategy,
        data_insights: analyze_data_characteristics,
        opportunity_level: classify_opportunity_level
      }
    end

    def identify_monetization_opportunities
      opportunities = []

      # API-as-a-Service opportunity
      if suitable_for_api_service?
        opportunities << {
          type: "api_service",
          revenue_potential: estimate_api_revenue,
          implementation_effort: "medium",
          description: "Transform pipeline into paid API service",
          suggested_pricing: suggest_api_pricing
        }
      end

      # Data Product opportunity
      if suitable_for_data_product?
        opportunities << {
          type: "data_product",
          revenue_potential: estimate_data_product_revenue,
          implementation_effort: "low",
          description: "Package processed data as purchasable dataset",
          suggested_pricing: suggest_data_product_pricing
        }
      end

      # Real-time Stream opportunity
      if suitable_for_realtime_stream?
        opportunities << {
          type: "realtime_stream",
          revenue_potential: estimate_stream_revenue,
          implementation_effort: "high",
          description: "Offer real-time data streaming service",
          suggested_pricing: suggest_stream_pricing
        }
      end

      # Custom Analytics opportunity
      if suitable_for_custom_analytics?
        opportunities << {
          type: "custom_analytics",
          revenue_potential: estimate_analytics_revenue,
          implementation_effort: "medium",
          description: "Provide custom analytics dashboards and insights",
          suggested_pricing: suggest_analytics_pricing
        }
      end

      opportunities.sort_by { |opp| -opp[:revenue_potential] }
    end

    def generate_market_insights
      category = detect_market_category
      market_data = MARKET_DEMAND_CATEGORIES[category] || { demand: 0.5, avg_price_per_1k: 5.0 }

      {
        category: category,
        market_demand: market_data[:demand],
        average_market_price: market_data[:avg_price_per_1k],
        competitive_analysis: analyze_competition,
        growth_trends: analyze_growth_trends,
        target_customers: identify_target_customers,
        value_propositions: generate_value_propositions
      }
    end

    def suggest_pricing_strategy
      score = calculate_overall_score
      category = detect_market_category
      market_data = MARKET_DEMAND_CATEGORIES[category] || { avg_price_per_1k: 5.0 }

      base_price = market_data[:avg_price_per_1k]

      {
        tier_1: { name: "Basic", price: base_price * 0.7, description: "Limited API calls, basic data" },
        tier_2: { name: "Professional", price: base_price, description: "Standard access with analytics" },
        tier_3: { name: "Enterprise", price: base_price * 1.8, description: "Full access with custom features" },
        usage_based: {
          per_api_call: (base_price / 1000.0).round(4),
          per_1k_records: base_price,
          per_gb: base_price * 2
        }
      }
    end

    private

    def calculate_overall_score
      scores = {
        data_volume: assess_data_volume,
        data_quality: assess_data_quality,
        market_demand: assess_market_demand,
        uniqueness: assess_uniqueness
      }

      weighted_score = REVENUE_POTENTIAL_WEIGHTS.sum do |factor, weight|
        scores[factor] * weight
      end

      (weighted_score * 100).round(1)
    end

    def assess_data_volume
      return 0.3 if @execution_data.empty?

      avg_records = @execution_data.average(:records_processed) || 0

      case avg_records
      when 0...1_000 then 0.3
      when 1_000...10_000 then 0.5
      when 10_000...100_000 then 0.7
      when 100_000...1_000_000 then 0.85
      else 1.0
      end
    end

    def assess_data_quality
      return 0.4 if @execution_data.empty?

      # Analyze success rate and error patterns
      success_rate = @pipeline.success_rate
      transformation_complexity = analyze_transformation_complexity

      base_score = success_rate / 100.0
      complexity_bonus = transformation_complexity * 0.2

      [ [ base_score + complexity_bonus, 1.0 ].min, 0.1 ].max
    end

    def assess_market_demand
      category = detect_market_category
      market_data = MARKET_DEMAND_CATEGORIES[category]
      market_data ? market_data[:demand] : 0.5
    end

    def assess_uniqueness
      # Analyze source types and transformation patterns
      source_uniqueness = analyze_source_uniqueness
      transformation_uniqueness = analyze_transformation_uniqueness

      (source_uniqueness + transformation_uniqueness) / 2.0
    end

    def estimate_monthly_revenue
      volume_factor = assess_data_volume
      quality_factor = assess_data_quality
      demand_factor = assess_market_demand

      base_revenue = volume_factor * quality_factor * demand_factor * 1000

      # Apply market pricing
      category = detect_market_category
      market_data = MARKET_DEMAND_CATEGORIES[category]
      price_multiplier = market_data ? market_data[:avg_price_per_1k] / 10.0 : 1.0

      (base_revenue * price_multiplier).round(2)
    end

    def detect_market_category
      source_config = @pipeline.source_config || {}
      destination_config = @pipeline.destination_config || {}
      transformation_rules = @pipeline.transformation_rules || {}

      # Analyze source and destination to categorize
      if ecommerce_indicators?(source_config, destination_config)
        "ecommerce_analytics"
      elsif financial_indicators?(source_config, destination_config)
        "financial_data"
      elsif social_media_indicators?(source_config, destination_config)
        "social_media_insights"
      elsif iot_indicators?(source_config, destination_config)
        "iot_sensor_data"
      elsif web_analytics_indicators?(source_config, destination_config)
        "web_analytics"
      elsif marketing_indicators?(source_config, destination_config)
        "marketing_performance"
      elsif user_engagement_indicators?(source_config, destination_config, transformation_rules)
        "user_engagement"
      elsif api_indicators?(source_config, destination_config)
        "api_usage_data"
      elsif customer_behavior_indicators?(source_config, destination_config, transformation_rules)
        "customer_behavior"
      else
        "operational_metrics"
      end
    end

    def generate_recommendations
      recommendations = []
      score = calculate_overall_score

      if score < 30
        recommendations << "Improve data quality by adding validation rules"
        recommendations << "Increase data volume by expanding source connections"
        recommendations << "Consider aggregating data from multiple sources"
      elsif score < 60
        recommendations << "Add real-time processing for higher market value"
        recommendations << "Implement data enrichment to increase uniqueness"
        recommendations << "Consider API-first architecture for monetization"
      else
        recommendations << "Ready for monetization - consider launching data products"
        recommendations << "Implement tiered pricing for different access levels"
        recommendations << "Add premium analytics features for enterprise customers"
      end

      recommendations
    end


    def suitable_for_api_service?
      @pipeline.active? &&
      @pipeline.success_rate > 80 &&
      regular_execution_pattern?
    end

    def suitable_for_data_product?
      has_valuable_transformations? &&
      consistent_data_output? &&
      @pipeline.success_rate > 70
    end

    def suitable_for_realtime_stream?
      @pipeline.schedule_type == "real_time" &&
      @pipeline.success_rate > 90 &&
      high_volume_data?
    end

    def suitable_for_custom_analytics?
      has_complex_transformations? &&
      business_intelligence_potential? &&
      @pipeline.success_rate > 75
    end

    # Helper methods for categorization
    def ecommerce_indicators?(source_config, destination_config)
      indicators = [ "shopify", "woocommerce", "magento", "product", "order", "customer", "inventory", "sales" ]
      config_text = [ source_config.to_s, destination_config.to_s, @pipeline.name ].join(" ").downcase
      indicators.any? { |indicator| config_text.include?(indicator) }
    end

    def financial_indicators?(source_config, destination_config)
      indicators = [ "stripe", "paypal", "payment", "transaction", "revenue", "billing", "finance", "accounting" ]
      config_text = [ source_config.to_s, destination_config.to_s, @pipeline.name ].join(" ").downcase
      indicators.any? { |indicator| config_text.include?(indicator) }
    end

    def social_media_indicators?(source_config, destination_config)
      indicators = [ "twitter", "facebook", "instagram", "linkedin", "social", "engagement", "followers", "likes" ]
      config_text = [ source_config.to_s, destination_config.to_s, @pipeline.name ].join(" ").downcase
      indicators.any? { |indicator| config_text.include?(indicator) }
    end

    def iot_indicators?(source_config, destination_config)
      indicators = [ "sensor", "iot", "device", "telemetry", "temperature", "humidity", "pressure", "monitoring" ]
      config_text = [ source_config.to_s, destination_config.to_s, @pipeline.name ].join(" ").downcase
      indicators.any? { |indicator| config_text.include?(indicator) }
    end

    def web_analytics_indicators?(source_config, destination_config)
      indicators = [ "google_analytics", "pageview", "session", "bounce", "conversion", "traffic", "visitor" ]
      config_text = [ source_config.to_s, destination_config.to_s, @pipeline.name ].join(" ").downcase
      indicators.any? { |indicator| config_text.include?(indicator) }
    end

    def marketing_indicators?(source_config, destination_config)
      indicators = [ "campaign", "marketing", "email", "newsletter", "lead", "conversion", "ctr", "roi" ]
      config_text = [ source_config.to_s, destination_config.to_s, @pipeline.name ].join(" ").downcase
      indicators.any? { |indicator| config_text.include?(indicator) }
    end

    def user_engagement_indicators?(source_config, destination_config, transformation_rules)
      indicators = [ "user", "engagement", "activity", "behavior", "interaction", "click", "view", "action" ]
      all_text = [ source_config.to_s, destination_config.to_s, transformation_rules.to_s, @pipeline.name ].join(" ").downcase
      indicators.any? { |indicator| all_text.include?(indicator) }
    end

    def api_indicators?(source_config, destination_config)
      indicators = [ "api", "endpoint", "request", "response", "rate_limit", "usage", "call", "rest" ]
      config_text = [ source_config.to_s, destination_config.to_s, @pipeline.name ].join(" ").downcase
      indicators.any? { |indicator| config_text.include?(indicator) }
    end

    def customer_behavior_indicators?(source_config, destination_config, transformation_rules)
      indicators = [ "customer", "purchase", "behavior", "preference", "journey", "funnel", "retention", "churn" ]
      all_text = [ source_config.to_s, destination_config.to_s, transformation_rules.to_s, @pipeline.name ].join(" ").downcase
      indicators.any? { |indicator| all_text.include?(indicator) }
    end

    # Additional helper methods
    def analyze_transformation_complexity
      rules = @pipeline.transformation_rules || {}
      complexity = 0

      complexity += 0.3 if rules["field_mappings"].present?
      complexity += 0.4 if rules["filters"].present?
      complexity += 0.5 if rules["aggregations"].present?
      complexity += 0.6 if rules["validations"].present?
      complexity += 0.7 if rules["enrichments"].present?

      [ complexity, 1.0 ].min
    end

    def analyze_source_uniqueness
      source_type = @pipeline.source_type

      case source_type
      when "rest_api", "graphql_api" then 0.8  # APIs often have unique data
      when "webhook" then 0.9                   # Real-time data is valuable
      when "csv_file", "json_file" then 0.4    # Files are less unique
      when "postgresql", "mysql" then 0.6      # Database data varies
      else 0.5
      end
    end

    def analyze_transformation_uniqueness
      rules = @pipeline.transformation_rules || {}
      return 0.3 if rules.empty?

      uniqueness = 0.5 # Base score
      uniqueness += 0.2 if rules["aggregations"].present?
      uniqueness += 0.2 if rules["enrichments"].present?
      uniqueness += 0.1 if rules["validations"].present?

      [ uniqueness, 1.0 ].min
    end

    def regular_execution_pattern?
      return false if @execution_data.empty?

      # Check if executions happen regularly
      @execution_data.count >= 5 && @pipeline.success_rate > 70
    end

    def has_valuable_transformations?
      rules = @pipeline.transformation_rules || {}
      rules["aggregations"].present? || rules["enrichments"].present?
    end

    def consistent_data_output?
      return false if @execution_data.empty?

      # Check consistency in output size
      records_processed = @execution_data.pluck(:records_processed).compact
      return false if records_processed.empty?

      avg = records_processed.sum.to_f / records_processed.size
      variance = records_processed.map { |x| (x - avg) ** 2 }.sum / records_processed.size

      # Low variance indicates consistency
      variance < (avg * 0.5) ** 2
    end

    def high_volume_data?
      return false if @execution_data.empty?

      avg_records = @execution_data.average(:records_processed) || 0
      avg_records > 10_000
    end

    def has_complex_transformations?
      analyze_transformation_complexity > 0.5
    end

    def business_intelligence_potential?
      rules = @pipeline.transformation_rules || {}
      rules["aggregations"].present? && (rules["filters"].present? || rules["validations"].present?)
    end

    # Revenue estimation methods
    def estimate_api_revenue
      volume_factor = assess_data_volume
      demand_factor = assess_market_demand

      # Base API revenue calculation
      base_monthly_calls = volume_factor * 10_000
      price_per_1k_calls = demand_factor * 15.0

      (base_monthly_calls / 1000.0 * price_per_1k_calls).round(2)
    end

    def estimate_data_product_revenue
      volume_factor = assess_data_volume
      quality_factor = assess_data_quality
      uniqueness_factor = assess_uniqueness

      base_revenue = volume_factor * quality_factor * uniqueness_factor * 500
      base_revenue.round(2)
    end

    def estimate_stream_revenue
      estimate_api_revenue * 2.5 # Real-time commands premium
    end

    def estimate_analytics_revenue
      base_revenue = estimate_data_product_revenue
      (base_revenue * 1.8).round(2) # Analytics premium
    end

    # Pricing suggestion methods
    def suggest_api_pricing
      category = detect_market_category
      market_data = MARKET_DEMAND_CATEGORIES[category] || { avg_price_per_1k: 5.0 }

      {
        per_1k_calls: market_data[:avg_price_per_1k],
        monthly_subscription: market_data[:avg_price_per_1k] * 10,
        enterprise_custom: "Contact for pricing"
      }
    end

    def suggest_data_product_pricing
      category = detect_market_category
      market_data = MARKET_DEMAND_CATEGORIES[category] || { avg_price_per_1k: 5.0 }

      {
        one_time_purchase: market_data[:avg_price_per_1k] * 20,
        monthly_subscription: market_data[:avg_price_per_1k] * 5,
        annual_subscription: market_data[:avg_price_per_1k] * 50
      }
    end

    def suggest_stream_pricing
      api_pricing = suggest_api_pricing

      {
        per_1k_events: api_pricing[:per_1k_calls] * 1.5,
        monthly_unlimited: api_pricing[:monthly_subscription] * 3,
        enterprise_volume: "Volume-based pricing"
      }
    end

    def suggest_analytics_pricing
      {
        basic_dashboard: 29.99,
        advanced_analytics: 99.99,
        enterprise_bi: 299.99,
        custom_reporting: "Starting at $500/month"
      }
    end

    # Market analysis methods
    def analyze_competition
      category = detect_market_category

      case category
      when "financial_data"
        { level: "high", key_players: [ "Bloomberg API", "Alpha Vantage", "IEX Cloud" ] }
      when "ecommerce_analytics"
        { level: "medium", key_players: [ "Shopify Analytics", "Google Analytics", "Mixpanel" ] }
      when "social_media_insights"
        { level: "high", key_players: [ "Hootsuite", "Sprout Social", "Buffer" ] }
      else
        { level: "medium", key_players: [ "Various niche providers" ] }
      end
    end

    def analyze_growth_trends
      category = detect_market_category

      # Simplified growth trend analysis
      case category
      when "iot_sensor_data"
        { trend: "rapidly_growing", growth_rate: "25% annually" }
      when "financial_data"
        { trend: "stable_growth", growth_rate: "12% annually" }
      when "ai_ml_data"
        { trend: "explosive_growth", growth_rate: "40% annually" }
      else
        { trend: "moderate_growth", growth_rate: "8% annually" }
      end
    end

    def identify_target_customers
      category = detect_market_category

      case category
      when "financial_data"
        [ "Fintech companies", "Trading firms", "Investment platforms", "Financial advisors" ]
      when "ecommerce_analytics"
        [ "E-commerce businesses", "Digital marketers", "Business analysts", "Consultants" ]
      when "iot_sensor_data"
        [ "IoT platform providers", "Smart city initiatives", "Industrial companies", "Research institutions" ]
      else
        [ "Data analysts", "Business intelligence teams", "Researchers", "SaaS platforms" ]
      end
    end

    def generate_value_propositions
      category = detect_market_category
      quality_score = assess_data_quality
      uniqueness_score = assess_uniqueness

      propositions = []

      propositions << "Clean, structured data ready for analysis" if quality_score > 0.7
      propositions << "Unique data sources not available elsewhere" if uniqueness_score > 0.8
      propositions << "Real-time or near real-time data updates" if @pipeline.schedule_type != "manual"
      propositions << "Reliable data pipeline with #{@pipeline.success_rate}% uptime"
      propositions << "Scalable API access with flexible pricing"

      if category == "financial_data"
        propositions << "Regulatory compliant financial data"
      elsif category == "ecommerce_analytics"
        propositions << "Actionable e-commerce insights"
      end

      propositions
    end

    def analyze_data_characteristics
      {
        average_volume: @execution_data.average(:records_processed)&.to_i || 0,
        execution_frequency: calculate_execution_frequency,
        success_rate: @pipeline.success_rate,
        data_consistency: assess_data_consistency,
        processing_complexity: analyze_transformation_complexity,
        source_reliability: assess_source_reliability
      }
    end

    def calculate_execution_frequency
      return "unknown" if @execution_data.empty?

      case @pipeline.schedule_type
      when "real_time" then "real-time"
      when "hourly" then "hourly"
      when "daily" then "daily"
      when "weekly" then "weekly"
      else "manual"
      end
    end

    def assess_data_consistency
      return 0.5 if @execution_data.empty?

      # Simple consistency check based on execution success patterns
      recent_successes = @execution_data.successful.count
      total_recent = @execution_data.count

      return 0.9 if total_recent.zero?

      (recent_successes.to_f / total_recent).round(2)
    end

    def assess_source_reliability
      # Based on connector health and execution patterns
      return 0.7 if @execution_data.empty?

      # Calculate reliability based on consistent execution times and success rates
      reliability = @pipeline.success_rate / 100.0

      # Bonus for consistent execution patterns
      if regular_execution_pattern?
        reliability += 0.1
      end

      [ [ reliability, 1.0 ].min, 0.1 ].max
    end

    def classify_opportunity_level
      score = calculate_overall_score

      case score
      when 0...30 then "low"
      when 30...60 then "medium"
      when 60...80 then "high"
      else "exceptional"
      end
    end
  end
end
