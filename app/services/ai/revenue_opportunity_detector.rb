# frozen_string_literal: true

module Ai
  class RevenueOpportunityDetector
    include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>

    OPPORTUNITY_TYPES = %w[
      new_market_segment
      pricing_optimization
      feature_expansion
      partnership_potential
      automation_improvement
      data_quality_enhancement
      performance_optimization
      security_upgrade
    ].freeze

    DETECTION_SENSITIVITY = {
      "conservative" => { threshold: 0.8, min_confidence: 0.9 },
      "balanced" => { threshold: 0.6, min_confidence: 0.7 },
      "aggressive" => { threshold: 0.4, min_confidence: 0.5 }
    }.freeze

    def initialize(account, sensitivity: "balanced")
      @account = account
      @sensitivity = sensitivity
      @detection_config = DETECTION_SENSITIVITY[sensitivity]
      @pipelines = account.pipelines.active.includes(:pipeline_executions)
    end

    def detect_opportunities
      opportunities = []

      @pipelines.each do |pipeline|
        pipeline_opportunities = analyze_pipeline_opportunities(pipeline)
        opportunities.concat(pipeline_opportunities)
      end

      # Add account-level opportunities
      account_opportunities = analyze_account_opportunities
      opportunities.concat(account_opportunities)

      # Sort by potential impact and filter by confidence threshold
      opportunities
        .select { |opp| opp[:confidence] >= @detection_config[:min_confidence] }
        .sort_by { |opp| [ -opp[:impact_score], -opp[:confidence] ] }
    end

    def detect_trending_opportunities
      historical_data = gather_historical_performance_data
      current_metrics = gather_current_metrics

      trends = analyze_performance_trends(historical_data, current_metrics)

      trending_opportunities = []

      trends.each do |trend|
        if trend[:direction] == "positive" && trend[:significance] > 0.7
          trending_opportunities << create_trending_opportunity(trend)
        end
      end

      trending_opportunities.sort_by { |opp| -opp[:momentum_score] }
    end

    def generate_opportunity_alerts
      opportunities = detect_opportunities
      critical_opportunities = opportunities.select { |opp| opp[:urgency] == "high" }

      alerts = []

      critical_opportunities.each do |opportunity|
        alerts << {
          type: "revenue_opportunity",
          severity: determine_alert_severity(opportunity),
          title: opportunity[:title],
          description: opportunity[:description],
          action_required: opportunity[:recommended_actions].first,
          potential_impact: format_currency(opportunity[:revenue_impact]),
          deadline: calculate_opportunity_deadline(opportunity),
          pipeline_id: opportunity[:pipeline_id],
          created_at: Time.current
        }
      end

      alerts
    end

    def analyze_competitive_opportunities
      market_insights = gather_market_intelligence
      competitive_gaps = identify_competitive_gaps

      competitive_opportunities = []

      competitive_gaps.each do |gap|
        opportunity = {
          type: "competitive_advantage",
          title: "Exploit competitive gap in #{gap[:market_segment]}",
          description: gap[:description],
          competitive_advantage: gap[:advantage_type],
          market_size: gap[:market_size],
          competitor_weakness: gap[:weakness],
          implementation_effort: gap[:effort_required],
          confidence: gap[:confidence],
          revenue_impact: estimate_competitive_revenue(gap),
          time_to_market: gap[:time_to_market],
          recommended_actions: generate_competitive_actions(gap)
        }

        competitive_opportunities << opportunity if opportunity[:confidence] > @detection_config[:min_confidence]
      end

      competitive_opportunities.sort_by { |opp| -opp[:revenue_impact] }
    end

    def create_opportunity_report
      opportunities = detect_opportunities
      trending = detect_trending_opportunities
      competitive = analyze_competitive_opportunities

      {
        summary: generate_report_summary(opportunities),
        high_impact_opportunities: opportunities.take(5),
        trending_opportunities: trending.take(3),
        competitive_opportunities: competitive.take(3),
        implementation_recommendations: prioritize_implementations(opportunities),
        market_analysis: generate_market_analysis,
        next_review_date: 2.weeks.from_now,
        generated_at: Time.current
      }
    end

    private

    def analyze_pipeline_opportunities(pipeline)
      opportunities = []
      assessor = Ai::DataValueAssessor.new(pipeline)
      monetization_analyzer = Ai::PipelineMonetizationAnalyzer.new(pipeline)

      # Market expansion opportunities
      market_opportunities = detect_market_expansion(pipeline, assessor)
      opportunities.concat(market_opportunities)

      # Pricing optimization opportunities
      pricing_opportunities = detect_pricing_optimization(pipeline, assessor)
      opportunities.concat(pricing_opportunities)

      # Technical improvement opportunities
      technical_opportunities = detect_technical_improvements(pipeline, monetization_analyzer)
      opportunities.concat(technical_opportunities)

      # Feature expansion opportunities
      feature_opportunities = detect_feature_expansion(pipeline, assessor)
      opportunities.concat(feature_opportunities)

      opportunities
    end

    def analyze_account_opportunities
      opportunities = []

      # Portfolio optimization opportunities
      portfolio_opportunities = detect_portfolio_optimization
      opportunities.concat(portfolio_opportunities)

      # Cross-selling opportunities
      cross_sell_opportunities = detect_cross_selling_opportunities
      opportunities.concat(cross_sell_opportunities)

      # Infrastructure optimization opportunities
      infrastructure_opportunities = detect_infrastructure_optimization
      opportunities.concat(infrastructure_opportunities)

      opportunities
    end

    def detect_market_expansion(pipeline, assessor)
      opportunities = []
      market_insights = assessor.generate_market_insights

      # New geographic markets
      if market_insights[:growth_trends][:trend] == "rapidly_growing"
        opportunities << {
          type: "new_market_segment",
          pipeline_id: pipeline.id,
          title: "Expand to high-growth market segment",
          description: "#{market_insights[:category]} market showing #{market_insights[:growth_trends][:growth_rate]} growth",
          market_segment: market_insights[:category],
          growth_rate: market_insights[:growth_trends][:growth_rate],
          confidence: 0.8,
          impact_score: calculate_impact_score(pipeline, "market_expansion"),
          revenue_impact: estimate_market_expansion_revenue(pipeline, market_insights),
          implementation_effort: "medium",
          urgency: determine_urgency(market_insights),
          recommended_actions: [
            "Research target market requirements",
            "Adapt data format for new market",
            "Develop market-specific features"
          ]
        }
      end

      # Underserved customer segments
      target_customers = market_insights[:target_customers]
      if target_customers.length > 3
        untapped_segments = identify_untapped_segments(target_customers, pipeline)

        untapped_segments.each do |segment|
          opportunities << {
            type: "new_market_segment",
            pipeline_id: pipeline.id,
            title: "Target #{segment} market segment",
            description: "Potential untapped market in #{segment} segment",
            market_segment: segment,
            confidence: 0.7,
            impact_score: calculate_impact_score(pipeline, "customer_segment"),
            revenue_impact: estimate_segment_revenue(pipeline, segment),
            implementation_effort: "low",
            urgency: "medium",
            recommended_actions: [
              "Analyze #{segment} requirements",
              "Create targeted marketing campaign",
              "Develop segment-specific features"
            ]
          }
        end
      end

      opportunities
    end

    def detect_pricing_optimization(pipeline, assessor)
      opportunities = []
      current_pricing = assessor.suggest_pricing_strategy
      market_insights = assessor.generate_market_insights

      # Price elasticity analysis
      if market_insights[:market_demand] > 0.8
        opportunities << {
          type: "pricing_optimization",
          pipeline_id: pipeline.id,
          title: "Premium pricing opportunity",
          description: "High market demand suggests pricing power for premium tiers",
          market_demand: market_insights[:market_demand],
          current_pricing: current_pricing[:tier_2][:price],
          suggested_pricing: current_pricing[:tier_2][:price] * 1.3,
          confidence: 0.85,
          impact_score: calculate_impact_score(pipeline, "pricing"),
          revenue_impact: estimate_pricing_impact(pipeline, 0.3),
          implementation_effort: "low",
          urgency: "high",
          recommended_actions: [
            "Test premium pricing with subset of customers",
            "Add premium features to justify price increase",
            "Monitor customer churn during transition"
          ]
        }
      end

      # Usage-based pricing opportunity
      if high_usage_variance?(pipeline)
        opportunities << {
          type: "pricing_optimization",
          pipeline_id: pipeline.id,
          title: "Usage-based pricing model",
          description: "High usage variance suggests opportunity for usage-based pricing",
          usage_variance: calculate_usage_variance(pipeline),
          confidence: 0.75,
          impact_score: calculate_impact_score(pipeline, "usage_pricing"),
          revenue_impact: estimate_usage_pricing_impact(pipeline),
          implementation_effort: "medium",
          urgency: "medium",
          recommended_actions: [
            "Implement usage tracking",
            "Design tiered usage pricing",
            "Migrate customers gradually"
          ]
        }
      end

      opportunities
    end

    def detect_technical_improvements(pipeline, monetization_analyzer)
      opportunities = []
      readiness = monetization_analyzer.analyze_monetization_readiness

      # Performance optimization opportunities
      if readiness[:blocking_issues].any? { |issue| issue[:type] == "performance" }
        opportunities << {
          type: "performance_optimization",
          pipeline_id: pipeline.id,
          title: "Performance optimization for SLA compliance",
          description: "Improve pipeline performance to meet API SLA requirements",
          current_performance: calculate_current_performance(pipeline),
          target_performance: calculate_target_performance(pipeline),
          confidence: 0.9,
          impact_score: calculate_impact_score(pipeline, "performance"),
          revenue_impact: estimate_performance_revenue_impact(pipeline),
          implementation_effort: "medium",
          urgency: "high",
          recommended_actions: [
            "Profile pipeline bottlenecks",
            "Optimize data processing algorithms",
            "Implement caching layers"
          ]
        }
      end

      # Security upgrade opportunities
      if readiness[:blocking_issues].any? { |issue| issue[:type] == "security" }
        opportunities << {
          type: "security_upgrade",
          pipeline_id: pipeline.id,
          title: "Security enhancements for enterprise customers",
          description: "Implement enterprise-grade security for premium market access",
          security_gaps: readiness[:blocking_issues].select { |issue| issue[:type] == "security" },
          confidence: 0.95,
          impact_score: calculate_impact_score(pipeline, "security"),
          revenue_impact: estimate_security_revenue_impact(pipeline),
          implementation_effort: "medium",
          urgency: "high",
          recommended_actions: [
            "Implement SOC2 compliance",
            "Add advanced authentication",
            "Enable audit logging"
          ]
        }
      end

      opportunities
    end

    def detect_feature_expansion(pipeline, assessor)
      opportunities = []
      market_insights = assessor.generate_market_insights

      # Real-time feature opportunity
      if pipeline.schedule_type != "real_time" && market_insights[:market_demand] > 0.7
        opportunities << {
          type: "feature_expansion",
          pipeline_id: pipeline.id,
          title: "Real-time data streaming feature",
          description: "Add real-time capabilities to capture streaming market premium",
          feature_type: "real_time_streaming",
          market_premium: 2.5, # 250% premium for real-time
          confidence: 0.8,
          impact_score: calculate_impact_score(pipeline, "realtime"),
          revenue_impact: estimate_realtime_revenue_impact(pipeline),
          implementation_effort: "high",
          urgency: "medium",
          recommended_actions: [
            "Assess real-time infrastructure requirements",
            "Implement WebSocket/SSE endpoints",
            "Test real-time processing pipeline"
          ]
        }
      end

      # Analytics dashboard opportunity
      transformation_rules = pipeline.transformation_rules || {}
      if transformation_rules["aggregations"].present?
        opportunities << {
          type: "feature_expansion",
          pipeline_id: pipeline.id,
          title: "Interactive analytics dashboard",
          description: "Leverage aggregated data to provide analytics dashboards",
          feature_type: "analytics_dashboard",
          confidence: 0.7,
          impact_score: calculate_impact_score(pipeline, "analytics"),
          revenue_impact: estimate_analytics_revenue_impact(pipeline),
          implementation_effort: "medium",
          urgency: "low",
          recommended_actions: [
            "Design dashboard mockups",
            "Implement charting library",
            "Add interactive filters"
          ]
        }
      end

      opportunities
    end

    def detect_portfolio_optimization
      opportunities = []

      # Pipeline consolidation opportunity
      similar_pipelines = find_similar_pipelines
      if similar_pipelines.any? { |group| group.size > 2 }
        opportunities << {
          type: "automation_improvement",
          title: "Pipeline consolidation and automation",
          description: "Consolidate similar pipelines to reduce operational overhead",
          similar_pipeline_groups: similar_pipelines,
          confidence: 0.8,
          impact_score: 75,
          revenue_impact: estimate_consolidation_savings,
          implementation_effort: "medium",
          urgency: "low",
          recommended_actions: [
            "Analyze pipeline overlap",
            "Design unified pipeline architecture",
            "Migrate customers to consolidated solution"
          ]
        }
      end

      opportunities
    end

    def detect_cross_selling_opportunities
      opportunities = []

      # Customer usage pattern analysis
      high_usage_customers = identify_high_usage_customers

      high_usage_customers.each do |customer_data|
        if customer_data[:expansion_potential] > 0.7
          opportunities << {
            type: "feature_expansion",
            title: "Upsell opportunity for #{customer_data[:account_name]}",
            description: "High usage customer ready for premium features",
            customer_account: customer_data[:account_name],
            current_usage: customer_data[:usage_metrics],
            expansion_potential: customer_data[:expansion_potential],
            confidence: 0.85,
            impact_score: 60,
            revenue_impact: customer_data[:upsell_potential],
            implementation_effort: "low",
            urgency: "high",
            recommended_actions: [
              "Schedule customer success call",
              "Present premium feature demo",
              "Offer limited-time upgrade discount"
            ]
          }
        end
      end

      opportunities
    end

    def detect_infrastructure_optimization
      opportunities = []

      # Cost optimization opportunity
      if total_infrastructure_cost > cost_optimization_threshold
        opportunities << {
          type: "automation_improvement",
          title: "Infrastructure cost optimization",
          description: "Optimize cloud infrastructure to improve profit margins",
          current_cost: total_infrastructure_cost,
          potential_savings: estimate_infrastructure_savings,
          confidence: 0.9,
          impact_score: 50,
          revenue_impact: estimate_infrastructure_savings,
          implementation_effort: "medium",
          urgency: "medium",
          recommended_actions: [
            "Audit current infrastructure usage",
            "Implement auto-scaling policies",
            "Negotiate better cloud pricing"
          ]
        }
      end

      opportunities
    end

    # Helper methods for calculations and analysis
    def calculate_impact_score(pipeline, opportunity_type)
      base_score = case opportunity_type
      when "market_expansion" then 80
      when "pricing" then 70
      when "performance" then 60
      when "security" then 65
      when "realtime" then 85
      when "analytics" then 55
      else 50
      end

      # Adjust based on pipeline characteristics
      volume_factor = Ai::DataValueAssessor.new(pipeline).send(:assess_data_volume)
      quality_factor = Ai::DataValueAssessor.new(pipeline).send(:assess_data_quality)

      adjusted_score = base_score * (0.5 + (volume_factor + quality_factor) / 2.0 * 0.5)
      adjusted_score.round
    end

    def estimate_market_expansion_revenue(pipeline, market_insights)
      assessor = Ai::DataValueAssessor.new(pipeline)
      base_revenue = assessor.assess_revenue_potential[:monthly_revenue_estimate]

      growth_multiplier = case market_insights[:growth_trends][:growth_rate]
      when /25%|30%|35%/ then 1.5
      when /15%|20%/ then 1.3
      else 1.2
      end

      (base_revenue * growth_multiplier * 6).round(2) # 6-month projection
    end

    def estimate_pricing_impact(pipeline, price_increase)
      assessor = Ai::DataValueAssessor.new(pipeline)
      base_revenue = assessor.assess_revenue_potential[:monthly_revenue_estimate]

      # Assume 85% customer retention with price increase
      (base_revenue * (1 + price_increase) * 0.85 - base_revenue) * 12
    end

    def high_usage_variance?(pipeline)
      return false if pipeline.pipeline_executions.empty?

      records = pipeline.pipeline_executions.recent.pluck(:records_processed).compact
      return false if records.empty? || records.size < 5

      mean = records.sum.to_f / records.size
      variance = records.map { |r| (r - mean) ** 2 }.sum / records.size
      coefficient_of_variation = Math.sqrt(variance) / mean

      coefficient_of_variation > 0.5
    end

    def calculate_usage_variance(pipeline)
      records = pipeline.pipeline_executions.recent.pluck(:records_processed).compact
      return 0 if records.empty?

      mean = records.sum.to_f / records.size
      variance = records.map { |r| (r - mean) ** 2 }.sum / records.size
      Math.sqrt(variance) / mean
    end

    def estimate_usage_pricing_impact(pipeline)
      assessor = Ai::DataValueAssessor.new(pipeline)
      base_revenue = assessor.assess_revenue_potential[:monthly_revenue_estimate]

      # Usage-based pricing typically increases revenue by 20-40%
      (base_revenue * 0.3 * 12).round(2)
    end

    def determine_urgency(market_insights)
      growth_rate = market_insights[:growth_trends][:growth_rate]

      case growth_rate
      when /40%|explosive/ then "high"
      when /25%|30%|rapidly/ then "high"
      when /15%|20%/ then "medium"
      else "low"
      end
    end

    def format_currency(amount)
      return "$0" if amount.nil? || amount.zero?

      if amount >= 1_000_000
        "$#{(amount / 1_000_000.0).round(1)}M"
      elsif amount >= 1_000
        "$#{(amount / 1_000.0).round(1)}K"
      else
        "$#{amount.round}"
      end
    end

    def calculate_opportunity_deadline(opportunity)
      case opportunity[:urgency]
      when "high" then 2.weeks.from_now
      when "medium" then 1.month.from_now
      else 3.months.from_now
      end
    end

    def determine_alert_severity(opportunity)
      if opportunity[:revenue_impact] > 10_000 && opportunity[:urgency] == "high"
        "critical"
      elsif opportunity[:revenue_impact] > 5_000 && opportunity[:urgency] != "low"
        "warning"
      else
        "info"
      end
    end

    # Placeholder methods for features to be implemented
    def gather_historical_performance_data
      # TODO: Implement historical data gathering
      []
    end

    def gather_current_metrics
      # TODO: Implement current metrics gathering
      {}
    end

    def analyze_performance_trends(historical, current)
      # TODO: Implement trend analysis
      []
    end

    def create_trending_opportunity(trend)
      # TODO: Implement trending opportunity creation
      { momentum_score: 0 }
    end

    def gather_market_intelligence
      # TODO: Implement market intelligence gathering
      {}
    end

    def identify_competitive_gaps
      # TODO: Implement competitive gap analysis
      []
    end

    def estimate_competitive_revenue(gap)
      # TODO: Implement competitive revenue estimation
      0
    end

    def generate_competitive_actions(gap)
      # TODO: Implement competitive action generation
      []
    end

    def identify_untapped_segments(customers, pipeline)
      # Simplified implementation - return sample segments
      all_segments = [
        "Data analysts", "Business intelligence teams", "Researchers",
        "SaaS platforms", "Consultants", "Marketing teams"
      ]

      all_segments - customers.sample(3)
    end

    def estimate_segment_revenue(pipeline, segment)
      assessor = Ai::DataValueAssessor.new(pipeline)
      base_revenue = assessor.assess_revenue_potential[:monthly_revenue_estimate]
      (base_revenue * 0.4 * 12).round(2) # New segment adds 40% revenue
    end

    def calculate_current_performance(pipeline)
      avg_duration = pipeline.pipeline_executions.recent.average(:execution_time) || 300
      "#{(avg_duration / 60.0).round(1)} minutes average"
    end

    def calculate_target_performance(pipeline)
      current_avg = pipeline.pipeline_executions.recent.average(:execution_time) || 300
      target = current_avg * 0.6 # 40% improvement target
      "#{(target / 60.0).round(1)} minutes target"
    end

    def estimate_performance_revenue_impact(pipeline)
      # Performance improvements typically enable 15% revenue increase
      assessor = Ai::DataValueAssessor.new(pipeline)
      base_revenue = assessor.assess_revenue_potential[:monthly_revenue_estimate]
      (base_revenue * 0.15 * 12).round(2)
    end

    def estimate_security_revenue_impact(pipeline)
      # Enterprise security enables premium pricing
      assessor = Ai::DataValueAssessor.new(pipeline)
      base_revenue = assessor.assess_revenue_potential[:monthly_revenue_estimate]
      (base_revenue * 0.5 * 12).round(2) # 50% premium for enterprise features
    end

    def estimate_realtime_revenue_impact(pipeline)
      assessor = Ai::DataValueAssessor.new(pipeline)
      base_revenue = assessor.assess_revenue_potential[:monthly_revenue_estimate]
      (base_revenue * 1.5 * 12).round(2) # Real-time commands 150% premium
    end

    def estimate_analytics_revenue_impact(pipeline)
      assessor = Ai::DataValueAssessor.new(pipeline)
      base_revenue = assessor.assess_revenue_potential[:monthly_revenue_estimate]
      (base_revenue * 0.3 * 12).round(2) # Analytics adds 30% value
    end

    def find_similar_pipelines
      # Group pipelines by similar characteristics
      pipeline_groups = @pipelines.group_by do |pipeline|
        "#{pipeline.source_type}_#{pipeline.destination_type}"
      end

      pipeline_groups.values.select { |group| group.size > 1 }
    end

    def estimate_consolidation_savings
      # Estimate operational savings from consolidation
      similar_groups = find_similar_pipelines
      total_savings = similar_groups.sum do |group|
        (group.size - 1) * 200 # $200/month operational cost per pipeline
      end

      total_savings * 12 # Annual savings
    end

    def identify_high_usage_customers
      # Simplified implementation
      high_usage = []

      @account.pipelines.each do |pipeline|
        next if pipeline.pipeline_executions.empty?

        avg_usage = pipeline.pipeline_executions.recent.average(:records_processed) || 0
        if avg_usage > 50_000
          high_usage << {
            account_name: @account.name,
            pipeline_name: pipeline.name,
            usage_metrics: { avg_records: avg_usage.to_i },
            expansion_potential: 0.8,
            upsell_potential: 2400 # $200/month * 12 months
          }
        end
      end

      high_usage
    end

    def total_infrastructure_cost
      # Placeholder - would integrate with actual cost tracking
      @pipelines.count * 50 # $50/month per pipeline
    end

    def cost_optimization_threshold
      500 # $500/month threshold
    end

    def estimate_infrastructure_savings
      current_cost = total_infrastructure_cost
      (current_cost * 0.25 * 12).round(2) # 25% annual savings potential
    end

    def generate_report_summary(opportunities)
      total_impact = opportunities.sum { |opp| opp[:revenue_impact] || 0 }
      high_impact_count = opportunities.count { |opp| (opp[:revenue_impact] || 0) > 5000 }

      {
        total_opportunities: opportunities.size,
        high_impact_opportunities: high_impact_count,
        total_revenue_potential: total_impact,
        top_opportunity_type: opportunities.first&.dig(:type) || "none",
        average_confidence: opportunities.map { |opp| opp[:confidence] }.sum / opportunities.size.to_f
      }
    end

    def prioritize_implementations(opportunities)
      # Sort by impact vs effort ratio
      prioritized = opportunities.sort_by do |opp|
        effort_score = case opp[:implementation_effort]
        when "low" then 1
        when "medium" then 2
        when "high" then 3
        else 2
        end

        impact_revenue = opp[:revenue_impact] || 0
        -(impact_revenue / effort_score) # Negative for descending sort
      end

      prioritized.take(10).map do |opp|
        {
          title: opp[:title],
          impact: opp[:revenue_impact],
          effort: opp[:implementation_effort],
          priority_score: (opp[:revenue_impact] || 0) / case opp[:implementation_effort]
                          when "low" then 1
                          when "medium" then 2
                          when "high" then 3
                          else 2
                          end
        }
      end
    end

    def generate_market_analysis
      # Simplified market analysis
      total_pipelines = @pipelines.count
      active_pipelines = @pipelines.select(&:active?).count

      {
        portfolio_health: active_pipelines.to_f / total_pipelines,
        market_coverage: calculate_market_coverage,
        growth_potential: assess_portfolio_growth_potential,
        competitive_position: "analyzing" # Placeholder
      }
    end

    def calculate_market_coverage
      # Analyze what market segments are covered
      categories = @pipelines.map do |pipeline|
        Ai::DataValueAssessor.new(pipeline).send(:detect_market_category)
      end

      unique_categories = categories.uniq.size
      total_available_categories = Ai::DataValueAssessor::MARKET_DEMAND_CATEGORIES.size

      unique_categories.to_f / total_available_categories
    end

    def assess_portfolio_growth_potential
      growth_scores = @pipelines.map do |pipeline|
        assessor = Ai::DataValueAssessor.new(pipeline)
        assessor.send(:calculate_overall_score) / 100.0
      end

      return 0.5 if growth_scores.empty?

      growth_scores.sum / growth_scores.size
    end
  end
end
