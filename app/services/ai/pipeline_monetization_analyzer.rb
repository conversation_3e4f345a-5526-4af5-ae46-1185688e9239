# frozen_string_literal: true

module Ai
  class PipelineMonetizationAnalyzer
    include ActionView::Helpers::Number<PERSON>elper

    MONETIZATION_STRATEGIES = %w[
      api_service
      data_product_subscription
      real_time_streaming
      custom_analytics_dashboard
      white_label_solution
    ].freeze

    IMPLEMENTATION_COMPLEXITY = {
      "low" => { effort_days: 1..3, technical_risk: 0.1 },
      "medium" => { effort_days: 3..7, technical_risk: 0.3 },
      "high" => { effort_days: 7..14, technical_risk: 0.5 },
      "very_high" => { effort_days: 14..30, technical_risk: 0.7 }
    }.freeze

    def initialize(pipeline)
      @pipeline = pipeline
      @assessor = Ai::DataValueAssessor.new(pipeline)
      @execution_data = pipeline.pipeline_executions.recent.limit(50)
    end

    def analyze_monetization_readiness
      {
        readiness_score: calculate_readiness_score,
        readiness_level: determine_readiness_level,
        blocking_issues: identify_blocking_issues,
        quick_wins: identify_quick_wins,
        implementation_roadmap: create_implementation_roadmap,
        revenue_projections: generate_revenue_projections,
        risk_assessment: assess_implementation_risks,
        recommended_strategy: recommend_primary_strategy
      }
    end

    def generate_api_specification
      return { error: "Pipeline not ready for API monetization" } unless api_ready?

      {
        api_design: generate_api_design,
        authentication: suggest_authentication_strategy,
        rate_limiting: calculate_rate_limits,
        pricing_tiers: design_pricing_tiers,
        documentation_outline: create_documentation_outline,
        implementation_steps: api_implementation_steps
      }
    end

    def create_monetization_plan
      strategy = recommend_primary_strategy

      {
        strategy: strategy,
        timeline: create_implementation_timeline(strategy),
        resource_requirements: calculate_resource_requirements(strategy),
        success_metrics: define_success_metrics(strategy),
        launch_checklist: create_launch_checklist(strategy),
        marketing_strategy: suggest_marketing_approach(strategy),
        competitive_positioning: analyze_competitive_positioning
      }
    end

    def estimate_market_entry_cost
      strategy = recommend_primary_strategy
      base_cost = calculate_base_implementation_cost(strategy)

      {
        development_cost: base_cost[:development],
        infrastructure_cost: base_cost[:infrastructure],
        marketing_cost: base_cost[:marketing],
        operational_cost: base_cost[:operational],
        total_initial_cost: base_cost.values.sum,
        monthly_operational_cost: calculate_monthly_operational_cost(strategy),
        break_even_analysis: calculate_break_even_timeline(strategy)
      }
    end

    private

    def calculate_readiness_score
      scores = {
        technical_stability: assess_technical_stability,
        data_quality: assess_data_consistency,
        performance_reliability: assess_performance_metrics,
        security_compliance: assess_security_readiness,
        scalability_potential: assess_scalability_factors,
        market_viability: assess_market_opportunity
      }

      weighted_score = scores.sum { |factor, score| score * readiness_weight(factor) }
      (weighted_score * 100).round(1)
    end

    def readiness_weight(factor)
      {
        technical_stability: 0.25,
        data_quality: 0.20,
        performance_reliability: 0.20,
        security_compliance: 0.15,
        scalability_potential: 0.10,
        market_viability: 0.10
      }[factor] || 0.0
    end

    def determine_readiness_level
      score = calculate_readiness_score

      case score
      when 0...30 then "not_ready"
      when 30...50 then "needs_improvement"
      when 50...70 then "nearly_ready"
      when 70...85 then "ready"
      else "highly_ready"
      end
    end

    def identify_blocking_issues
      issues = []

      issues << {
        type: "technical_stability",
        severity: "high",
        description: "Pipeline success rate below 80%",
        impact: "API reliability concerns",
        solution: "Improve error handling and data validation"
      } if @pipeline.success_rate < 80

      issues << {
        type: "performance",
        severity: "medium",
        description: "Inconsistent execution times",
        impact: "SLA compliance issues",
        solution: "Optimize data processing and add performance monitoring"
      } if inconsistent_performance?

      issues << {
        type: "security",
        severity: "high",
        description: "No authentication or rate limiting configured",
        impact: "Security and abuse risks",
        solution: "Implement API authentication and rate limiting"
      } unless has_security_measures?

      issues << {
        type: "scalability",
        severity: "medium",
        description: "Fixed resource allocation may not handle increased load",
        impact: "Service degradation under load",
        solution: "Implement auto-scaling and load balancing"
      } unless scalable_architecture?

      issues
    end

    def identify_quick_wins
      wins = []

      wins << {
        type: "api_wrapper",
        effort: "low",
        description: "Create simple REST API wrapper",
        impact: "Immediate monetization potential",
        estimated_value: estimate_api_revenue_quick_win
      } if api_wrapper_feasible?

      wins << {
        type: "data_export",
        effort: "low",
        description: "Offer processed data as downloadable exports",
        impact: "Low-effort revenue stream",
        estimated_value: estimate_data_export_revenue
      } if data_export_feasible?

      wins << {
        type: "webhook_service",
        effort: "medium",
        description: "Provide webhook notifications for data updates",
        impact: "Real-time data service",
        estimated_value: estimate_webhook_revenue
      } if webhook_feasible?

      wins.sort_by { |win| -win[:estimated_value] }
    end

    def create_implementation_roadmap
      strategy = recommend_primary_strategy
      complexity = assess_implementation_complexity(strategy)

      {
        phase_1: {
          name: "Foundation",
          duration: "2-4 weeks",
          tasks: foundation_tasks(strategy),
          deliverables: foundation_deliverables(strategy)
        },
        phase_2: {
          name: "Core Implementation",
          duration: complexity == "high" ? "4-8 weeks" : "2-4 weeks",
          tasks: core_implementation_tasks(strategy),
          deliverables: core_deliverables(strategy)
        },
        phase_3: {
          name: "Launch & Optimization",
          duration: "2-3 weeks",
          tasks: launch_tasks(strategy),
          deliverables: launch_deliverables(strategy)
        }
      }
    end

    def generate_revenue_projections
      strategy = recommend_primary_strategy
      base_assessment = @assessor.assess_revenue_potential

      {
        month_1: project_revenue(base_assessment[:monthly_revenue_estimate], 1, strategy),
        month_3: project_revenue(base_assessment[:monthly_revenue_estimate], 3, strategy),
        month_6: project_revenue(base_assessment[:monthly_revenue_estimate], 6, strategy),
        month_12: project_revenue(base_assessment[:monthly_revenue_estimate], 12, strategy),
        assumptions: revenue_projection_assumptions(strategy),
        growth_factors: identify_growth_factors(strategy)
      }
    end

    def project_revenue(base_revenue, months, strategy)
      growth_rate = monthly_growth_rate(strategy)
      adoption_factor = customer_adoption_curve(months)

      (base_revenue * adoption_factor * (1 + growth_rate) ** months).round(2)
    end

    def monthly_growth_rate(strategy)
      {
        "api_service" => 0.15,
        "data_product_subscription" => 0.12,
        "real_time_streaming" => 0.20,
        "custom_analytics_dashboard" => 0.10,
        "white_label_solution" => 0.08
      }[strategy] || 0.10
    end

    def customer_adoption_curve(months)
      # S-curve adoption: slow start, rapid growth, plateau
      case months
      when 1 then 0.1
      when 2..3 then 0.3
      when 4..6 then 0.6
      when 7..12 then 0.85
      else 1.0
      end
    end

    def recommend_primary_strategy
      opportunities = @assessor.identify_monetization_opportunities
      readiness = calculate_readiness_score

      # Select strategy based on highest potential and implementation feasibility
      if readiness > 70 && api_ready?
        "api_service"
      elsif opportunities.any? { |opp| opp[:type] == "data_product" }
        "data_product_subscription"
      elsif @pipeline.schedule_type == "real_time"
        "real_time_streaming"
      else
        "custom_analytics_dashboard"
      end
    end

    def generate_api_design
      return {} unless api_ready?

      {
        base_url: "https://api.#{@pipeline.account.subdomain}.datareflow.io",
        endpoints: design_api_endpoints,
        request_format: determine_request_format,
        response_format: design_response_format,
        error_handling: design_error_responses,
        versioning: suggest_versioning_strategy
      }
    end

    def design_api_endpoints
      [
        {
          path: "/v1/pipelines/#{@pipeline.id}/execute",
          method: "POST",
          description: "Execute pipeline with custom parameters",
          rate_limit: "100 requests/hour",
          pricing_tier: "all"
        },
        {
          path: "/v1/pipelines/#{@pipeline.id}/data",
          method: "GET",
          description: "Retrieve processed data",
          rate_limit: "1000 requests/hour",
          pricing_tier: "professional+"
        },
        {
          path: "/v1/pipelines/#{@pipeline.id}/status",
          method: "GET",
          description: "Check pipeline execution status",
          rate_limit: "unlimited",
          pricing_tier: "all"
        }
      ]
    end

    def calculate_rate_limits
      volume_score = @assessor.send(:assess_data_volume)

      {
        basic_tier: {
          requests_per_hour: (volume_score * 100).to_i,
          requests_per_day: (volume_score * 1000).to_i,
          concurrent_requests: 5
        },
        professional_tier: {
          requests_per_hour: (volume_score * 500).to_i,
          requests_per_day: (volume_score * 10000).to_i,
          concurrent_requests: 20
        },
        enterprise_tier: {
          requests_per_hour: "unlimited",
          requests_per_day: "unlimited",
          concurrent_requests: 100
        }
      }
    end

    # Assessment helper methods
    def assess_technical_stability
      return 0.3 if @execution_data.empty?

      success_rate = @pipeline.success_rate / 100.0
      error_rate = calculate_error_rate

      (success_rate * 0.7) + ((1 - error_rate) * 0.3)
    end

    def assess_data_consistency
      return 0.4 if @execution_data.empty?

      @assessor.send(:assess_data_quality)
    end

    def assess_performance_metrics
      return 0.4 if @execution_data.empty?

      avg_duration = @execution_data.average(:execution_time) || 300
      consistency = calculate_performance_consistency

      # Lower duration and higher consistency = better score
      duration_score = [ 1.0 - (avg_duration / 3600.0), 0.1 ].max # Normalize against 1 hour
      (duration_score * 0.6) + (consistency * 0.4)
    end

    def assess_security_readiness
      # Basic security assessment
      score = 0.5 # Base score

      score += 0.2 if @pipeline.account.has_api_authentication?
      score += 0.15 if has_rate_limiting_configured?
      score += 0.1 if has_data_encryption?
      score += 0.05 if has_audit_logging?

      [ score, 1.0 ].min
    end

    def assess_scalability_factors
      volume_factor = @assessor.send(:assess_data_volume)
      architecture_factor = assess_architecture_scalability

      (volume_factor + architecture_factor) / 2.0
    end

    def assess_market_opportunity
      @assessor.send(:assess_market_demand)
    end

    # Feature detection methods
    def api_ready?
      @pipeline.active? &&
      @pipeline.success_rate > 70 &&
      @execution_data.count >= 3
    end

    def api_wrapper_feasible?
      @pipeline.active? && @pipeline.success_rate > 60
    end

    def data_export_feasible?
      @execution_data.any? { |exec| (exec.records_processed || 0) > 1000 }
    end

    def webhook_feasible?
      @pipeline.schedule_type != "manual" && @pipeline.success_rate > 75
    end

    def has_security_measures?
      # Check if basic security measures are in place
      @pipeline.account.has_api_authentication? || has_rate_limiting_configured?
    end

    def scalable_architecture?
      # Simple heuristic - check if using cloud-native components
      source_type = @pipeline.source_type
      %w[rest_api graphql_api webhook cloud_storage].include?(source_type)
    end

    def inconsistent_performance?
      return false if @execution_data.empty?

      durations = @execution_data.pluck(:execution_time).compact
      return false if durations.empty?

      avg = durations.sum.to_f / durations.size
      variance = durations.map { |d| (d - avg) ** 2 }.sum / durations.size

      # High variance indicates inconsistent performance
      variance > (avg * 0.5) ** 2
    end

    # Revenue estimation helpers
    def estimate_api_revenue_quick_win
      base_volume = @assessor.send(:assess_data_volume)
      market_demand = @assessor.send(:assess_market_demand)

      (base_volume * market_demand * 500).round(2)
    end

    def estimate_data_export_revenue
      avg_records = @execution_data.average(:records_processed) || 1000
      (avg_records / 1000.0 * 5.0 * 30).round(2) # $5 per 1K records, monthly
    end

    def estimate_webhook_revenue
      estimate_api_revenue_quick_win * 1.5 # Premium for real-time
    end

    # Implementation planning helpers
    def assess_implementation_complexity(strategy)
      base_complexity = {
        "api_service" => "medium",
        "data_product_subscription" => "low",
        "real_time_streaming" => "high",
        "custom_analytics_dashboard" => "medium",
        "white_label_solution" => "very_high"
      }[strategy]

      # Adjust based on pipeline characteristics
      if @pipeline.transformation_rules.present? && @pipeline.transformation_rules.keys.count > 3
        complexity_levels = %w[low medium high very_high]
        current_index = complexity_levels.index(base_complexity) || 1
        complexity_levels[[ current_index + 1, 3 ].min]
      else
        base_complexity
      end
    end

    def foundation_tasks(strategy)
      tasks = [
        "Set up API authentication and authorization",
        "Implement rate limiting and usage tracking",
        "Create monitoring and alerting system",
        "Design pricing and billing integration"
      ]

      if strategy == "real_time_streaming"
        tasks << "Set up real-time infrastructure (WebSockets/SSE)"
        tasks << "Implement stream processing pipeline"
      end

      tasks
    end

    def core_implementation_tasks(strategy)
      case strategy
      when "api_service"
        [
          "Develop REST API endpoints",
          "Implement request validation",
          "Add response formatting",
          "Create API documentation"
        ]
      when "data_product_subscription"
        [
          "Build subscription management system",
          "Implement data packaging and delivery",
          "Create customer portal",
          "Set up automated billing"
        ]
      else
        [
          "Implement core functionality",
          "Add user interface components",
          "Integrate with billing system",
          "Create documentation"
        ]
      end
    end

    def launch_tasks(strategy)
      [
        "Beta testing with selected customers",
        "Performance optimization and scaling",
        "Security audit and penetration testing",
        "Marketing material creation",
        "Customer support system setup",
        "Go-to-market strategy execution"
      ]
    end

    def calculate_base_implementation_cost(strategy)
      complexity = assess_implementation_complexity(strategy)
      base_rate = 150 # $150/hour development rate

      effort_days = IMPLEMENTATION_COMPLEXITY[complexity][:effort_days].max
      development_hours = effort_days * 8

      {
        development: (development_hours * base_rate).to_f,
        infrastructure: calculate_infrastructure_cost(strategy),
        marketing: calculate_marketing_cost(strategy),
        operational: calculate_setup_operational_cost(strategy)
      }
    end

    def calculate_infrastructure_cost(strategy)
      case strategy
      when "api_service" then 200.0
      when "real_time_streaming" then 500.0
      when "custom_analytics_dashboard" then 300.0
      else 150.0
      end
    end

    def calculate_marketing_cost(strategy)
      1000.0 # Base marketing budget
    end

    def calculate_setup_operational_cost(strategy)
      500.0 # Setup and initial operational costs
    end

    def calculate_monthly_operational_cost(strategy)
      base_cost = 50.0 # Base operational cost

      case strategy
      when "real_time_streaming" then base_cost * 3
      when "api_service" then base_cost * 2
      when "custom_analytics_dashboard" then base_cost * 1.5
      else base_cost
      end
    end

    def calculate_break_even_timeline(strategy)
      total_cost = estimate_market_entry_cost[:total_initial_cost]
      monthly_revenue = @assessor.assess_revenue_potential[:monthly_revenue_estimate]
      monthly_operational = calculate_monthly_operational_cost(strategy)

      return "Unknown - insufficient revenue projection" if monthly_revenue <= monthly_operational

      net_monthly = monthly_revenue - monthly_operational
      months_to_break_even = (total_cost / net_monthly).ceil

      "#{months_to_break_even} months"
    end

    # Additional helper methods for completeness
    def calculate_error_rate
      return 0.1 if @execution_data.empty?

      failed_executions = @execution_data.where(status: "failed").count
      total_executions = @execution_data.count

      return 0.0 if total_executions.zero?

      failed_executions.to_f / total_executions
    end

    def calculate_performance_consistency
      return 0.5 if @execution_data.empty?

      durations = @execution_data.pluck(:execution_time).compact
      return 0.5 if durations.empty? || durations.size < 3

      avg = durations.sum.to_f / durations.size
      variance = durations.map { |d| (d - avg) ** 2 }.sum / durations.size
      coefficient_of_variation = Math.sqrt(variance) / avg

      # Lower coefficient of variation = higher consistency
      [ 1.0 - coefficient_of_variation, 0.0 ].max
    end

    def assess_architecture_scalability
      # Simplified assessment based on source and destination types
      scalable_sources = %w[rest_api graphql_api webhook cloud_storage]
      scalable_destinations = %w[cloud_database api_endpoint webhook cloud_storage]

      source_score = scalable_sources.include?(@pipeline.source_type) ? 0.5 : 0.2
      dest_score = scalable_destinations.include?(@pipeline.destination_type) ? 0.5 : 0.2

      source_score + dest_score
    end

    # Placeholder methods for features not yet implemented
    def has_rate_limiting_configured?
      false # TODO: Implement when rate limiting is added
    end

    def has_data_encryption?
      true # Assume encrypted in transit/rest
    end

    def has_audit_logging?
      false # TODO: Implement when audit logging is added
    end

    def determine_request_format
      { content_type: "application/json", required_fields: %w[parameters] }
    end

    def design_response_format
      {
        success: { status: "success", data: "{}", execution_id: "uuid" },
        error: { status: "error", message: "string", code: "string" }
      }
    end

    def design_error_responses
      {
        400 => "Bad Request - Invalid parameters",
        401 => "Unauthorized - Invalid API key",
        429 => "Too Many Requests - Rate limit exceeded",
        500 => "Internal Server Error - Pipeline execution failed"
      }
    end

    def suggest_versioning_strategy
      {
        strategy: "URL versioning",
        current_version: "v1",
        deprecation_policy: "6 months notice for breaking changes"
      }
    end

    def suggest_authentication_strategy
      {
        method: "API Key",
        headers: { "X-API-Key" => "string" },
        scopes: %w[read write execute]
      }
    end
  end
end
