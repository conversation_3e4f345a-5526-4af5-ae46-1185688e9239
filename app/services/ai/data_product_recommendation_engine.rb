# frozen_string_literal: true

module Ai
  class DataProductRecommendationEngine
    include ActionView::Helpers::NumberHelper

    PRODUCT_TYPES = {
      "api_service" => {
        name: "API Service",
        description: "Real-time data access via REST API",
        complexity: "medium",
        time_to_market: "4-6 weeks",
        market_fit_indicators: %w[high_volume consistent_quality regular_usage]
      },
      "data_export" => {
        name: "Data Export Service",
        description: "Batch data downloads in various formats",
        complexity: "low",
        time_to_market: "1-2 weeks",
        market_fit_indicators: %w[valuable_transformations consistent_output]
      },
      "subscription_feed" => {
        name: "Data Subscription Feed",
        description: "Regular data delivery to customer systems",
        complexity: "medium",
        time_to_market: "3-4 weeks",
        market_fit_indicators: %w[scheduled_execution high_quality predictable_volume]
      },
      "analytics_dashboard" => {
        name: "Analytics Dashboard",
        description: "Interactive data visualization and insights",
        complexity: "high",
        time_to_market: "6-8 weeks",
        market_fit_indicators: %w[complex_transformations business_intelligence aggregated_data]
      },
      "webhook_service" => {
        name: "Webhook Service",
        description: "Real-time event notifications and data push",
        complexity: "medium",
        time_to_market: "3-5 weeks",
        market_fit_indicators: %w[real_time_processing event_driven high_frequency]
      },
      "white_label_solution" => {
        name: "White Label Solution",
        description: "Complete branded data solution for resellers",
        complexity: "very_high",
        time_to_market: "10-12 weeks",
        market_fit_indicators: %w[enterprise_quality scalable_architecture multi_tenant]
      }
    }.freeze

    MARKET_SEGMENTS = {
      "financial_services" => {
        preferred_products: %w[api_service subscription_feed webhook_service],
        pricing_sensitivity: "low",
        compliance_requirements: "high",
        data_freshness_priority: "high"
      },
      "ecommerce" => {
        preferred_products: %w[api_service analytics_dashboard webhook_service],
        pricing_sensitivity: "medium",
        compliance_requirements: "medium",
        data_freshness_priority: "high"
      },
      "marketing_analytics" => {
        preferred_products: %w[analytics_dashboard data_export subscription_feed],
        pricing_sensitivity: "high",
        compliance_requirements: "medium",
        data_freshness_priority: "medium"
      },
      "enterprise_saas" => {
        preferred_products: %w[white_label_solution api_service webhook_service],
        pricing_sensitivity: "low",
        compliance_requirements: "very_high",
        data_freshness_priority: "high"
      },
      "small_business" => {
        preferred_products: %w[data_export analytics_dashboard],
        pricing_sensitivity: "very_high",
        compliance_requirements: "low",
        data_freshness_priority: "medium"
      }
    }.freeze

    def initialize(pipeline)
      @pipeline = pipeline
      @assessor = Ai::DataValueAssessor.new(pipeline)
      @monetization_analyzer = Ai::PipelineMonetizationAnalyzer.new(pipeline)
    end

    def recommend_products
      pipeline_characteristics = analyze_pipeline_characteristics
      market_insights = @assessor.generate_market_insights
      target_market = identify_target_market_segment(market_insights)

      recommendations = []

      PRODUCT_TYPES.each do |product_type, product_info|
        fit_score = calculate_product_fit_score(product_type, pipeline_characteristics, target_market)
        next if fit_score < 0.4 # Skip low-fit products

        recommendation = create_product_recommendation(
          product_type,
          product_info,
          fit_score,
          pipeline_characteristics,
          target_market
        )

        recommendations << recommendation
      end

      # Sort by fit score and potential revenue
      recommendations.sort_by { |rec| [ -rec[:fit_score], -rec[:revenue_potential] ] }
    end

    def generate_product_specifications(product_type)
      return { error: "Unknown product type" } unless PRODUCT_TYPES.key?(product_type)

      pipeline_characteristics = analyze_pipeline_characteristics
      market_insights = @assessor.generate_market_insights

      case product_type
      when "api_service"
        generate_api_service_spec(pipeline_characteristics, market_insights)
      when "data_export"
        generate_data_export_spec(pipeline_characteristics, market_insights)
      when "subscription_feed"
        generate_subscription_feed_spec(pipeline_characteristics, market_insights)
      when "analytics_dashboard"
        generate_analytics_dashboard_spec(pipeline_characteristics, market_insights)
      when "webhook_service"
        generate_webhook_service_spec(pipeline_characteristics, market_insights)
      when "white_label_solution"
        generate_white_label_spec(pipeline_characteristics, market_insights)
      else
        { error: "Specification generation not implemented for this product type" }
      end
    end

    def create_go_to_market_strategy(product_type)
      target_market = identify_target_market_segment(@assessor.generate_market_insights)
      market_segment_info = MARKET_SEGMENTS[target_market] || MARKET_SEGMENTS["small_business"]

      {
        target_market: target_market,
        positioning_strategy: create_positioning_strategy(product_type, target_market),
        pricing_strategy: create_pricing_strategy(product_type, market_segment_info),
        marketing_channels: identify_marketing_channels(target_market),
        sales_approach: determine_sales_approach(target_market),
        competitive_differentiation: identify_competitive_advantages(product_type),
        launch_timeline: create_launch_timeline(product_type),
        success_metrics: define_success_metrics(product_type, target_market)
      }
    end

    def estimate_development_requirements(product_type)
      complexity = PRODUCT_TYPES.dig(product_type, "complexity") || "medium"
      pipeline_characteristics = analyze_pipeline_characteristics

      base_requirements = {
        "low" => {
          developer_weeks: 2,
          designer_weeks: 0.5,
          infrastructure_setup: "basic"
        },
        "medium" => {
          developer_weeks: 6,
          designer_weeks: 1,
          infrastructure_setup: "moderate"
        },
        "high" => {
          developer_weeks: 12,
          designer_weeks: 3,
          infrastructure_setup: "advanced"
        },
        "very_high" => {
          developer_weeks: 20,
          designer_weeks: 5,
          infrastructure_setup: "enterprise"
        }
      }[complexity]

      # Adjust based on pipeline complexity
      complexity_multiplier = calculate_pipeline_complexity_multiplier(pipeline_characteristics)

      {
        development_effort: {
          backend_developer_weeks: (base_requirements[:developer_weeks] * complexity_multiplier).round(1),
          frontend_developer_weeks: product_needs_frontend?(product_type) ? base_requirements[:developer_weeks] * 0.3 : 0,
          designer_weeks: base_requirements[:designer_weeks],
          devops_weeks: base_requirements[:developer_weeks] * 0.2
        },
        infrastructure_requirements: determine_infrastructure_requirements(product_type, base_requirements[:infrastructure_setup]),
        third_party_integrations: identify_required_integrations(product_type),
        compliance_requirements: assess_compliance_requirements(product_type),
        estimated_cost: calculate_development_cost(base_requirements, complexity_multiplier, product_type)
      }
    end

    def generate_feature_roadmap(product_type)
      mvp_features = identify_mvp_features(product_type)
      v2_features = identify_v2_features(product_type)
      future_features = identify_future_features(product_type)

      {
        mvp: {
          features: mvp_features,
          timeline: "4-8 weeks",
          success_criteria: define_mvp_success_criteria(product_type)
        },
        v2: {
          features: v2_features,
          timeline: "8-12 weeks after MVP",
          success_criteria: define_v2_success_criteria(product_type)
        },
        future: {
          features: future_features,
          timeline: "6+ months",
          success_criteria: define_future_success_criteria(product_type)
        }
      }
    end

    private

    def analyze_pipeline_characteristics
      execution_data = @pipeline.pipeline_executions.recent.limit(20)

      {
        data_volume: calculate_data_volume_characteristics(execution_data),
        data_quality: assess_data_quality_characteristics,
        processing_complexity: analyze_processing_complexity,
        execution_frequency: determine_execution_frequency,
        reliability: calculate_reliability_metrics(execution_data),
        scalability_factors: assess_scalability_characteristics,
        security_level: assess_security_characteristics,
        integration_complexity: assess_integration_complexity
      }
    end

    def calculate_product_fit_score(product_type, characteristics, target_market)
      fit_indicators = PRODUCT_TYPES.dig(product_type, "market_fit_indicators") || []
      market_preferences = MARKET_SEGMENTS.dig(target_market, "preferred_products") || []

      # Base score from characteristic match
      characteristic_score = calculate_characteristic_fit_score(product_type, characteristics)

      # Market preference score
      market_preference_score = market_preferences.include?(product_type) ? 0.8 : 0.4

      # Pipeline readiness score
      readiness_score = calculate_readiness_score(product_type, characteristics)

      # Weighted combination
      (characteristic_score * 0.4) + (market_preference_score * 0.3) + (readiness_score * 0.3)
    end

    def calculate_characteristic_fit_score(product_type, characteristics)
      fit_indicators = PRODUCT_TYPES.dig(product_type, "market_fit_indicators") || []
      matches = 0

      fit_indicators.each do |indicator|
        case indicator
        when "high_volume"
          matches += 1 if characteristics[:data_volume][:avg_records_per_execution] > 10_000
        when "consistent_quality"
          matches += 1 if characteristics[:data_quality][:consistency_score] > 0.8
        when "regular_usage"
          matches += 1 if characteristics[:execution_frequency] != "manual"
        when "valuable_transformations"
          matches += 1 if characteristics[:processing_complexity][:transformation_score] > 0.6
        when "consistent_output"
          matches += 1 if characteristics[:reliability][:output_consistency] > 0.7
        when "scheduled_execution"
          matches += 1 if %w[hourly daily weekly].include?(characteristics[:execution_frequency])
        when "high_quality"
          matches += 1 if characteristics[:data_quality][:overall_score] > 0.8
        when "predictable_volume"
          matches += 1 if characteristics[:data_volume][:volume_variance] < 0.3
        when "complex_transformations"
          matches += 1 if characteristics[:processing_complexity][:complexity_level] == "high"
        when "business_intelligence"
          matches += 1 if has_business_intelligence_potential?
        when "aggregated_data"
          matches += 1 if has_aggregation_capabilities?
        when "real_time_processing"
          matches += 1 if characteristics[:execution_frequency] == "real_time"
        when "event_driven"
          matches += 1 if @pipeline.source_type == "webhook"
        when "high_frequency"
          matches += 1 if characteristics[:execution_frequency] == "real_time"
        when "enterprise_quality"
          matches += 1 if characteristics[:reliability][:success_rate] > 0.95
        when "scalable_architecture"
          matches += 1 if characteristics[:scalability_factors][:architecture_score] > 0.7
        when "multi_tenant"
          matches += 1 if characteristics[:security_level][:isolation_capability] > 0.8
        end
      end

      return 0.0 if fit_indicators.empty?
      matches.to_f / fit_indicators.size
    end

    def create_product_recommendation(product_type, product_info, fit_score, characteristics, target_market)
      revenue_potential = estimate_product_revenue_potential(product_type, characteristics, target_market)
      development_effort = estimate_development_effort(product_type, characteristics)

      {
        product_type: product_type,
        name: product_info["name"],
        description: product_info["description"],
        fit_score: fit_score.round(2),
        revenue_potential: revenue_potential,
        development_effort: development_effort,
        time_to_market: product_info["time_to_market"],
        complexity: product_info["complexity"],
        market_segment: target_market,
        competitive_advantage: identify_competitive_advantage(product_type, characteristics),
        implementation_priority: calculate_implementation_priority(fit_score, revenue_potential, development_effort),
        success_probability: calculate_success_probability(fit_score, characteristics, target_market),
        key_features: identify_key_features(product_type, characteristics),
        pricing_model: suggest_pricing_model(product_type, target_market)
      }
    end

    def identify_target_market_segment(market_insights)
      category = market_insights[:category]

      case category
      when "financial_data"
        "financial_services"
      when "ecommerce_analytics"
        "ecommerce"
      when "marketing_performance", "user_engagement"
        "marketing_analytics"
      when "api_usage_data", "operational_metrics"
        "enterprise_saas"
      else
        "small_business"
      end
    end

    def generate_api_service_spec(characteristics, market_insights)
      {
        api_type: "REST API",
        authentication: "API Key + Bearer Token",
        rate_limiting: calculate_api_rate_limits(characteristics),
        endpoints: design_api_endpoints(characteristics),
        data_formats: %w[JSON CSV XML],
        caching_strategy: determine_caching_strategy(characteristics),
        monitoring: design_monitoring_requirements,
        sla_requirements: define_sla_requirements(characteristics),
        scalability_targets: define_scalability_targets(characteristics)
      }
    end

    def generate_data_export_spec(characteristics, market_insights)
      {
        export_formats: determine_export_formats(characteristics),
        delivery_methods: %w[download_link email s3_bucket ftp],
        scheduling_options: determine_scheduling_options(characteristics),
        data_packaging: design_data_packaging(characteristics),
        compression_options: %w[zip gzip],
        metadata_inclusion: design_metadata_structure,
        retention_policy: "30 days for downloads",
        size_limitations: calculate_export_size_limits(characteristics)
      }
    end

    def generate_subscription_feed_spec(characteristics, market_insights)
      {
        delivery_frequency: determine_optimal_frequency(characteristics),
        delivery_methods: %w[webhook rest_api email],
        data_format: "JSON with metadata",
        subscription_management: design_subscription_management,
        retry_logic: design_retry_strategy,
        data_validation: design_validation_rules,
        customer_customization: identify_customization_options,
        notification_system: design_notification_system
      }
    end

    # Helper methods for characteristic analysis
    def calculate_data_volume_characteristics(execution_data)
      if execution_data.empty?
        return {
          avg_records_per_execution: 0,
          volume_trend: "unknown",
          volume_variance: 0,
          peak_volume: 0
        }
      end

      records = execution_data.pluck(:records_processed).compact
      return { avg_records_per_execution: 0, volume_trend: "unknown", volume_variance: 0, peak_volume: 0 } if records.empty?

      avg_records = records.sum.to_f / records.size
      max_records = records.max
      variance = records.map { |r| (r - avg_records) ** 2 }.sum / records.size
      coefficient_variation = Math.sqrt(variance) / avg_records if avg_records > 0

      {
        avg_records_per_execution: avg_records.to_i,
        volume_trend: analyze_volume_trend(records),
        volume_variance: coefficient_variation || 0,
        peak_volume: max_records
      }
    end

    def assess_data_quality_characteristics
      quality_score = @assessor.send(:assess_data_quality)
      consistency_score = @assessor.send(:assess_data_consistency)

      {
        overall_score: quality_score,
        consistency_score: consistency_score,
        transformation_quality: analyze_transformation_quality,
        validation_coverage: assess_validation_coverage
      }
    end

    def analyze_processing_complexity
      transformation_rules = @pipeline.transformation_rules || {}
      complexity_score = @assessor.send(:analyze_transformation_complexity)

      {
        transformation_score: complexity_score,
        complexity_level: determine_complexity_level(complexity_score),
        rule_count: transformation_rules.keys.size,
        has_aggregations: transformation_rules.key?("aggregations"),
        has_enrichments: transformation_rules.key?("enrichments")
      }
    end

    def determine_execution_frequency
      case @pipeline.schedule_type
      when "real_time" then "real_time"
      when "hourly" then "hourly"
      when "daily" then "daily"
      when "weekly" then "weekly"
      else "manual"
      end
    end

    def calculate_reliability_metrics(execution_data)
      return default_reliability_metrics if execution_data.empty?

      success_rate = @pipeline.success_rate / 100.0
      output_consistency = calculate_output_consistency(execution_data)
      uptime = calculate_uptime(execution_data)

      {
        success_rate: success_rate,
        output_consistency: output_consistency,
        uptime: uptime,
        error_recovery_capability: assess_error_recovery
      }
    end

    def assess_scalability_characteristics
      source_scalability = assess_source_scalability
      architecture_score = @assessor.send(:assess_scalability_factors)

      {
        source_scalability: source_scalability,
        architecture_score: architecture_score,
        current_load_capacity: estimate_current_capacity,
        scaling_bottlenecks: identify_scaling_bottlenecks
      }
    end

    def assess_security_characteristics
      {
        current_security_level: assess_current_security_level,
        data_sensitivity: assess_data_sensitivity,
        compliance_readiness: assess_compliance_readiness,
        isolation_capability: assess_isolation_capability
      }
    end

    def assess_integration_complexity
      source_complexity = assess_source_integration_complexity
      destination_complexity = assess_destination_integration_complexity

      {
        source_complexity: source_complexity,
        destination_complexity: destination_complexity,
        overall_complexity: (source_complexity + destination_complexity) / 2.0,
        integration_points: count_integration_points
      }
    end

    # Additional helper methods (simplified implementations)
    def analyze_volume_trend(records)
      return "stable" if records.size < 3

      # Simple trend analysis
      first_half = records.first(records.size / 2)
      second_half = records.last(records.size / 2)

      first_avg = first_half.sum.to_f / first_half.size
      second_avg = second_half.sum.to_f / second_half.size

      if second_avg > first_avg * 1.1
        "growing"
      elsif second_avg < first_avg * 0.9
        "declining"
      else
        "stable"
      end
    end

    def analyze_transformation_quality
      transformation_rules = @pipeline.transformation_rules || {}
      return 0.5 if transformation_rules.empty?

      quality_score = 0.5 # Base score
      quality_score += 0.1 if transformation_rules.key?("validations")
      quality_score += 0.2 if transformation_rules.key?("enrichments")
      quality_score += 0.1 if transformation_rules.key?("field_mappings")
      quality_score += 0.1 if transformation_rules.key?("filters")

      [ quality_score, 1.0 ].min
    end

    def assess_validation_coverage
      transformation_rules = @pipeline.transformation_rules || {}
      validations = transformation_rules["validations"] || []

      return 0.3 if validations.empty?

      # Simple coverage assessment based on validation count
      [ validations.size * 0.2, 1.0 ].min
    end

    def determine_complexity_level(score)
      case score
      when 0...0.3 then "low"
      when 0.3...0.6 then "medium"
      when 0.6...0.8 then "high"
      else "very_high"
      end
    end

    def default_reliability_metrics
      {
        success_rate: 0.7,
        output_consistency: 0.5,
        uptime: 0.8,
        error_recovery_capability: 0.4
      }
    end

    def calculate_output_consistency(execution_data)
      records = execution_data.pluck(:records_processed).compact
      return 0.5 if records.empty? || records.size < 3

      mean = records.sum.to_f / records.size
      variance = records.map { |r| (r - mean) ** 2 }.sum / records.size
      coefficient_variation = Math.sqrt(variance) / mean if mean > 0

      # Lower variation = higher consistency
      consistency = 1.0 - [ coefficient_variation || 0, 1.0 ].min
      [ consistency, 0.0 ].max
    end

    def calculate_uptime(execution_data)
      return 0.8 if execution_data.empty?

      total_executions = execution_data.count
      successful_executions = execution_data.where(status: "success").count

      successful_executions.to_f / total_executions
    end

    def assess_error_recovery
      # Simplified assessment - could be enhanced with actual error recovery analysis
      0.6
    end

    def assess_source_scalability
      case @pipeline.source_type
      when "rest_api", "graphql_api" then 0.8
      when "webhook" then 0.9
      when "cloud_storage" then 0.7
      when "database" then 0.6
      else 0.5
      end
    end

    def estimate_current_capacity
      # Simplified capacity estimation
      avg_execution_time = @pipeline.pipeline_executions.recent.average(:execution_time) || 300
      executions_per_hour = 3600 / avg_execution_time

      {
        executions_per_hour: executions_per_hour.to_i,
        estimated_records_per_hour: executions_per_hour * (@pipeline.pipeline_executions.recent.average(:records_processed) || 1000)
      }
    end

    def identify_scaling_bottlenecks
      bottlenecks = []

      # Analyze common bottleneck patterns
      avg_duration = @pipeline.pipeline_executions.recent.average(:execution_time) || 0
      bottlenecks << "processing_speed" if avg_duration > 1800 # 30 minutes

      bottlenecks << "data_source" if @pipeline.source_type.in?(%w[csv_file json_file])
      bottlenecks << "transformation_complexity" if (@pipeline.transformation_rules || {}).keys.size > 5

      bottlenecks
    end

    def assess_current_security_level
      # Simplified security assessment
      base_score = 0.5
      base_score += 0.1 if @pipeline.source_config.to_s.include?("ssl")
      base_score += 0.2 if @pipeline.destination_type.in?(%w[secure_database encrypted_storage])
      base_score
    end

    def assess_data_sensitivity
      # Analyze data for PII and sensitive information indicators
      config_text = [ @pipeline.source_config.to_s, @pipeline.destination_config.to_s, @pipeline.name ].join(" ").downcase

      sensitive_indicators = %w[email phone ssn credit_card payment customer_data personal_info]
      sensitivity_score = sensitive_indicators.count { |indicator| config_text.include?(indicator) }

      [ sensitivity_score * 0.2, 1.0 ].min
    end

    def assess_compliance_readiness
      # Basic compliance readiness assessment
      has_encryption = assess_current_security_level > 0.6
      has_audit_trail = false # TODO: Implement audit trail detection
      has_data_governance = (@pipeline.transformation_rules || {}).key?("validations")

      compliance_factors = [ has_encryption, has_audit_trail, has_data_governance ].count(true)
      compliance_factors / 3.0
    end

    def assess_isolation_capability
      # Multi-tenancy isolation capability assessment
      case @pipeline.destination_type
      when "database" then 0.8
      when "cloud_storage" then 0.7
      when "api_endpoint" then 0.9
      else 0.5
      end
    end

    def assess_source_integration_complexity
      case @pipeline.source_type
      when "rest_api", "graphql_api" then 0.6
      when "webhook" then 0.4
      when "database" then 0.7
      when "csv_file", "json_file" then 0.3
      else 0.5
      end
    end

    def assess_destination_integration_complexity
      case @pipeline.destination_type
      when "database" then 0.7
      when "api_endpoint" then 0.6
      when "cloud_storage" then 0.4
      when "webhook" then 0.5
      else 0.5
      end
    end

    def count_integration_points
      integration_count = 1 # Source
      integration_count += 1 # Destination
      integration_count += (@pipeline.transformation_rules || {}).fetch("enrichments", []).size
      integration_count
    end

    def has_business_intelligence_potential?
      transformation_rules = @pipeline.transformation_rules || {}
      transformation_rules.key?("aggregations") && transformation_rules.key?("filters")
    end

    def has_aggregation_capabilities?
      transformation_rules = @pipeline.transformation_rules || {}
      transformation_rules.key?("aggregations")
    end

    def estimate_product_revenue_potential(product_type, characteristics, target_market)
      base_revenue = @assessor.assess_revenue_potential[:monthly_revenue_estimate]

      # Product type multipliers
      multipliers = {
        "api_service" => 1.0,
        "data_export" => 0.6,
        "subscription_feed" => 0.8,
        "analytics_dashboard" => 1.2,
        "webhook_service" => 1.1,
        "white_label_solution" => 2.0
      }

      product_multiplier = multipliers[product_type] || 1.0

      # Market segment adjustments
      market_multipliers = {
        "financial_services" => 1.5,
        "enterprise_saas" => 1.3,
        "ecommerce" => 1.0,
        "marketing_analytics" => 0.8,
        "small_business" => 0.6
      }

      market_multiplier = market_multipliers[target_market] || 1.0

      (base_revenue * product_multiplier * market_multiplier * 12).round(2) # Annual revenue
    end

    def estimate_development_effort(product_type, characteristics)
      base_efforts = {
        "api_service" => 6,
        "data_export" => 2,
        "subscription_feed" => 4,
        "analytics_dashboard" => 10,
        "webhook_service" => 5,
        "white_label_solution" => 16
      }

      base_effort = base_efforts[product_type] || 6
      complexity_multiplier = calculate_pipeline_complexity_multiplier(characteristics)

      (base_effort * complexity_multiplier).round(1)
    end

    def calculate_pipeline_complexity_multiplier(characteristics)
      multiplier = 1.0

      # Adjust based on various complexity factors
      multiplier += 0.2 if characteristics[:processing_complexity][:complexity_level] == "high"
      multiplier += 0.1 if characteristics[:integration_complexity][:overall_complexity] > 0.7
      multiplier += 0.15 if characteristics[:security_level][:data_sensitivity] > 0.5
      multiplier += 0.1 if characteristics[:scalability_factors][:scaling_bottlenecks].size > 2

      [ multiplier, 2.0 ].min # Cap at 2x
    end

    def product_needs_frontend?(product_type)
      %w[analytics_dashboard white_label_solution].include?(product_type)
    end

    # Placeholder methods for additional functionality
    def calculate_readiness_score(product_type, characteristics)
      # Simplified readiness scoring
      0.7
    end

    def identify_competitive_advantage(product_type, characteristics)
      advantages = []

      advantages << "High data quality" if characteristics[:data_quality][:overall_score] > 0.8
      advantages << "Real-time processing" if characteristics[:execution_frequency] == "real_time"
      advantages << "Complex transformations" if characteristics[:processing_complexity][:transformation_score] > 0.7
      advantages << "Reliable service" if characteristics[:reliability][:success_rate] > 0.9

      advantages.empty? ? [ "Competitive pricing" ] : advantages
    end

    def calculate_implementation_priority(fit_score, revenue_potential, development_effort)
      # Priority = (fit_score * revenue_potential) / development_effort
      return 0 if development_effort.zero?

      priority_score = (fit_score * revenue_potential) / (development_effort * 1000)

      case priority_score
      when 0..0.5 then "low"
      when 0.5..1.0 then "medium"
      when 1.0..2.0 then "high"
      else "very_high"
      end
    end

    def calculate_success_probability(fit_score, characteristics, target_market)
      base_probability = fit_score * 0.6

      # Adjust based on market and technical factors
      base_probability += 0.1 if characteristics[:reliability][:success_rate] > 0.9
      base_probability += 0.1 if target_market.in?(%w[financial_services enterprise_saas])
      base_probability += 0.05 if characteristics[:data_quality][:overall_score] > 0.8
      base_probability += 0.05 if characteristics[:scalability_factors][:architecture_score] > 0.7

      [ base_probability, 1.0 ].min
    end

    def identify_key_features(product_type, characteristics)
      common_features = [ "Authentication", "Rate limiting", "Monitoring", "Documentation" ]

      case product_type
      when "api_service"
        common_features + [ "RESTful endpoints", "JSON responses", "Error handling", "Caching" ]
      when "data_export"
        common_features + [ "Multiple formats", "Scheduled exports", "Download management", "Compression" ]
      when "analytics_dashboard"
        common_features + [ "Interactive charts", "Custom filters", "Export capabilities", "Sharing" ]
      else
        common_features + [ "Core functionality", "User management" ]
      end
    end

    def suggest_pricing_model(product_type, target_market)
      market_info = MARKET_SEGMENTS[target_market] || {}

      case market_info["pricing_sensitivity"]
      when "very_high"
        "Freemium with usage tiers"
      when "high"
        "Pay-per-use"
      when "medium"
        "Subscription tiers"
      when "low"
        "Enterprise licensing"
      else
        "Subscription tiers"
      end
    end

    # Additional helper methods would continue here...
    # For brevity, I'm including simplified implementations of the remaining methods

    def determine_infrastructure_requirements(product_type, setup_level)
      base_requirements = {
        "basic" => [ "Load balancer", "Application server", "Database" ],
        "moderate" => [ "Load balancer", "Application servers (2)", "Database", "Cache layer", "Monitoring" ],
        "advanced" => [ "Load balancers (2)", "Application servers (3+)", "Database cluster", "Cache layer", "Message queue", "Monitoring", "CDN" ],
        "enterprise" => [ "Multi-region setup", "Auto-scaling groups", "Database cluster", "Multiple cache layers", "Message queues", "Comprehensive monitoring", "CDN", "Security tools" ]
      }

      base_requirements[setup_level] || base_requirements["moderate"]
    end

    def identify_required_integrations(product_type)
      base_integrations = [ "Payment processing", "Authentication service", "Monitoring tools" ]

      case product_type
      when "api_service"
        base_integrations + [ "API gateway", "Rate limiting service" ]
      when "analytics_dashboard"
        base_integrations + [ "Charting library", "PDF generation" ]
      else
        base_integrations
      end
    end

    def assess_compliance_requirements(product_type)
      requirements = []

      data_sensitivity = assess_data_sensitivity

      requirements << "SOC2 Type II" if data_sensitivity > 0.5
      requirements << "GDPR compliance" if @pipeline.name.downcase.include?("eu") || data_sensitivity > 0.7
      requirements << "HIPAA compliance" if @pipeline.name.downcase.include?("health")

      requirements.empty? ? [ "Basic security standards" ] : requirements
    end

    def calculate_development_cost(base_requirements, complexity_multiplier, product_type)
      base_cost = base_requirements[:developer_weeks] * 150 * 40 # $150/hour * 40 hours/week
      adjusted_cost = base_cost * complexity_multiplier

      # Add infrastructure and other costs
      infrastructure_cost = case product_type
      when "white_label_solution" then 10000
      when "analytics_dashboard" then 5000
      when "api_service" then 3000
      else 1000
      end

      {
        development: adjusted_cost,
        infrastructure: infrastructure_cost,
        total: adjusted_cost + infrastructure_cost
      }
    end

    def identify_mvp_features(product_type)
      case product_type
      when "api_service"
        [ "Core API endpoints", "Authentication", "Basic rate limiting", "Error handling", "Documentation" ]
      when "data_export"
        [ "Export to JSON/CSV", "Basic scheduling", "Download links", "User authentication" ]
      when "analytics_dashboard"
        [ "Basic charts", "Data filtering", "Export charts", "User authentication" ]
      else
        [ "Core functionality", "User management", "Basic UI", "Documentation" ]
      end
    end

    def identify_v2_features(product_type)
      case product_type
      when "api_service"
        [ "Advanced rate limiting", "Webhooks", "SDK generation", "Analytics dashboard" ]
      when "data_export"
        [ "Advanced formatting", "Automated delivery", "Custom scheduling", "API integration" ]
      when "analytics_dashboard"
        [ "Advanced charts", "Custom dashboards", "Collaboration features", "API access" ]
      else
        [ "Enhanced UI", "Advanced features", "Integration capabilities", "Mobile support" ]
      end
    end

    def identify_future_features(product_type)
      [ "Machine learning insights", "Advanced integrations", "White-label options", "Enterprise features" ]
    end

    def define_mvp_success_criteria(product_type)
      [ "10+ active users", "Core functionality working", "<1% error rate", "Positive user feedback" ]
    end

    def define_v2_success_criteria(product_type)
      [ "100+ active users", "$10K+ monthly revenue", "Customer retention >80%", "Feature adoption >60%" ]
    end

    def define_future_success_criteria(product_type)
      [ "1000+ active users", "$100K+ monthly revenue", "Market leadership position", "Enterprise customer base" ]
    end
  end
end
