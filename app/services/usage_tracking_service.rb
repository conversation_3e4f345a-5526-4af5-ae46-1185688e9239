class UsageTrackingService
  def initialize(account)
    @account = account
  end

  # Record various usage metrics
  def record_pipeline_execution(pipeline, execution, success:)
    UsageMetric.record_metric(@account, "pipeline_executions", 1, {
      pipeline_id: pipeline.id,
      pipeline_name: pipeline.name,
      execution_id: execution.id,
      execution_time: execution.execution_time,
      rows_processed: execution.records_processed,
      success: success,
      status: execution.status
    })

    if execution.execution_time
      UsageMetric.record_metric(@account, "execution_time_minutes", execution.execution_time / 60.0)
    end

    if execution.records_processed > 0
      UsageMetric.record_metric(@account, "data_rows_processed", execution.records_processed)
    end

    # Track success/error rates
    if success
      update_success_rate
    else
      update_error_rate
    end
  end

  def record_pipeline_status_change(pipeline, new_status)
    UsageMetric.record_metric(@account, "pipeline_status_change", 1, {
      pipeline_id: pipeline.id,
      pipeline_name: pipeline.name,
      previous_status: pipeline.status_before_last_save,
      new_status: new_status
    })
  end

  def record_connector_test(connector, success:)
    UsageMetric.record_metric(@account, "connector_test", 1, {
      connector_id: connector.id,
      connector_name: connector.name,
      connector_type: connector.connector_type,
      success: success,
      test_result: connector.test_result
    })
  end

  def record_api_request
    UsageMetric.record_metric(@account, "api_requests", 1)
  end

  def record_storage_usage(megabytes)
    UsageMetric.record_metric(@account, "storage_used_mb", megabytes)
  end

  def record_active_user
    # Update monthly active users count (deduplicated)
    current_mau = get_current_metric("monthly_active_users")
    UsageMetric.record_metric(@account, "monthly_active_users", current_mau + 1) if current_mau
  end

  def update_team_members_count
    current_count = @account.users.count
    UsageMetric.record_metric(@account, "team_members", current_count)
  end

  def update_active_connections_count
    # This would be updated when connections are created/destroyed
    # For now, we'll use a placeholder
    connections_count = @account.data_connections&.active&.count || 0
    UsageMetric.record_metric(@account, "active_connections", connections_count)
  end

  # Get current billing cycle usage
  def billing_cycle_usage
    UsageMetric.usage_for_current_billing_cycle(@account)
  end

  # Get usage summary for dashboard
  def usage_summary(days = 30)
    {
      pipeline_executions: daily_trend("pipeline_executions", days),
      data_rows_processed: daily_trend("data_rows_processed", days),
      api_requests: daily_trend("api_requests", days),
      storage_used_mb: latest_value("storage_used_mb"),
      team_members: latest_value("team_members"),
      active_connections: latest_value("active_connections"),
      execution_time_avg: average_for_period("execution_time_minutes", days),
      success_rate: latest_value("success_rate"),
      error_rate: latest_value("error_rate")
    }
  end

  # Check if account is approaching plan limits
  def check_plan_limits
    return {} unless @account.subscription&.plan

    current_usage = billing_cycle_usage
    plan_limits = get_plan_limits(@account.subscription.plan)
    warnings = {}

    plan_limits.each do |metric, limit|
      usage = current_usage[metric] || 0
      percentage = (usage.to_f / limit * 100).round(1)

      if percentage >= 90
        warnings[metric] = { usage: usage, limit: limit, percentage: percentage, level: "critical" }
      elsif percentage >= 75
        warnings[metric] = { usage: usage, limit: limit, percentage: percentage, level: "warning" }
      end
    end

    warnings
  end

  private

  def update_success_rate
    # Calculate success rate based on recent executions
    recent_executions = UsageMetric.where(account: @account, metric_type: "pipeline_executions")
                                  .where("recorded_at >= ?", 1.hour.ago)

    return if recent_executions.empty?

    successful = recent_executions.select { |m| m.metadata["success"] == true }.count
    total = recent_executions.count
    success_rate = successful.to_f / total

    UsageMetric.record_metric(@account, "success_rate", success_rate)
  end

  def update_error_rate
    # Calculate error rate based on recent executions
    recent_executions = UsageMetric.where(account: @account, metric_type: "pipeline_executions")
                                  .where("recorded_at >= ?", 1.hour.ago)

    return if recent_executions.empty?

    failed = recent_executions.select { |m| m.metadata["success"] == false }.count
    total = recent_executions.count
    error_rate = failed.to_f / total

    UsageMetric.record_metric(@account, "error_rate", error_rate)
  end

  def daily_trend(metric_type, days)
    UsageMetric.daily_summary(@account, metric_type, days)
  end

  def latest_value(metric_type)
    UsageMetric.where(account: @account, metric_type: metric_type)
              .order(recorded_at: :desc)
              .first&.value || 0
  end

  def average_for_period(metric_type, days)
    start_date = Date.current - days.days
    end_date = Date.current

    UsageMetric.aggregate_for_period(@account, metric_type, start_date, end_date, :avg) || 0
  end

  def get_current_metric(metric_type)
    latest_value(metric_type)
  end

  def get_plan_limits(plan)
    case plan.to_s
    when "free"
      {
        "pipeline_executions" => 100,
        "data_rows_processed" => 10_000,
        "api_requests" => 1_000,
        "storage_used_mb" => 100,
        "team_members" => 2
      }
    when "starter"
      {
        "pipeline_executions" => 1_000,
        "data_rows_processed" => 100_000,
        "api_requests" => 10_000,
        "storage_used_mb" => 1_000,
        "team_members" => 5
      }
    when "professional"
      {
        "pipeline_executions" => 10_000,
        "data_rows_processed" => 1_000_000,
        "api_requests" => 100_000,
        "storage_used_mb" => 10_000,
        "team_members" => 25
      }
    when "enterprise"
      {
        "pipeline_executions" => Float::INFINITY,
        "data_rows_processed" => Float::INFINITY,
        "api_requests" => Float::INFINITY,
        "storage_used_mb" => Float::INFINITY,
        "team_members" => Float::INFINITY
      }
    else
      {}
    end
  end
end
