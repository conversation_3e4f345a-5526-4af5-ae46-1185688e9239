class PipelineIntelligenceService
  include ActiveModel::Model
  include ActiveModel::Attributes

  attr_accessor :pipeline, :account

  # AI Agent pricing tiers
  PRICING = {
    basic: { price: 25, features: [ "Basic optimization", "Performance monitoring" ] },
    pro: { price: 50, features: [ "Advanced optimization", "Predictive analysis", "Custom recommendations" ] },
    enterprise: { price: 150, features: [ "Real-time optimization", "Advanced AI analysis", "Priority support" ] }
  }.freeze

  def initialize(attributes = {})
    super
    @analysis_cache = {}
  end

  # Main analysis method that generates recommendations
  def analyze_pipeline
    return [] unless pipeline&.pipeline_executions&.any?

    recommendations = []

    # Performance optimization recommendations
    recommendations += analyze_performance_issues

    # Cost optimization recommendations
    recommendations += analyze_cost_optimization

    # Reliability improvements
    recommendations += analyze_reliability_issues

    # Scaling recommendations
    recommendations += analyze_scaling_opportunities

    # Filter and prioritize recommendations
    prioritize_recommendations(recommendations)
  end

  # Generate monthly report for account
  def generate_monthly_report
    pipelines = account.pipelines.active
    total_savings = 0
    total_improvements = 0

    report_data = {
      account_id: account.id,
      period: {
        start: 1.month.ago,
        end: Time.current
      },
      pipelines_analyzed: pipelines.count,
      recommendations_generated: 0,
      recommendations_implemented: 0,
      total_savings_usd: 0,
      performance_improvements: {},
      top_recommendations: []
    }

    pipelines.each do |pipeline|
      pipeline_service = self.class.new(pipeline: pipeline, account: account)
      recommendations = pipeline_service.analyze_pipeline

      report_data[:recommendations_generated] += recommendations.count

      # Track implemented recommendations
      implemented = AgentRecommendation
        .where(pipeline: pipeline, status: :implemented)
        .where("implemented_at > ?", 1.month.ago)

      report_data[:recommendations_implemented] += implemented.count

      # Calculate savings from implemented recommendations
      implemented.each do |rec|
        if rec.estimated_value.present?
          total_savings += rec.estimated_value
        end
      end

      # Add top recommendations
      report_data[:top_recommendations] += recommendations.first(3)
    end

    report_data[:total_savings_usd] = total_savings
    report_data[:top_recommendations] = report_data[:top_recommendations]
      .sort_by { |r| [ -r[:priority_score], -r[:estimated_value] ] }
      .first(10)

    # Generate revenue if this is a paid service
    generate_intelligence_revenue

    report_data
  end

  # Check if account has active intelligence subscription
  def has_active_subscription?
    AgentRevenue
      .where(account: account)
      .where(revenue_source: :intelligence_subscription)
      .where(status: :active)
      .exists?
  end

  # Subscribe account to intelligence service
  def subscribe!(tier = :basic)
    return false if has_active_subscription?

    pricing = PRICING[tier]
    return false unless pricing

    AgentRevenue.create_subscription!(
      account,
      :intelligence_subscription,
      pricing[:price],
      pipeline: nil
    )
  end

  private

  def analyze_performance_issues
    recommendations = []

    # Get recent executions
    recent_executions = pipeline.pipeline_executions
      .where("started_at > ?", 30.days.ago)
      .order(started_at: :desc)
      .limit(100)

    return recommendations if recent_executions.empty?

    # Analyze execution times
    if execution_time_degrading?(recent_executions)
      recommendations << create_recommendation(
        type: :optimization,
        title: "Pipeline execution time is increasing",
        description: "Your pipeline execution time has increased by #{calculate_execution_time_increase(recent_executions)}% over the last 30 days",
        estimated_value: 50.0, # Monthly savings from optimization
        confidence_score: 85.0,
        priority: :high,
        implementation_steps: {
          "estimated_hours" => 2,
          "steps" => [
            "Analyze query performance",
            "Optimize data transformations",
            "Review connection settings",
            "Implement caching where appropriate"
          ]
        }
      )
    end

    # Analyze failure rates
    failure_rate = calculate_failure_rate(recent_executions)
    if failure_rate > 5.0 # More than 5% failure rate
      recommendations << create_recommendation(
        type: :optimization,
        title: "High pipeline failure rate detected",
        description: "Your pipeline has a #{failure_rate.round(1)}% failure rate, which is above the recommended 2% threshold",
        estimated_value: 75.0,
        confidence_score: 90.0,
        priority: :critical,
        implementation_steps: {
          "estimated_hours" => 3,
          "steps" => [
            "Add comprehensive error handling",
            "Implement retry logic",
            "Add data validation checks",
            "Set up monitoring alerts"
          ]
        }
      )
    end

    # Memory usage analysis
    if memory_usage_high?(recent_executions)
      recommendations << create_recommendation(
        type: :optimization,
        title: "Optimize memory usage",
        description: "Pipeline is processing data in large batches that could be optimized for better memory efficiency",
        estimated_value: 30.0,
        confidence_score: 70.0,
        priority: :medium,
        implementation_steps: {
          "estimated_hours" => 1.5,
          "steps" => [
            "Implement batch processing",
            "Add streaming for large datasets",
            "Optimize data loading strategy"
          ]
        }
      )
    end

    recommendations
  end

  def analyze_cost_optimization
    recommendations = []

    # Analyze execution frequency
    executions_per_day = pipeline.pipeline_executions
      .where("started_at > ?", 7.days.ago)
      .count / 7.0

    if executions_per_day > 24 # More than once per hour
      recommendations << create_recommendation(
        type: :cost_saving,
        title: "Reduce execution frequency",
        description: "Pipeline runs #{executions_per_day.round(1)} times per day. Consider if all executions are necessary",
        estimated_value: 25.0,
        confidence_score: 60.0,
        priority: :medium,
        implementation_steps: {
          "estimated_hours" => 0.5,
          "steps" => [
            "Review business requirements for execution frequency",
            "Implement intelligent scheduling",
            "Add change detection to skip unnecessary runs"
          ]
        }
      )
    end

    # Check for expensive transformations
    avg_records = pipeline.pipeline_executions
      .where("started_at > ?", 30.days.ago)
      .average(:records_processed) || 0

    if avg_records > 100_000
      recommendations << create_recommendation(
        type: :cost_saving,
        title: "Optimize large dataset processing",
        description: "Processing #{avg_records.to_i} records on average. Optimization could reduce costs",
        estimated_value: 100.0,
        confidence_score: 75.0,
        priority: :high,
        implementation_steps: {
          "estimated_hours" => 4,
          "steps" => [
            "Implement incremental data loading",
            "Add data deduplication",
            "Optimize SQL queries",
            "Consider data partitioning"
          ]
        }
      )
    end

    recommendations
  end

  def analyze_reliability_issues
    recommendations = []

    recent_executions = pipeline.pipeline_executions
      .where("started_at > ?", 7.days.ago)
      .order(started_at: :desc)

    # Check for timeout issues
    timeout_rate = recent_executions
      .where("error_message ILIKE '%timeout%' OR error_message ILIKE '%connection%'")
      .count.to_f / recent_executions.count * 100

    if timeout_rate > 2.0
      recommendations << create_recommendation(
        type: :optimization,
        title: "Connection reliability issues",
        description: "#{timeout_rate.round(1)}% of executions fail due to connection or timeout issues",
        estimated_value: 40.0,
        confidence_score: 85.0,
        priority: :high,
        implementation_steps: {
          "estimated_hours" => 2,
          "steps" => [
            "Implement connection pooling",
            "Add retry logic with exponential backoff",
            "Optimize timeout settings",
            "Add connection health checks"
          ]
        }
      )
    end

    # Check for data quality issues
    if has_data_quality_issues?(recent_executions)
      recommendations << create_recommendation(
        type: :quality_fix,
        title: "Add data quality monitoring",
        description: "Inconsistent record processing suggests data quality issues",
        estimated_value: 60.0,
        confidence_score: 70.0,
        priority: :medium,
        implementation_steps: {
          "estimated_hours" => 3,
          "steps" => [
            "Add data validation rules",
            "Implement data profiling",
            "Set up quality alerts",
            "Add data cleansing steps"
          ]
        }
      )
    end

    recommendations
  end

  def analyze_scaling_opportunities
    recommendations = []

    # Growth trend analysis
    monthly_growth = calculate_monthly_growth

    if monthly_growth > 20.0 # More than 20% growth
      recommendations << create_recommendation(
        type: :scaling,
        title: "Prepare for scale",
        description: "Data volume growing #{monthly_growth.round(1)}% monthly. Scale optimization recommended",
        estimated_value: 200.0,
        confidence_score: 80.0,
        priority: :high,
        implementation_steps: {
          "estimated_hours" => 8,
          "steps" => [
            "Implement horizontal scaling",
            "Add load balancing",
            "Optimize for parallel processing",
            "Set up auto-scaling triggers",
            "Performance testing for scale"
          ]
        }
      )
    end

    recommendations
  end

  def create_recommendation(params)
    {
      pipeline_id: pipeline.id,
      recommendation_type: params[:type],
      title: params[:title],
      description: params[:description],
      estimated_value: params[:estimated_value],
      confidence_score: params[:confidence_score],
      priority: params[:priority],
      implementation_steps: params[:implementation_steps],
      priority_score: calculate_priority_score(params)
    }
  end

  def calculate_priority_score(params)
    # Weighted scoring: value (40%) + confidence (30%) + priority (30%)
    value_score = (params[:estimated_value] || 0) / 200.0 * 100
    confidence_score = params[:confidence_score] || 0
    priority_scores = { low: 20, medium: 50, high: 80, critical: 100 }
    priority_score = priority_scores[params[:priority]] || 50

    (value_score * 0.4 + confidence_score * 0.3 + priority_score * 0.3).round(2)
  end

  def prioritize_recommendations(recommendations)
    recommendations
      .sort_by { |r| [ -r[:priority_score], -r[:estimated_value] ] }
      .map { |rec_data| create_agent_recommendation(rec_data) }
  end

  def create_agent_recommendation(rec_data)
    AgentRecommendation.create!(
      account: account,
      pipeline: pipeline,
      recommendation_type: rec_data[:recommendation_type],
      title: rec_data[:title],
      description: rec_data[:description],
      estimated_value: rec_data[:estimated_value],
      confidence_score: rec_data[:confidence_score],
      priority: rec_data[:priority],
      implementation_steps: rec_data[:implementation_steps],
      ai_analysis: {
        generated_at: Time.current,
        analysis_version: "1.0",
        priority_score: rec_data[:priority_score]
      },
      before_metrics: capture_current_metrics
    )
  end

  def capture_current_metrics
    recent_execution = pipeline.pipeline_executions.recent.first
    return {} unless recent_execution

    {
      avg_execution_time: pipeline.avg_execution_time,
      success_rate: calculate_success_rate,
      avg_records_processed: recent_execution.records_processed,
      last_execution_status: recent_execution.status,
      captured_at: Time.current
    }
  end

  # Helper methods for analysis
  def execution_time_degrading?(executions)
    return false if executions.count < 10

    recent_avg = executions.first(5).average(:execution_time) || 0
    older_avg = executions.last(5).average(:execution_time) || 0

    return false if older_avg.zero?

    ((recent_avg - older_avg) / older_avg) > 0.2 # 20% increase
  end

  def calculate_execution_time_increase(executions)
    recent_avg = executions.first(5).average(:execution_time) || 0
    older_avg = executions.last(5).average(:execution_time) || 0

    return 0 if older_avg.zero?

    (((recent_avg - older_avg) / older_avg) * 100).round(1)
  end

  def calculate_failure_rate(executions)
    return 0 if executions.empty?

    failed_count = executions.where.not(status: :success).count
    (failed_count.to_f / executions.count * 100).round(1)
  end

  def calculate_success_rate
    recent_executions = pipeline.pipeline_executions
      .where("started_at > ?", 30.days.ago)

    return 100.0 if recent_executions.empty?

    successful_count = recent_executions.where(status: :success).count
    (successful_count.to_f / recent_executions.count * 100).round(1)
  end

  def memory_usage_high?(executions)
    # Simplified heuristic - in production would analyze actual memory usage
    avg_records = executions.average(:records_processed) || 0
    avg_time = executions.average(:execution_time) || 0

    # If processing relatively few records but taking long time, might be memory issue
    return false if avg_records.zero? || avg_time.zero?

    records_per_second = avg_records / avg_time
    records_per_second < 100 && avg_records > 10_000
  end

  def has_data_quality_issues?(executions)
    # Look for patterns that suggest data quality issues
    return false if executions.count < 5

    # Check for high variance in records processed
    records_processed = executions.pluck(:records_processed)
    return false if records_processed.empty?

    avg_records = records_processed.sum.to_f / records_processed.count
    variance = records_processed.sum { |r| (r - avg_records) ** 2 } / records_processed.count
    coefficient_of_variation = Math.sqrt(variance) / avg_records

    coefficient_of_variation > 0.5 # High variance suggests inconsistent data
  end

  def calculate_monthly_growth
    current_month = pipeline.pipeline_executions
      .where("started_at > ?", 1.month.ago)
      .sum(:records_processed)

    previous_month = pipeline.pipeline_executions
      .where("started_at BETWEEN ? AND ?", 2.months.ago, 1.month.ago)
      .sum(:records_processed)

    return 0 if previous_month.zero?

    ((current_month - previous_month).to_f / previous_month * 100).round(1)
  end

  def generate_intelligence_revenue
    return unless has_active_subscription?

    # This would be called monthly to generate recurring revenue
    # Implementation depends on billing cycle
    Rails.logger.info "Intelligence service revenue generated for account #{account.id}"
  end
end
