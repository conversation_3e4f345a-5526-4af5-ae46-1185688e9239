class DataQualityGuardService
  include ActiveModel::Model
  include ActiveModel::Attributes

  attr_accessor :pipeline, :account

  # Quality Guard pricing tiers
  PRICING = {
    basic: {
      price: 15,
      features: [ "Basic quality monitoring", "Email alerts", "Quality reports" ]
    },
    standard: {
      price: 25,
      features: [ "Advanced monitoring", "Real-time alerts", "Data profiling", "Quality rules" ]
    },
    premium: {
      price: 40,
      features: [ "ML-based anomaly detection", "Auto-remediation", "Custom quality metrics", "24/7 monitoring" ]
    }
  }.freeze

  # Data quality dimensions
  QUALITY_DIMENSIONS = %w[
    completeness
    accuracy
    consistency
    validity
    uniqueness
    timeliness
    integrity
  ].freeze

  def initialize(attributes = {})
    super
    @quality_cache = {}
  end

  # Main quality analysis method
  def analyze_data_quality
    return [] unless pipeline&.pipeline_executions&.any?

    recommendations = []

    # Analyze each quality dimension
    QUALITY_DIMENSIONS.each do |dimension|
      issues = analyze_dimension(dimension)
      recommendations += issues if issues.any?
    end

    # Generate overall quality score
    quality_score = calculate_overall_quality_score

    # Add quality monitoring recommendations
    recommendations += suggest_quality_monitoring_improvements(quality_score)

    # Prioritize by impact and confidence
    prioritize_quality_recommendations(recommendations)
  end

  # Real-time quality monitoring
  def monitor_execution(execution)
    return unless has_active_monitoring?

    quality_issues = []

    # Check data completeness
    completeness_issues = check_completeness(execution)
    quality_issues += completeness_issues if completeness_issues.any?

    # Check data accuracy patterns
    accuracy_issues = check_accuracy_patterns(execution)
    quality_issues += accuracy_issues if accuracy_issues.any?

    # Check for anomalies
    anomalies = detect_anomalies(execution)
    quality_issues += anomalies if anomalies.any?

    # Send alerts if critical issues found
    send_quality_alerts(quality_issues) if quality_issues.any?

    # Log quality metrics
    log_quality_metrics(execution, quality_issues)

    quality_issues
  end

  # Generate quality report for account
  def generate_quality_report(period = 30.days)
    pipelines = account.pipelines.active

    report_data = {
      account_id: account.id,
      period: {
        start: period.ago,
        end: Time.current
      },
      overall_quality_score: 0,
      pipelines_monitored: pipelines.count,
      quality_dimensions: {},
      critical_issues: [],
      recommendations: [],
      trends: {}
    }

    total_quality_score = 0

    pipelines.each do |pipeline|
      quality_service = self.class.new(pipeline: pipeline, account: account)
      pipeline_quality = quality_service.calculate_pipeline_quality_score
      total_quality_score += pipeline_quality

      # Analyze quality dimensions for this pipeline
      QUALITY_DIMENSIONS.each do |dimension|
        dimension_score = quality_service.analyze_dimension_score(dimension)
        report_data[:quality_dimensions][dimension] ||= []
        report_data[:quality_dimensions][dimension] << {
          pipeline_id: pipeline.id,
          score: dimension_score
        }
      end

      # Get recommendations
      recommendations = quality_service.analyze_data_quality
      report_data[:recommendations] += recommendations.first(3) # Top 3 per pipeline
    end

    # Calculate overall quality score
    report_data[:overall_quality_score] = pipelines.any? ?
      (total_quality_score / pipelines.count).round(2) : 0

    # Calculate quality trends
    report_data[:trends] = calculate_quality_trends(period)

    # Generate revenue for quality monitoring
    generate_quality_revenue

    report_data
  end

  # Subscribe to quality monitoring
  def subscribe!(tier = :basic)
    return false if has_active_monitoring?

    pricing = PRICING[tier]
    return false unless pricing

    AgentRevenue.create_subscription!(
      account,
      :quality_monitoring,
      pricing[:price],
      pipeline: pipeline
    )
  end

  # Check if account has active monitoring
  def has_active_monitoring?
    AgentRevenue
      .where(account: account)
      .where(revenue_source: :quality_monitoring)
      .where(status: :active)
      .exists?
  end

  private

  def analyze_dimension(dimension)
    case dimension
    when "completeness"
      analyze_completeness
    when "accuracy"
      analyze_accuracy
    when "consistency"
      analyze_consistency
    when "validity"
      analyze_validity
    when "uniqueness"
      analyze_uniqueness
    when "timeliness"
      analyze_timeliness
    when "integrity"
      analyze_integrity
    else
      []
    end
  end

  def analyze_completeness
    recommendations = []

    # Analyze recent executions for completeness issues
    recent_executions = pipeline.pipeline_executions
      .where("started_at > ?", 30.days.ago)
      .order(started_at: :desc)
      .limit(50)

    return recommendations if recent_executions.empty?

    # Check for records with null/empty values
    null_rate = calculate_null_rate(recent_executions)

    if null_rate > 15.0 # More than 15% null values
      recommendations << create_quality_recommendation(
        title: "High null value rate detected",
        description: "#{null_rate.round(1)}% of records contain null/empty values, affecting data completeness",
        dimension: "completeness",
        severity: null_rate > 30 ? :critical : :high,
        estimated_value: 35.0,
        confidence_score: 85.0,
        remediation_steps: [
          "Add data validation at source",
          "Implement default value strategies",
          "Add data cleansing rules",
          "Set up null value monitoring alerts"
        ]
      )
    end

    # Check for incomplete record processing
    processing_variance = calculate_processing_variance(recent_executions)
    if processing_variance > 20.0
      recommendations << create_quality_recommendation(
        title: "Inconsistent record processing",
        description: "Record processing varies by #{processing_variance.round(1)}%, suggesting incomplete data loads",
        dimension: "completeness",
        severity: :medium,
        estimated_value: 25.0,
        confidence_score: 70.0,
        remediation_steps: [
          "Add data validation checks",
          "Implement transaction consistency",
          "Add processing checkpoints",
          "Monitor data source stability"
        ]
      )
    end

    recommendations
  end

  def analyze_accuracy
    recommendations = []

    # Look for patterns that suggest accuracy issues
    recent_executions = pipeline.pipeline_executions
      .where("started_at > ?", 30.days.ago)
      .where.not(status: :success)

    if recent_executions.any?
      error_patterns = analyze_error_patterns(recent_executions)

      if error_patterns[:data_type_errors] > 5
        recommendations << create_quality_recommendation(
          title: "Data type accuracy issues",
          description: "#{error_patterns[:data_type_errors]} executions failed due to data type mismatches",
          dimension: "accuracy",
          severity: :high,
          estimated_value: 45.0,
          confidence_score: 90.0,
          remediation_steps: [
            "Add data type validation",
            "Implement schema enforcement",
            "Add type conversion rules",
            "Set up accuracy monitoring"
          ]
        )
      end
    end

    recommendations
  end

  def analyze_consistency
    recommendations = []

    # Check for format consistency issues
    format_variance = calculate_format_variance

    if format_variance > 25.0
      recommendations << create_quality_recommendation(
        title: "Data format consistency issues",
        description: "Data format varies significantly across executions (#{format_variance.round(1)}% variance)",
        dimension: "consistency",
        severity: :medium,
        estimated_value: 30.0,
        confidence_score: 75.0,
        remediation_steps: [
          "Standardize data formats",
          "Add format validation rules",
          "Implement data normalization",
          "Create format consistency checks"
        ]
      )
    end

    recommendations
  end

  def analyze_validity
    recommendations = []

    # Check for validation rule violations
    validation_failures = count_validation_failures

    if validation_failures > 10
      recommendations << create_quality_recommendation(
        title: "Data validation failures detected",
        description: "#{validation_failures} validation failures in recent executions",
        dimension: "validity",
        severity: :high,
        estimated_value: 40.0,
        confidence_score: 85.0,
        remediation_steps: [
          "Review and update validation rules",
          "Add business rule validation",
          "Implement data quality gates",
          "Set up validation monitoring"
        ]
      )
    end

    recommendations
  end

  def analyze_uniqueness
    recommendations = []

    # Check for duplicate processing
    duplicate_rate = calculate_duplicate_processing_rate

    if duplicate_rate > 5.0
      recommendations << create_quality_recommendation(
        title: "Duplicate data processing detected",
        description: "#{duplicate_rate.round(1)}% of records appear to be duplicates",
        dimension: "uniqueness",
        severity: :medium,
        estimated_value: 35.0,
        confidence_score: 80.0,
        remediation_steps: [
          "Add deduplication logic",
          "Implement unique key constraints",
          "Add duplicate detection rules",
          "Set up uniqueness monitoring"
        ]
      )
    end

    recommendations
  end

  def analyze_timeliness
    recommendations = []

    # Check for data freshness issues
    avg_processing_delay = calculate_average_processing_delay

    if avg_processing_delay > 24.hours
      recommendations << create_quality_recommendation(
        title: "Data timeliness issues",
        description: "Average data processing delay is #{(avg_processing_delay / 1.hour).round(1)} hours",
        dimension: "timeliness",
        severity: :medium,
        estimated_value: 25.0,
        confidence_score: 75.0,
        remediation_steps: [
          "Optimize execution schedule",
          "Implement real-time processing",
          "Add timeliness monitoring",
          "Reduce processing bottlenecks"
        ]
      )
    end

    recommendations
  end

  def analyze_integrity
    recommendations = []

    # Check for referential integrity issues
    integrity_violations = count_integrity_violations

    if integrity_violations > 0
      recommendations << create_quality_recommendation(
        title: "Data integrity violations",
        description: "#{integrity_violations} potential integrity violations detected",
        dimension: "integrity",
        severity: :high,
        estimated_value: 50.0,
        confidence_score: 85.0,
        remediation_steps: [
          "Add referential integrity checks",
          "Implement foreign key validation",
          "Add relationship consistency rules",
          "Set up integrity monitoring"
        ]
      )
    end

    recommendations
  end

  def create_quality_recommendation(params)
    {
      pipeline_id: pipeline.id,
      recommendation_type: :quality_fix,
      title: params[:title],
      description: params[:description],
      estimated_value: params[:estimated_value],
      confidence_score: params[:confidence_score],
      priority: params[:severity],
      quality_dimension: params[:dimension],
      implementation_steps: {
        "estimated_hours" => calculate_remediation_hours(params[:remediation_steps]),
        "steps" => params[:remediation_steps],
        "dimension" => params[:dimension]
      }
    }
  end

  def calculate_remediation_hours(steps)
    # Simple estimation based on number of steps
    base_hours = steps.count * 0.5
    [ base_hours, 0.5 ].max.round(1)
  end

  def calculate_overall_quality_score
    dimension_scores = QUALITY_DIMENSIONS.map do |dimension|
      analyze_dimension_score(dimension)
    end.compact

    return 0 if dimension_scores.empty?

    (dimension_scores.sum / dimension_scores.count).round(2)
  end

  def analyze_dimension_score(dimension)
    case dimension
    when "completeness"
      [ 100 - calculate_null_rate, 0 ].max
    when "accuracy"
      calculate_accuracy_score
    when "consistency"
      [ 100 - calculate_format_variance, 0 ].max
    when "validity"
      calculate_validity_score
    when "uniqueness"
      [ 100 - calculate_duplicate_processing_rate, 0 ].max
    when "timeliness"
      calculate_timeliness_score
    when "integrity"
      calculate_integrity_score
    else
      50 # Default neutral score
    end
  end

  def calculate_pipeline_quality_score
    calculate_overall_quality_score
  end

  def suggest_quality_monitoring_improvements(quality_score)
    recommendations = []

    if quality_score < 70
      recommendations << create_quality_recommendation(
        title: "Implement comprehensive quality monitoring",
        description: "Overall quality score of #{quality_score}% indicates need for enhanced monitoring",
        dimension: "overall",
        severity: :high,
        estimated_value: 100.0,
        confidence_score: 95.0,
        remediation_steps: [
          "Set up automated quality monitoring",
          "Implement quality gates in pipeline",
          "Add real-time quality alerts",
          "Create quality dashboards",
          "Establish quality SLAs"
        ]
      )
    end

    recommendations
  end

  def prioritize_quality_recommendations(recommendations)
    recommendations
      .sort_by { |r| [ -priority_weight(r[:priority]), -r[:confidence_score] ] }
      .map { |rec_data| create_agent_recommendation(rec_data) }
  end

  def priority_weight(priority)
    { critical: 100, high: 80, medium: 60, low: 40 }[priority] || 50
  end

  def create_agent_recommendation(rec_data)
    AgentRecommendation.create!(
      account: account,
      pipeline: pipeline,
      recommendation_type: rec_data[:recommendation_type],
      title: rec_data[:title],
      description: rec_data[:description],
      estimated_value: rec_data[:estimated_value],
      confidence_score: rec_data[:confidence_score],
      priority: rec_data[:priority],
      implementation_steps: rec_data[:implementation_steps],
      ai_analysis: {
        generated_at: Time.current,
        analysis_version: "1.0",
        quality_dimension: rec_data[:quality_dimension],
        service: "DataQualityGuardService"
      },
      before_metrics: capture_quality_metrics
    )
  end

  def capture_quality_metrics
    {
      overall_quality_score: calculate_overall_quality_score,
      completeness_score: analyze_dimension_score("completeness"),
      accuracy_score: analyze_dimension_score("accuracy"),
      consistency_score: analyze_dimension_score("consistency"),
      captured_at: Time.current
    }
  end

  # Helper methods for quality calculations
  def calculate_null_rate(executions = nil)
    executions ||= pipeline.pipeline_executions.recent.limit(20)
    return 0 if executions.empty?

    # Simplified calculation - in production would analyze actual data
    failed_records = executions.sum(:records_failed)
    total_records = executions.sum(:records_processed)

    return 0 if total_records.zero?

    (failed_records.to_f / total_records * 100).round(1)
  end

  def calculate_processing_variance(executions)
    records = executions.pluck(:records_processed).reject(&:zero?)
    return 0 if records.empty?

    mean = records.sum.to_f / records.count
    variance = records.sum { |r| (r - mean) ** 2 } / records.count
    coefficient_of_variation = Math.sqrt(variance) / mean * 100

    coefficient_of_variation.round(1)
  end

  def analyze_error_patterns(failed_executions)
    patterns = {
      data_type_errors: 0,
      constraint_violations: 0,
      format_errors: 0,
      timeout_errors: 0
    }

    failed_executions.each do |execution|
      error_msg = execution.error_message&.downcase || ""

      patterns[:data_type_errors] += 1 if error_msg.match?(/type|cast|convert/)
      patterns[:constraint_violations] += 1 if error_msg.match?(/constraint|unique|foreign/)
      patterns[:format_errors] += 1 if error_msg.match?(/format|parse|invalid/)
      patterns[:timeout_errors] += 1 if error_msg.match?(/timeout|connection/)
    end

    patterns
  end

  def calculate_format_variance
    # Simplified calculation - would analyze actual data formats in production
    recent_executions = pipeline.pipeline_executions.recent.limit(10)
    return 0 if recent_executions.count < 5

    # Use execution time variance as proxy for format consistency
    times = recent_executions.pluck(:execution_time).compact
    return 0 if times.empty?

    mean_time = times.sum / times.count.to_f
    variance = times.sum { |t| (t - mean_time) ** 2 } / times.count
    coefficient_of_variation = Math.sqrt(variance) / mean_time * 100

    [ coefficient_of_variation.round(1), 100 ].min
  end

  def count_validation_failures
    # Count executions with validation-related errors
    pipeline.pipeline_executions
      .where("started_at > ?", 30.days.ago)
      .where("error_message ILIKE '%validation%' OR error_message ILIKE '%invalid%'")
      .count
  end

  def calculate_duplicate_processing_rate
    # Simplified calculation - analyze processing patterns
    recent_executions = pipeline.pipeline_executions
      .where("started_at > ?", 7.days.ago)

    return 0 if recent_executions.count < 3

    # Look for executions with identical record counts (potential duplicates)
    record_counts = recent_executions.pluck(:records_processed)
    duplicates = record_counts.group_by(&:itself)
      .select { |_, v| v.count > 1 }
      .values
      .sum(&:count)

    (duplicates.to_f / recent_executions.count * 100).round(1)
  end

  def calculate_average_processing_delay
    recent_executions = pipeline.pipeline_executions
      .where("started_at > ?", 7.days.ago)
      .order(:started_at)

    return 0 if recent_executions.count < 2

    delays = recent_executions.each_cons(2).map do |prev, curr|
      curr.started_at - prev.completed_at if prev.completed_at
    end.compact

    return 0 if delays.empty?

    delays.sum / delays.count
  end

  def count_integrity_violations
    # Count executions with integrity-related errors
    pipeline.pipeline_executions
      .where("started_at > ?", 30.days.ago)
      .where("error_message ILIKE '%integrity%' OR error_message ILIKE '%constraint%' OR error_message ILIKE '%foreign%'")
      .count
  end

  def calculate_accuracy_score
    error_rate = calculate_null_rate
    [ 100 - error_rate, 0 ].max
  end

  def calculate_validity_score
    failures = count_validation_failures
    recent_count = pipeline.pipeline_executions.where("started_at > ?", 30.days.ago).count

    return 100 if recent_count.zero?

    failure_rate = (failures.to_f / recent_count * 100)
    [ 100 - failure_rate, 0 ].max
  end

  def calculate_timeliness_score
    delay_hours = calculate_average_processing_delay / 1.hour

    # Score decreases as delay increases
    case delay_hours
    when 0..1 then 100
    when 1..4 then 90
    when 4..12 then 75
    when 12..24 then 60
    else 40
    end
  end

  def calculate_integrity_score
    violations = count_integrity_violations
    recent_count = pipeline.pipeline_executions.where("started_at > ?", 30.days.ago).count

    return 100 if recent_count.zero?

    violation_rate = (violations.to_f / recent_count * 100)
    [ 100 - violation_rate, 0 ].max
  end

  def check_completeness(execution)
    issues = []

    if execution.records_failed > execution.records_processed * 0.1
      issues << {
        type: :completeness,
        severity: :high,
        message: "High failure rate: #{execution.records_failed}/#{execution.records_processed} records failed"
      }
    end

    issues
  end

  def check_accuracy_patterns(execution)
    issues = []

    if execution.error_message&.match?(/type|cast|format/)
      issues << {
        type: :accuracy,
        severity: :medium,
        message: "Data type or format issues detected in execution"
      }
    end

    issues
  end

  def detect_anomalies(execution)
    issues = []

    # Simple anomaly detection - compare with recent averages
    recent_avg = pipeline.pipeline_executions
      .where("started_at > ?", 7.days.ago)
      .where.not(id: execution.id)
      .average(:execution_time)

    if recent_avg && execution.execution_time > recent_avg * 2
      issues << {
        type: :performance_anomaly,
        severity: :medium,
        message: "Execution time #{execution.execution_time}s is significantly higher than average #{recent_avg.round(1)}s"
      }
    end

    issues
  end

  def send_quality_alerts(quality_issues)
    critical_issues = quality_issues.select { |issue| issue[:severity] == :high }

    return unless critical_issues.any?

    # In production, this would send actual alerts via email, Slack, etc.
    Rails.logger.warn "Quality Alert for Pipeline #{pipeline.id}: #{critical_issues.count} critical issues detected"
  end

  def log_quality_metrics(execution, quality_issues)
    # Log quality metrics for monitoring and reporting
    UsageMetric.create!(
      account: account,
      metric_type: "data_quality_check",
      value: quality_issues.count,
      recorded_at: Time.current,
      metadata: {
        pipeline_id: pipeline.id,
        execution_id: execution.id,
        issues: quality_issues.map { |i| i[:type] }
      }
    )
  end

  def calculate_quality_trends(period)
    # Calculate quality trends over the period
    {
      quality_score_trend: "improving", # Simplified
      most_common_issues: [ "completeness", "timeliness" ],
      resolution_rate: 85.0
    }
  end

  def generate_quality_revenue
    return unless has_active_monitoring?

    # Monthly revenue generation for quality monitoring
    Rails.logger.info "Quality monitoring revenue generated for account #{account.id}"
  end
end
