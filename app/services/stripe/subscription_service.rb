# frozen_string_literal: true

module Stripe
  class SubscriptionService
    class Error < StandardError; end
    class CustomerNotFoundError < Error; end
    class SubscriptionCreationError < Error; end

    def initialize(account)
      @account = account
    end

    def create_customer
      customer = ::Stripe::Customer.create(
        email: @account.users.first&.email,
        name: @account.name,
        metadata: {
          account_id: @account.id,
          subdomain: @account.subdomain
        }
      )

      @account.subscription.update!(stripe_customer_id: customer.id)
      customer
    rescue ::Stripe::StripeError => e
      Rails.logger.error "Failed to create Stripe customer for account #{@account.id}: #{e.message}"
      raise CustomerNotFoundError, e.message
    end

    def create_subscription(price_id, trial_days: 14)
      customer = find_or_create_customer
      subscription_plan = SubscriptionPlan.find_by(stripe_price_id: price_id)

      stripe_subscription = ::Stripe::Subscription.create(
        customer: customer.id,
        items: [ { price: price_id } ],
        trial_period_days: trial_days,
        metadata: {
          account_id: @account.id,
          subdomain: @account.subdomain
        },
        expand: [ "latest_invoice.payment_intent" ]
      )

      @account.subscription.update!(
        stripe_subscription_id: stripe_subscription.id,
        subscription_plan: subscription_plan,
        status: map_stripe_status(stripe_subscription.status),
        current_period_start: Time.at(stripe_subscription.current_period_start),
        current_period_end: Time.at(stripe_subscription.current_period_end),
        trial_start: trial_days > 0 ? Time.at(stripe_subscription.trial_start) : nil,
        trial_end: trial_days > 0 ? Time.at(stripe_subscription.trial_end) : nil
      )

      stripe_subscription
    rescue ::Stripe::StripeError => e
      Rails.logger.error "Failed to create subscription for account #{@account.id}: #{e.message}"
      raise SubscriptionCreationError, e.message
    end

    def cancel_subscription
      return unless @account.subscription.stripe_subscription_id

      stripe_subscription = ::Stripe::Subscription.update(
        @account.subscription.stripe_subscription_id,
        { cancel_at_period_end: true }
      )

      @account.subscription.update!(
        status: :canceled,
        canceled_at: Time.current
      )

      stripe_subscription
    rescue ::Stripe::StripeError => e
      Rails.logger.error "Failed to cancel subscription for account #{@account.id}: #{e.message}"
      raise Error, e.message
    end

    def update_subscription(new_price_id)
      return unless @account.subscription.stripe_subscription_id

      subscription = ::Stripe::Subscription.retrieve(@account.subscription.stripe_subscription_id)
      subscription_plan = SubscriptionPlan.find_by(stripe_price_id: new_price_id)

      updated_subscription = ::Stripe::Subscription.update(
        subscription.id,
        {
          items: [
            {
              id: subscription.items.data[0].id,
              price: new_price_id
            }
          ],
          proration_behavior: "always_invoice",
          metadata: {
            account_id: @account.id,
            subdomain: @account.subdomain
          }
        }
      )

      @account.subscription.update!(
        subscription_plan: subscription_plan,
        current_period_start: Time.at(updated_subscription.current_period_start),
        current_period_end: Time.at(updated_subscription.current_period_end)
      )

      updated_subscription
    rescue ::Stripe::StripeError => e
      Rails.logger.error "Failed to update subscription for account #{@account.id}: #{e.message}"
      raise Error, e.message
    end

    def retrieve_subscription
      return unless @account.subscription.stripe_subscription_id

      ::Stripe::Subscription.retrieve(@account.subscription.stripe_subscription_id)
    rescue ::Stripe::StripeError => e
      Rails.logger.error "Failed to retrieve subscription for account #{@account.id}: #{e.message}"
      nil
    end

    private

    def find_or_create_customer
      if @account.subscription.stripe_customer_id
        ::Stripe::Customer.retrieve(@account.subscription.stripe_customer_id)
      else
        create_customer
      end
    rescue ::Stripe::InvalidRequestError
      create_customer
    end

    def map_stripe_status(stripe_status)
      case stripe_status
      when "trialing"
        :trialing
      when "active"
        :active
      when "past_due"
        :past_due
      when "canceled", "unpaid"
        :canceled
      else
        :canceled
      end
    end
  end
end
