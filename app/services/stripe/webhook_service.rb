# frozen_string_literal: true

module Stripe
  class WebhookService
    class Error < StandardError; end
    class InvalidSignatureError < Error; end
    class UnknownEventError < Error; end

    def initialize(payload, signature)
      @payload = payload
      @signature = signature
      @event = construct_event
    end

    def process!
      # Record the webhook for idempotency
      webhook_record = StripeWebhook.find_or_create_by(event_id: @event.id) do |webhook|
        webhook.event_type = @event.type
        webhook.data = @event.data.to_h
        webhook.processed = false
      end

      # Skip if already processed
      return if webhook_record.processed?

      Rails.logger.info "Processing Stripe webhook: #{@event.type} (#{@event.id})"

      case @event.type
      when "customer.subscription.created"
        handle_subscription_created
      when "customer.subscription.updated"
        handle_subscription_updated
      when "customer.subscription.deleted"
        handle_subscription_deleted
      when "invoice.payment_succeeded"
        handle_payment_succeeded
      when "invoice.payment_failed"
        handle_payment_failed
      when "customer.subscription.trial_will_end"
        handle_trial_ending
      else
        Rails.logger.warn "Unhandled Stripe webhook event type: #{@event.type}"
      end

      webhook_record.mark_as_processed!
    rescue StandardError => e
      Rails.logger.error "Failed to process Stripe webhook #{@event.id}: #{e.message}"
      raise Error, e.message
    end

    private

    def construct_event
      ::Stripe::Webhook.construct_event(
        @payload,
        @signature,
        Rails.configuration.stripe[:webhook_secret]
      )
    rescue ::Stripe::SignatureVerificationError => e
      Rails.logger.error "Invalid Stripe webhook signature: #{e.message}"
      raise InvalidSignatureError, e.message
    end

    def handle_subscription_created
      stripe_subscription = @event.data.object
      account = find_account_by_customer_id(stripe_subscription.customer)

      return unless account

      subscription_plan = SubscriptionPlan.find_by(stripe_price_id: stripe_subscription.items.data[0].price.id)

      account.subscription.update!(
        stripe_subscription_id: stripe_subscription.id,
        subscription_plan: subscription_plan,
        status: map_stripe_status(stripe_subscription.status),
        current_period_start: Time.at(stripe_subscription.current_period_start),
        current_period_end: Time.at(stripe_subscription.current_period_end),
        trial_start: stripe_subscription.trial_start ? Time.at(stripe_subscription.trial_start) : nil,
        trial_end: stripe_subscription.trial_end ? Time.at(stripe_subscription.trial_end) : nil
      )

      Rails.logger.info "Updated subscription for account #{account.id} - subscription created"
    end

    def handle_subscription_updated
      stripe_subscription = @event.data.object
      account = find_account_by_customer_id(stripe_subscription.customer)

      return unless account

      subscription_plan = SubscriptionPlan.find_by(stripe_price_id: stripe_subscription.items.data[0].price.id)

      account.subscription.update!(
        subscription_plan: subscription_plan,
        status: map_stripe_status(stripe_subscription.status),
        current_period_start: Time.at(stripe_subscription.current_period_start),
        current_period_end: Time.at(stripe_subscription.current_period_end),
        trial_start: stripe_subscription.trial_start ? Time.at(stripe_subscription.trial_start) : nil,
        trial_end: stripe_subscription.trial_end ? Time.at(stripe_subscription.trial_end) : nil
      )

      Rails.logger.info "Updated subscription for account #{account.id} - subscription updated"
    end

    def handle_subscription_deleted
      stripe_subscription = @event.data.object
      account = find_account_by_customer_id(stripe_subscription.customer)

      return unless account

      account.subscription.update!(
        status: :canceled,
        canceled_at: Time.current
      )

      Rails.logger.info "Updated subscription for account #{account.id} - subscription canceled"
    end

    def handle_payment_succeeded
      invoice = @event.data.object
      account = find_account_by_customer_id(invoice.customer)

      return unless account

      # Update subscription status to active if payment succeeded
      if account.subscription.past_due?
        account.subscription.update!(status: :active)
        Rails.logger.info "Updated subscription for account #{account.id} - payment succeeded"
      end

      # TODO: Send payment confirmation email
      # TODO: Update usage limits if needed
    end

    def handle_payment_failed
      invoice = @event.data.object
      account = find_account_by_customer_id(invoice.customer)

      return unless account

      # Update subscription status to past due
      account.subscription.update!(status: :past_due)

      Rails.logger.warn "Payment failed for account #{account.id}"

      # TODO: Send payment failure notification
      # TODO: Implement grace period logic
    end

    def handle_trial_ending
      subscription = @event.data.object
      account = find_account_by_customer_id(subscription.customer)

      return unless account

      Rails.logger.info "Trial ending for account #{account.id}"

      # TODO: Send trial ending notification
      # TODO: Prompt user to update payment method
    end

    def find_account_by_customer_id(customer_id)
      subscription = Subscription.find_by(stripe_customer_id: customer_id)
      unless subscription
        Rails.logger.error "Could not find subscription for Stripe customer #{customer_id}"
        return nil
      end

      subscription.account
    end

    def map_stripe_status(stripe_status)
      case stripe_status
      when "trialing"
        :trialing
      when "active"
        :active
      when "past_due"
        :past_due
      when "canceled", "unpaid"
        :canceled
      else
        :canceled
      end
    end
  end
end
