class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable,
         :confirmable, :trackable, :lockable,
         :jwt_authenticatable, jwt_revocation_strategy: JwtDenylist

  # Associations
  belongs_to :account
  has_many :api_tokens, foreign_key: :created_by_id, dependent: :destroy
  has_many :account_invitations, foreign_key: :invited_by_id, dependent: :destroy
  has_many :notifications, dependent: :destroy
  has_many :created_projects, class_name: "Project", foreign_key: :created_by_id, dependent: :destroy
  has_many :created_data_connectors, class_name: "DataConnector", foreign_key: :created_by, dependent: :destroy
  has_many :created_pipelines, class_name: "Pipeline", foreign_key: :created_by, dependent: :destroy

  # Enums
  enum :role, {
    owner:  0,
    admin:  1,
    member: 2,
    viewer: 3
  }

  # Validations
  validates :email, uniqueness: { scope: :account_id }
  validates :role, presence: true

  # Callbacks
  before_validation :set_default_role, on: :create

  # Class methods
  def self.generate_otp_secret
    SecureRandom.base32(32)
  end

  # Instance methods
  def full_name
    "#{first_name} #{last_name}".strip.presence || email
  end

  def can_manage_account?
    owner? || admin?
  end

  def can_manage_team?
    owner? || admin?
  end

  def can_execute_pipelines?
    !viewer?
  end

  # Two-factor authentication methods
  def two_factor_enabled?
    otp_required_for_login?
  end

  def enable_two_factor_authentication!
    self.otp_secret = User.generate_otp_secret
    self.otp_required_for_login = true
    save!
  end

  def disable_two_factor_authentication!
    self.otp_secret = nil
    self.otp_required_for_login = false
    save!
  end

  def two_factor_qr_code
    # Generate QR code for 2FA setup
    # This would typically use a gem like rqrcode
    "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
  end

  def generate_backup_codes
    # Generate backup codes for 2FA
    Array.new(8) { SecureRandom.hex(4).upcase }
  end

  private

  def set_default_role
    self.role ||= "member"
  end
end
