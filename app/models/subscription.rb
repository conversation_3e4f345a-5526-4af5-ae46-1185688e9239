class Subscription < ApplicationRecord
  belongs_to :account
  belongs_to :subscription_plan, optional: true

  enum :status, {
    trialing: 0,
    active: 1,
    past_due: 2,
    canceled: 3,
    unpaid: 4
  }

  enum :plan, {
    free: 0,
    starter: 1,
    professional: 2,
    enterprise: 3
  }

  validates :stripe_subscription_id, uniqueness: true, allow_nil: true
  validates :stripe_customer_id, presence: true, unless: -> { free? }

  PLAN_DETAILS = {
    free: {
      price: 0,
      pipelines: 2,
      executions_per_month: 1_000,
      support: "community",
      features: [ "Basic connectors", "Email support" ],
      agent_features: {
        enabled: false,
        recommendations_per_month: 0,
        auto_implementation: false,
        template_marketplace: false,
        revenue_tracking: false
      }
    },
    starter: {
      price: 99,
      pipelines: 10,
      executions_per_month: 10_000,
      support: "email",
      features: [ "All connectors", "Email support", "Basic transformations", "Basic AI recommendations" ],
      agent_features: {
        enabled: true,
        recommendations_per_month: 50,
        auto_implementation: false,
        template_marketplace: true,
        revenue_tracking: true,
        quality_monitoring: false
      }
    },
    professional: {
      price: 299,
      pipelines: 50,
      executions_per_month: 100_000,
      support: "priority",
      features: [ "All features", "Priority support", "Advanced transformations", "API access", "Advanced AI agent" ],
      agent_features: {
        enabled: true,
        recommendations_per_month: 200,
        auto_implementation: true,
        template_marketplace: true,
        revenue_tracking: true,
        quality_monitoring: true,
        custom_recommendations: false
      }
    },
    enterprise: {
      price: 599,
      pipelines: -1, # unlimited
      executions_per_month: -1, # unlimited
      support: "dedicated",
      features: [ "All features", "Dedicated support", "Custom integrations", "SLA", "On-premise option", "Enterprise AI agent" ],
      agent_features: {
        enabled: true,
        recommendations_per_month: -1, # unlimited
        auto_implementation: true,
        template_marketplace: true,
        revenue_tracking: true,
        quality_monitoring: true,
        custom_recommendations: true,
        priority_support: true
      }
    }
  }.freeze

  def plan_limits
    PLAN_DETAILS[plan.to_sym]
  end

  def within_limits?(resource_type, count = 1)
    limit = plan_limits[resource_type]
    return true if limit == -1 # unlimited

    current_usage = account.send("#{resource_type}_count")
    current_usage + count <= limit
  rescue
    true # Default to allowing if we can't check
  end

  def plan_price
    plan_limits[:price]
  end

  def plan_name
    plan.humanize
  end

  # Agent feature access methods
  def agent_features_enabled?
    agent_features[:enabled] == true
  end

  def agent_features
    plan_limits[:agent_features] || {}
  end

  def recommendations_per_month_limit
    agent_features[:recommendations_per_month] || 0
  end

  def auto_implementation_enabled?
    agent_features[:auto_implementation] == true
  end

  def template_marketplace_enabled?
    agent_features[:template_marketplace] == true
  end

  def revenue_tracking_enabled?
    agent_features[:revenue_tracking] == true
  end

  def quality_monitoring_enabled?
    agent_features[:quality_monitoring] == true
  end

  def custom_recommendations_enabled?
    agent_features[:custom_recommendations] == true
  end

  def within_agent_recommendations_limit?
    limit = recommendations_per_month_limit
    return true if limit == -1 # unlimited

    current_month_start = Time.current.beginning_of_month
    current_usage = account.agent_recommendations
                          .where(created_at: current_month_start..)
                          .count

    current_usage < limit
  end

  def agent_recommendations_remaining
    limit = recommendations_per_month_limit
    return Float::INFINITY if limit == -1

    current_month_start = Time.current.beginning_of_month
    current_usage = account.agent_recommendations
                          .where(created_at: current_month_start..)
                          .count

    [ limit - current_usage, 0 ].max
  end

  def agent_features_included?
    agent_features_enabled?
  end
end
