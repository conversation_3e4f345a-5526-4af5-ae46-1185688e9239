class ApiToken < ApplicationRecord
  belongs_to :account
  belongs_to :created_by, class_name: "User"

  before_create :generate_token

  scope :active, -> { where(revoked_at: nil) }

  validates :name, presence: true

  def revoke!
    update!(revoked_at: Time.current)
  end

  def active?
    revoked_at.nil? && (expires_at.nil? || expires_at > Time.current)
  end

  def update_last_used!
    update_column(:last_used_at, Time.current)
  end

  private

  def generate_token
    env_prefix = Rails.env.production? ? "live" : "test"
    self.token = "dr_#{env_prefix}_#{SecureRandom.hex(32)}"
    self.expires_at = 1.year.from_now if expires_at.nil?
  end
end
