class TeamInvitation < ApplicationRecord
  belongs_to :invited_by, class_name: "User"
  belongs_to :account

  enum :role, { viewer: 0, member: 1, admin: 2, owner: 3 }
  enum :status, { pending: 0, accepted: 1, rejected: 2, expired: 3 }

  validates :email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :role, presence: true
  validates :token, presence: true, uniqueness: true
  validates :expires_at, presence: true

  scope :pending, -> { where(status: :pending) }
  scope :not_expired, -> { where("expires_at > ?", Time.current) }
  scope :valid_invitations, -> { pending.not_expired }

  before_validation :generate_token, on: :create
  before_validation :set_expiration, on: :create

  def expired?
    expires_at < Time.current
  end

  def valid_for_acceptance?
    pending? && !expired?
  end

  def accept!(user)
    return false unless valid_for_acceptance?

    transaction do
      # Create or update the user's membership in this account
      user.update!(account: account, role: role)
      update!(status: :accepted)
    end

    true
  rescue ActiveRecord::RecordInvalid
    false
  end

  def reject!
    return false unless pending?

    update(status: :rejected)
  end

  private

  def generate_token
    self.token = SecureRandom.urlsafe_base64(32)
  end

  def set_expiration
    self.expires_at = 7.days.from_now
  end
end
