class AgentRevenue < ApplicationRecord
  # Associations
  belongs_to :account
  belongs_to :agent_recommendation, optional: true
  belongs_to :pipeline, optional: true

  # Enums
  enum :revenue_source, {
    optimization_fee: 0,        # One-time fees for optimizations
    template_sale: 1,          # Template marketplace sales
    quality_monitoring: 2,      # Data quality monitoring subscription
    compliance_subscription: 3,  # Compliance monitoring subscription
    integration_commission: 4,   # Commission on integration sales
    intelligence_subscription: 5, # Pipeline intelligence subscription
    performance_bonus: 6        # Performance-based bonuses
  }

  enum :status, {
    active: 0,
    cancelled: 1,
    expired: 2,
    pending: 3,
    refunded: 4
  }

  enum :billing_period, {
    one_time: 0,
    monthly: 1,
    quarterly: 2,
    annual: 3
  }, prefix: true

  # Validations
  validates :revenue_source, presence: true
  validates :amount_cents, presence: true, numericality: { greater_than: 0 }
  validates :currency, presence: true

  # Scopes
  scope :active_subscriptions, -> { where(status: :active).where.not(billing_period: :one_time) }
  scope :recent, -> { order(created_at: :desc) }
  scope :in_period, ->(start_date, end_date) { where(created_at: start_date..end_date) }
  scope :by_source, ->(source) { where(revenue_source: source) }

  # Callbacks
  before_validation :set_defaults
  after_create :update_account_metrics

  # Money handling
  def amount
    amount_cents / 100.0
  end

  def amount=(value)
    self.amount_cents = (value.to_f * 100).to_i
  end

  def formatted_amount
    "$#{sprintf('%.2f', amount)}"
  end

  # Instance methods
  def recurring?
    !billing_period_one_time?
  end

  def subscription?
    revenue_source.in?([ "quality_monitoring", "compliance_subscription", "intelligence_subscription" ]) && recurring?
  end

  def calculate_mrr
    return 0 unless active? && recurring?

    case billing_period
    when "monthly"
      amount
    when "quarterly"
      amount / 3.0
    when "annual"
      amount / 12.0
    else
      0
    end
  end

  def cancel!
    return false unless subscription? && active?

    update!(
      status: :cancelled,
      period_end: Time.current,
      performance_metrics: performance_metrics.merge(
        "cancelled_at" => Time.current,
        "cancellation_reason" => "user_requested"
      )
    )
  end

  def renew!
    return false unless subscription? && active?

    # Create new revenue record for next period
    self.class.create!(
      account: account,
      pipeline: pipeline,
      agent_recommendation: agent_recommendation,
      revenue_source: revenue_source,
      amount_cents: amount_cents,
      currency: currency,
      billing_period: billing_period,
      period_start: period_end || Time.current,
      period_end: calculate_next_period_end,
      status: :active,
      description: "Renewal: #{description}",
      performance_metrics: { "renewed_from" => id }
    )
  end

  # Class methods
  def self.total_revenue(period = nil)
    scope = all
    scope = scope.in_period(period.ago, Time.current) if period
    scope.sum(:amount_cents) / 100.0
  end

  def self.mrr
    active_subscriptions.sum { |rev| rev.calculate_mrr }
  end

  def self.revenue_by_source(period = 30.days)
    in_period(period.ago, Time.current)
      .group(:revenue_source)
      .sum(:amount_cents)
      .transform_values { |cents| cents / 100.0 }
  end

  def self.growth_rate(current_period = 30.days, previous_period = 30.days)
    current_revenue = in_period(current_period.ago, Time.current).sum(:amount_cents)
    previous_revenue = in_period((current_period + previous_period).ago, current_period.ago).sum(:amount_cents)

    return 0 if previous_revenue.zero?

    ((current_revenue - previous_revenue).to_f / previous_revenue * 100).round(2)
  end

  def self.create_subscription!(account, source, amount, pipeline: nil)
    create!(
      account: account,
      pipeline: pipeline,
      revenue_source: source,
      amount_cents: (amount * 100).to_i,
      billing_period: :monthly,
      period_start: Time.current,
      period_end: 1.month.from_now,
      status: :active,
      description: "#{source.humanize} subscription"
    )
  end

  private

  def set_defaults
    self.currency ||= "USD"
    self.status ||= :active
    self.billing_period ||= :one_time
    self.performance_metrics ||= {}

    if recurring? && period_start.blank?
      self.period_start = Time.current
      self.period_end = calculate_next_period_end
    end
  end

  def calculate_next_period_end
    return nil unless period_start

    case billing_period
    when "monthly"
      period_start + 1.month
    when "quarterly"
      period_start + 3.months
    when "annual"
      period_start + 1.year
    else
      nil
    end
  end

  def update_account_metrics
    # Track in usage metrics for billing
    UsageMetric.create!(
      account: account,
      metric_type: "agent_revenue",
      value: amount,
      recorded_at: Time.current,
      metadata: {
        revenue_source: revenue_source,
        revenue_id: id,
        pipeline_id: pipeline_id,
        recommendation_id: agent_recommendation_id
      }
    )
  end
end
