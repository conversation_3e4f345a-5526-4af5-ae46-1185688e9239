class SubscriptionPlan < ApplicationRecord
  has_many :subscriptions, dependent: :restrict_with_error

  validates :name, presence: true
  validates :stripe_product_id, presence: true
  validates :stripe_price_id, presence: true
  validates :price_cents, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :billing_cycle, presence: true, inclusion: { in: %w[month year] }

  scope :active, -> { where(active: true) }
  scope :by_price, -> { order(:price_cents) }

  def price
    Money.new(price_cents, "USD")
  end

  def price_in_dollars
    price_cents / 100.0
  end

  def features_list
    return [] if features.blank?

    JSON.parse(features)
  rescue JSON::ParserError
    []
  end

  def monthly?
    billing_cycle == "month"
  end

  def yearly?
    billing_cycle == "year"
  end
end
