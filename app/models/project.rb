class Project < ApplicationRecord
  # Associations
  belongs_to :account
  belongs_to :created_by, class_name: "User", foreign_key: "created_by_id"
  has_many :data_connectors, dependent: :destroy
  has_many :pipelines, dependent: :nullify

  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :name, uniqueness: { scope: :account_id, message: "already exists in this account" }

  # Enums
  enum :status, {
    active: 0,
    archived: 1,
    paused: 2
  }

  # Scopes
  scope :active, -> { where(status: :active) }
  scope :recent, -> { order(created_at: :desc) }
  scope :by_account, ->(account) { where(account: account) }

  # Callbacks
  before_validation :set_defaults

  # Instance methods
  def display_name
    name
  end

  def connector_count
    data_connectors.count
  end

  def active_connector_count
    data_connectors.active.count
  end

  def pipeline_count
    pipelines.count
  end

  def can_be_deleted?
    data_connectors.empty? && pipelines.empty?
  end

  def health_summary
    connectors = data_connectors.includes(:created_by)

    {
      total_connectors: connectors.count,
      active_connectors: connectors.active.count,
      healthy_connectors: connectors.joins("").where(
        test_status: :test_passed,
        last_tested_at: 24.hours.ago..
      ).count,
      needs_attention: connectors.where(
        status: :error
      ).or(
        connectors.where(
          "last_tested_at IS NULL OR last_tested_at < ?", 24.hours.ago
        )
      ).count
    }
  end

  # Class methods
  def self.create_default_for_account!(account, user)
    create!(
      account: account,
      created_by: user,
      name: "Default Project",
      description: "Default project for organizing your data connectors",
      status: :active
    )
  end

  private

  def set_defaults
    self.settings ||= {}
    self.status ||= :active
  end
end
