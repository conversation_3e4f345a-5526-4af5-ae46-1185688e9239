class AgentRecommendation < ApplicationRecord
  # Associations
  belongs_to :account
  belongs_to :pipeline, optional: true
  has_many :agent_revenues, dependent: :nullify

  # Enums
  enum :recommendation_type, {
    optimization: 0,      # Pipeline performance optimization
    quality_fix: 1,      # Data quality improvement
    compliance: 2,       # Compliance and security
    integration: 3,      # New integration suggestion
    template: 4,         # Template marketplace suggestion
    cost_saving: 5,      # Cost reduction opportunity
    scaling: 6          # Scaling recommendation
  }

  enum :status, {
    pending: 0,
    accepted: 1,
    rejected: 2,
    implemented: 3,
    expired: 4,
    in_progress: 5
  }

  enum :priority, {
    low: 0,
    medium: 1,
    high: 2,
    critical: 3
  }

  # Validations
  validates :title, presence: true, length: { maximum: 200 }
  validates :description, presence: true
  validates :recommendation_type, presence: true
  validates :confidence_score, numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 100 }, allow_nil: true
  validates :estimated_value, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true

  # Scopes
  scope :active, -> { where(status: [ :pending, :accepted, :in_progress ]) }
  scope :not_expired, -> { where("expires_at IS NULL OR expires_at > ?", Time.current) }
  scope :high_confidence, -> { where("confidence_score >= ?", 70) }
  scope :high_value, -> { where("estimated_value >= ?", 100) }
  scope :recent, -> { order(created_at: :desc) }
  scope :by_priority, -> { order(priority: :desc, confidence_score: :desc) }

  # Callbacks
  before_validation :set_defaults
  after_update :track_implementation_revenue, if: :just_implemented?

  # Instance methods
  def expired?
    expires_at.present? && expires_at < Time.current
  end

  def can_implement?
    pending? || accepted?
  end

  def mark_as_implemented!(metrics = {})
    self.status = :implemented
    self.implemented_at = Time.current
    self.after_metrics = metrics if metrics.present?
    save!

    # Track revenue if there's value
    if estimated_value.present? && estimated_value > 0
      create_revenue_record!
    end
  end

  def implementation_success_rate
    return nil unless implemented? && before_metrics.present? && after_metrics.present?

    # Calculate improvement percentage based on metrics
    calculate_improvement_rate
  end

  def roi_estimate
    return nil unless estimated_value.present?

    # Simple ROI calculation
    implementation_cost = implementation_steps["estimated_hours"].to_f * 100 # $100/hour
    return nil if implementation_cost.zero?

    ((estimated_value * 12 - implementation_cost) / implementation_cost * 100).round(2)
  end

  def auto_expire!
    return if expired? || implemented?

    update!(status: :expired) if expires_at.present? && expires_at < Time.current
  end

  def formatted_value
    return "N/A" unless estimated_value.present?

    if estimated_value >= 1000
      "$#{(estimated_value / 1000.0).round(1)}K/month"
    else
      "$#{estimated_value.round}/month"
    end
  end

  def implementation_difficulty
    return "unknown" unless implementation_steps.present?

    hours = implementation_steps["estimated_hours"].to_f
    case hours
    when 0..2 then "easy"
    when 2..8 then "medium"
    when 8..24 then "hard"
    else "very_hard"
    end
  end

  # Class methods
  def self.generate_for_pipeline(pipeline)
    # This will be called by the PipelineIntelligenceService
    # Placeholder for now
    []
  end

  def self.cleanup_expired!
    not_expired.where("expires_at < ?", Time.current).update_all(status: :expired)
  end

  def self.acceptance_rate(period = 30.days)
    total = where(created_at: period.ago..).count
    return 0 if total.zero?

    accepted = where(created_at: period.ago.., status: [ :accepted, :implemented, :in_progress ]).count
    (accepted.to_f / total * 100).round(2)
  end

  private

  def set_defaults
    self.status ||= :pending
    self.priority ||= :medium
    self.expires_at ||= 30.days.from_now
    self.implementation_steps ||= {}
    self.ai_analysis ||= {}
    self.before_metrics ||= {}
    self.after_metrics ||= {}
  end

  def just_implemented?
    saved_change_to_status? && implemented?
  end

  def track_implementation_revenue
    # This will be expanded to track actual revenue
    Rails.logger.info "Recommendation #{id} implemented with estimated value: #{estimated_value}"
  end

  def create_revenue_record!
    AgentRevenue.create!(
      account: account,
      agent_recommendation: self,
      pipeline: pipeline,
      revenue_source: map_revenue_source,
      amount_cents: (estimated_value * 100).to_i,
      description: "Implementation of #{title}",
      performance_metrics: {
        recommendation_type: recommendation_type,
        confidence_score: confidence_score,
        implementation_date: implemented_at
      }
    )
  end

  def map_revenue_source
    case recommendation_type
    when "optimization", "cost_saving", "scaling"
      :optimization_fee
    when "quality_fix"
      :quality_monitoring
    when "compliance"
      :compliance_subscription
    when "integration"
      :integration_commission
    when "template"
      :template_sale
    else
      :optimization_fee
    end
  end

  def calculate_improvement_rate
    # Simplified calculation - would be more complex in production
    before_value = before_metrics["performance_score"] || before_metrics["quality_score"] || 50
    after_value = after_metrics["performance_score"] || after_metrics["quality_score"] || before_value

    return 0 if before_value.zero?

    ((after_value - before_value) / before_value.to_f * 100).round(2)
  end
end
