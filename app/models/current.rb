class Current < ActiveSupport::CurrentAttributes
  attribute :account, :user

  def account=(account)
    super
    set_account_context(account&.id)
  end

  def user=(user)
    super
    self.account = user&.account
  end

  private

  def set_account_context(account_id)
    if account_id
      # Set the PostgreSQL session variable for RLS (using parameterized query for security)
      ActiveRecord::Base.transaction do
        ActiveRecord::Base.connection.execute(
          ActiveRecord::Base.sanitize_sql_array([ "SET LOCAL rls.account_id = ?", account_id ])
        )
      end
    else
      # Clear the context
      ActiveRecord::Base.transaction do
        ActiveRecord::Base.connection.execute(
          "SET LOCAL rls.account_id = NULL"
        )
      end
    end
  end
end
