class PipelineTemplate < ApplicationRecord
  # Associations
  belongs_to :creator_account, class_name: "Account", optional: true
  belongs_to :source_pipeline, class_name: "Pipeline", optional: true
  has_many :agent_recommendations, -> { where(recommendation_type: :template) },
           class_name: "AgentRecommendation", dependent: :nullify
  has_many :agent_revenues, -> { where(revenue_source: :template_sale) },
           class_name: "AgentRevenue", dependent: :nullify

  # Enums
  enum :status, {
    draft: 0,
    published: 1,
    archived: 2,
    under_review: 3
  }

  enum :category, {
    e_commerce: 0,
    marketing: 1,
    finance: 2,
    operations: 3,
    analytics: 4,
    customer_service: 5,
    sales: 6,
    hr: 7
  }, prefix: true

  enum :industry, {
    retail: 0,
    saas: 1,
    healthcare: 2,
    manufacturing: 3,
    education: 4,
    finance_services: 5,
    real_estate: 6,
    hospitality: 7,
    logistics: 8,
    technology: 9
  }, prefix: true

  # Validations
  validates :name, presence: true, length: { maximum: 200 }
  validates :source_type, presence: true
  validates :destination_type, presence: true
  validates :price_cents, numericality: { greater_than_or_equal_to: 0 }
  validates :average_rating, numericality: {
    greater_than_or_equal_to: 0,
    less_than_or_equal_to: 5
  }, allow_nil: true

  # Scopes
  scope :published, -> { where(status: :published) }
  scope :featured, -> { where(featured: true) }
  scope :free, -> { where(price_cents: 0) }
  scope :paid, -> { where("price_cents > 0") }
  scope :popular, -> { order(purchases_count: :desc) }
  scope :top_rated, -> { where("ratings_count >= ?", 5).order(average_rating: :desc) }
  scope :recent, -> { order(published_at: :desc) }
  scope :by_category, ->(category) { where(category: category) }
  scope :by_industry, ->(industry) { where(industry: industry) }
  scope :for_integration, ->(source, destination) {
    where(source_type: source, destination_type: destination)
  }

  # Callbacks
  before_validation :set_defaults
  before_save :anonymize_configuration, if: :source_pipeline_id?
  after_update :track_revenue, if: :purchased?

  # Money handling
  def price
    price_cents / 100.0
  end

  def price=(value)
    self.price_cents = (value.to_f * 100).to_i
  end

  def formatted_price
    return "Free" if free?
    "$#{sprintf('%.2f', price)}"
  end

  def free?
    price_cents.zero?
  end

  # Instance methods
  def publish!
    return false unless draft? || under_review?

    update!(
      status: :published,
      published_at: Time.current
    )
  end

  def archive!
    return false unless published?

    update!(
      status: :archived,
      performance_metrics: performance_metrics.merge(
        "archived_at" => Time.current,
        "archive_reason" => "manual"
      )
    )
  end

  def purchase!(account)
    return false unless published?
    return false if creator_account_id == account.id # Can't buy own template

    transaction do
      # Increment purchase count
      increment!(:purchases_count)

      # Create revenue record if paid
      if price_cents > 0
        revenue_share = calculate_revenue_share

        # Revenue for marketplace (platform)
        AgentRevenue.create!(
          account: Account.platform_account, # Platform account
          revenue_source: :template_sale,
          amount_cents: (price_cents * (1 - revenue_share)).to_i,
          description: "Platform fee for template: #{name}",
          performance_metrics: {
            template_id: id,
            buyer_account_id: account.id
          }
        )

        # Revenue for template creator
        if creator_account_id.present?
          AgentRevenue.create!(
            account_id: creator_account_id,
            revenue_source: :template_sale,
            amount_cents: (price_cents * revenue_share).to_i,
            description: "Template sale: #{name}",
            performance_metrics: {
              template_id: id,
              buyer_account_id: account.id
            }
          )
        end
      end

      # Create pipeline from template
      create_pipeline_for(account)
    end
  end

  def create_pipeline_for(account)
    Pipeline.create!(
      account: account,
      created_by: account.users.first.id, # Default to first user
      name: "#{name} (from template)",
      description: "Created from template: #{description}",
      source_config: apply_config_template(source_config_template),
      destination_config: apply_config_template(destination_config_template),
      transformation_rules: transformation_template,
      schedule_type: schedule_template["type"] || 0,
      schedule_config: schedule_template["config"] || {}
    )
  end

  def add_rating!(rating, account_id)
    return false unless rating.between?(1, 5)
    return false if creator_account_id == account_id # Can't rate own template

    # Update rating (simplified - in production would track individual ratings)
    new_total = (average_rating || 0) * ratings_count + rating
    new_count = ratings_count + 1
    new_average = new_total.to_f / new_count

    update!(
      average_rating: new_average.round(2),
      ratings_count: new_count
    )
  end

  def compatible_with?(pipeline)
    source_type == pipeline.source_config["type"] &&
      destination_type == pipeline.destination_config["type"]
  end

  def success_rate
    return nil unless performance_metrics["total_runs"].to_i > 0

    successful = performance_metrics["successful_runs"].to_f
    total = performance_metrics["total_runs"].to_f

    ((successful / total) * 100).round(2)
  end

  def average_execution_time
    performance_metrics["avg_execution_time_seconds"] || 0
  end

  def estimated_monthly_savings
    performance_metrics["estimated_savings_usd"] || 0
  end

  # Class methods
  def self.create_from_pipeline!(pipeline, creator_account, options = {})
    template = new(
      creator_account: creator_account,
      source_pipeline: pipeline,
      name: options[:name] || "#{pipeline.name} Template",
      description: options[:description] || pipeline.description,
      category: options[:category],
      industry: options[:industry],
      source_type: pipeline.source_config["type"],
      destination_type: pipeline.destination_config["type"],
      price_cents: options[:price_cents] || 0,
      use_cases: options[:use_cases] || [],
      requirements: options[:requirements] || []
    )

    # Anonymize and save configurations
    template.anonymize_configuration
    template.save!
    template
  end

  def self.recommend_for_account(account)
    # Find templates that match account's existing pipelines
    pipeline_types = account.pipelines.pluck(:source_config, :destination_config).map do |source, dest|
      [ source["type"], dest["type"] ]
    end.uniq

    templates = published.where(status: :published)

    # Filter by matching integration types
    relevant_templates = templates.select do |template|
      pipeline_types.any? do |source_type, dest_type|
        template.source_type == source_type || template.destination_type == dest_type
      end
    end

    # Sort by relevance (rating, popularity, etc.)
    relevant_templates.sort_by { |t| [ t.average_rating || 0, t.purchases_count ] }.reverse.first(5)
  end

  def self.trending(limit = 10)
    published
      .where("created_at > ?", 30.days.ago)
      .order("purchases_count DESC, average_rating DESC NULLS LAST")
      .limit(limit)
  end

  def self.revenue_summary(period = 30.days)
    AgentRevenue
      .where(revenue_source: :template_sale)
      .where("created_at > ?", period.ago)
      .group("DATE(created_at)")
      .sum(:amount_cents)
      .transform_values { |cents| cents / 100.0 }
  end

  private

  def set_defaults
    self.currency ||= "USD"
    self.status ||= :draft
    self.performance_metrics ||= {}
    self.use_cases ||= []
    self.requirements ||= []
    self.source_config_template ||= {}
    self.destination_config_template ||= {}
    self.transformation_template ||= {}
    self.schedule_template ||= {}
  end

  def anonymize_configuration
    return unless source_pipeline

    # Anonymize source configuration
    self.source_config_template = anonymize_config(source_pipeline.source_config)

    # Anonymize destination configuration
    self.destination_config_template = anonymize_config(source_pipeline.destination_config)

    # Copy transformation rules (usually safe to share)
    self.transformation_template = source_pipeline.transformation_rules

    # Copy schedule config
    self.schedule_template = {
      "type" => source_pipeline.schedule_type,
      "config" => anonymize_schedule(source_pipeline.schedule_config)
    }

    # Calculate performance metrics
    calculate_performance_metrics
  end

  def anonymize_config(config)
    # Remove sensitive data like passwords, API keys, etc.
    config.deep_dup.tap do |safe_config|
      safe_config.delete("password")
      safe_config.delete("api_key")
      safe_config.delete("secret")
      safe_config.delete("token")
      safe_config.delete("connection_string")

      # Replace specific values with placeholders
      safe_config["host"] = "{{HOST}}" if safe_config["host"]
      safe_config["database"] = "{{DATABASE}}" if safe_config["database"]
      safe_config["username"] = "{{USERNAME}}" if safe_config["username"]
      safe_config["bucket"] = "{{BUCKET}}" if safe_config["bucket"]
      safe_config["endpoint"] = "{{ENDPOINT}}" if safe_config["endpoint"]
    end
  end

  def anonymize_schedule(schedule_config)
    schedule_config.deep_dup.tap do |config|
      # Keep schedule structure but remove any sensitive webhook URLs
      config["webhook_url"] = "{{WEBHOOK_URL}}" if config["webhook_url"]
    end
  end

  def calculate_performance_metrics
    return unless source_pipeline

    executions = source_pipeline.pipeline_executions.recent.limit(100)

    self.performance_metrics = {
      "total_runs" => executions.count,
      "successful_runs" => executions.where(status: :success).count,
      "avg_execution_time_seconds" => executions.average(:execution_time) || 0,
      "avg_records_processed" => executions.average(:records_processed) || 0,
      "last_updated" => Time.current
    }
  end

  def apply_config_template(template)
    # This would be expanded to properly apply template with user inputs
    template.deep_dup
  end

  def calculate_revenue_share
    # Platform takes 30%, creator gets 70%
    0.7
  end

  def purchased?
    saved_change_to_purchases_count? && purchases_count > purchases_count_before_last_save
  end

  def track_revenue
    # Additional revenue tracking logic if needed
    Rails.logger.info "Template #{id} purchased. Total purchases: #{purchases_count}"
  end
end
