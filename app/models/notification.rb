class Notification < ApplicationRecord
  belongs_to :account
  belongs_to :user, optional: true # Can be account-wide or user-specific
  belongs_to :notifiable, polymorphic: true, optional: true

  validates :title, presence: true
  validates :message, presence: true
  validates :notification_type, presence: true
  validates :priority, inclusion: { in: %w[low medium high critical] }

  scope :unread, -> { where(read_at: nil) }
  scope :read, -> { where.not(read_at: nil) }
  scope :for_user, ->(user) { where(user: [ nil, user ]) }
  scope :recent, -> { order(created_at: :desc) }
  scope :by_priority, -> { order(Arel.sql("CASE priority WHEN 'critical' THEN 1 WHEN 'high' THEN 2 WHEN 'medium' THEN 3 ELSE 4 END")) }

  enum :notification_type, {
    system: "system",
    pipeline_success: "pipeline_success",
    pipeline_failure: "pipeline_failure",
    connector_issue: "connector_issue",
    storage_warning: "storage_warning",
    plan_limit: "plan_limit",
    team_update: "team_update",
    security_alert: "security_alert",
    maintenance: "maintenance",
    feature_announcement: "feature_announcement"
  }

  def read?
    read_at.present?
  end

  def unread?
    !read?
  end

  def mark_as_read!
    update!(read_at: Time.current) unless read?
  end

  def mark_as_unread!
    update!(read_at: nil) if read?
  end

  def icon_class
    case notification_type
    when "pipeline_success"
      "text-green-500"
    when "pipeline_failure", "connector_issue"
      "text-red-500"
    when "storage_warning", "plan_limit"
      "text-yellow-500"
    when "security_alert"
      "text-red-600"
    when "maintenance"
      "text-blue-500"
    when "feature_announcement"
      "text-purple-500"
    when "team_update"
      "text-indigo-500"
    else
      "text-gray-500"
    end
  end

  def icon_svg
    case notification_type
    when "pipeline_success"
      '<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/></svg>'
    when "pipeline_failure", "connector_issue"
      '<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/></svg>'
    when "storage_warning", "plan_limit"
      '<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/></svg>'
    when "security_alert"
      '<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/></svg>'
    when "maintenance"
      '<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/></svg>'
    when "feature_announcement"
      '<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"/></svg>'
    when "team_update"
      '<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20"><path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/></svg>'
    else
      '<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/></svg>'
    end
  end

  def time_ago
    return "Just now" if created_at > 1.minute.ago
    return "#{((Time.current - created_at) / 1.minute).round}m ago" if created_at > 1.hour.ago
    return "#{((Time.current - created_at) / 1.hour).round}h ago" if created_at > 1.day.ago
    return "#{((Time.current - created_at) / 1.day).round}d ago" if created_at > 1.week.ago

    created_at.strftime("%m/%d/%Y")
  end

  # Class methods for creating notifications
  def self.create_pipeline_success(account, pipeline, user = nil)
    create!(
      account: account,
      user: user,
      title: "Pipeline Executed Successfully",
      message: "Pipeline '#{pipeline.name}' completed successfully.",
      notification_type: "pipeline_success",
      priority: "low",
      notifiable: pipeline,
      action_url: "/pipelines/#{pipeline.id}"
    )
  end

  def self.create_pipeline_failure(account, pipeline, error_message = nil, user = nil)
    create!(
      account: account,
      user: user,
      title: "Pipeline Execution Failed",
      message: "Pipeline '#{pipeline.name}' failed to execute. #{error_message}",
      notification_type: "pipeline_failure",
      priority: "high",
      notifiable: pipeline,
      action_url: "/pipelines/#{pipeline.id}"
    )
  end

  def self.create_connector_issue(account, connector, issue_description, user = nil)
    create!(
      account: account,
      user: user,
      title: "Connection Issue Detected",
      message: "Connection '#{connector.name}' has an issue: #{issue_description}",
      notification_type: "connector_issue",
      priority: "medium",
      notifiable: connector,
      action_url: "/connectors/#{connector.id}"
    )
  end

  def self.create_storage_warning(account, usage_percent)
    create!(
      account: account,
      title: "Storage Usage Warning",
      message: "Your storage usage is at #{usage_percent}% of your plan limit.",
      notification_type: "storage_warning",
      priority: "medium",
      action_url: "/subscription"
    )
  end

  def self.create_plan_limit_reached(account, limit_type)
    create!(
      account: account,
      title: "Plan Limit Reached",
      message: "You've reached your #{limit_type} limit for your current plan.",
      notification_type: "plan_limit",
      priority: "high",
      action_url: "/subscription"
    )
  end

  def self.create_team_update(account, message, user = nil)
    create!(
      account: account,
      user: user,
      title: "Team Update",
      message: message,
      notification_type: "team_update",
      priority: "low",
      action_url: "/team_members"
    )
  end

  def self.create_security_alert(account, alert_message)
    create!(
      account: account,
      title: "Security Alert",
      message: alert_message,
      notification_type: "security_alert",
      priority: "critical",
      action_url: "/settings/security"
    )
  end

  def self.create_maintenance_notice(account, maintenance_message)
    create!(
      account: account,
      title: "Scheduled Maintenance",
      message: maintenance_message,
      notification_type: "maintenance",
      priority: "medium"
    )
  end

  def self.create_feature_announcement(account, feature_name, description)
    create!(
      account: account,
      title: "New Feature: #{feature_name}",
      message: description,
      notification_type: "feature_announcement",
      priority: "low"
    )
  end
end
