class StripeWebhook < ApplicationRecord
  validates :event_id, presence: true, uniqueness: true
  validates :event_type, presence: true

  scope :unprocessed, -> { where(processed: false) }
  scope :processed, -> { where(processed: true) }

  def mark_as_processed!
    update!(processed: true, processed_at: Time.current)
  end

  def customer_subscription_events
    %w[
      customer.subscription.created
      customer.subscription.updated
      customer.subscription.deleted
      customer.subscription.trial_will_end
      invoice.payment_succeeded
      invoice.payment_failed
    ]
  end

  def subscription_event?
    customer_subscription_events.include?(event_type)
  end
end
