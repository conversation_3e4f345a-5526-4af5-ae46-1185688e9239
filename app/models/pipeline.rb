class Pipeline < ApplicationRecord
  # Associations
  belongs_to :account
  belongs_to :created_by, class_name: "User", foreign_key: "created_by"
  has_many :pipeline_executions, dependent: :destroy

  # Config fields are stored as JSONB in the database

  # Validations
  validates :name, presence: true, length: { minimum: 3, maximum: 100 }
  validates :status, presence: true
  validates :schedule_type, presence: true
  validates :source_config, presence: true
  validates :destination_config, presence: true

  # Enums
  enum :status, {
    draft: 0,
    active: 1,
    paused: 2,
    error: 3,
    archived: 4
  }

  enum :schedule_type, {
    manual: 0,
    real_time: 1,
    hourly: 2,
    daily: 3,
    weekly: 4,
    cron: 5
  }

  enum :last_execution_status, {
    pending: 0,
    running: 1,
    success: 2,
    failed: 3,
    cancelled: 4
  }, prefix: true

  # Scopes
  scope :active, -> { where(status: :active) }
  scope :scheduled, -> { where.not(schedule_type: :manual) }
  scope :recent, -> { order(created_at: :desc) }
  scope :by_performance, -> { order(avg_execution_time: :asc) }

  # Callbacks
  before_validation :set_defaults
  after_update :track_status_changes

  # Instance methods
  def source_type
    source_config["type"] || "unknown"
  end

  def destination_type
    destination_config["type"] || "unknown"
  end

  def can_execute?
    active? && source_config.present? && destination_config.present?
  end

  def last_execution
    pipeline_executions.order(started_at: :desc).first
  end

  def success_rate(period = 30.days)
    executions = pipeline_executions.where(started_at: period.ago..)
    return 0 if executions.empty?

    successful = executions.success.count
    (successful.to_f / executions.count * 100).round(2)
  end

  def average_execution_time(period = 30.days)
    pipeline_executions.where(started_at: period.ago..)
                      .where.not(execution_time: nil)
                      .average(:execution_time) || 0
  end

  def update_performance_metrics!
    recent_executions = pipeline_executions.where(started_at: 7.days.ago..)

    if recent_executions.any?
      avg_time = recent_executions.where.not(execution_time: nil).average(:execution_time)
      last_exec = recent_executions.order(started_at: :desc).first

      update_columns(
        avg_execution_time: avg_time,
        last_executed_at: last_exec&.started_at,
        last_execution_status: last_exec&.status,
        execution_count: pipeline_executions.count
      )
    end
  end

  def schedule_description
    case schedule_type
    when "real_time"
      "Real-time (as data arrives)"
    when "hourly"
      "Every hour at #{schedule_config['minute'] || 0} minutes"
    when "daily"
      "Daily at #{schedule_config['hour'] || 0}:#{schedule_config['minute']&.to_s&.rjust(2, '0') || '00'}"
    when "weekly"
      day = Date::DAYNAMES[schedule_config["day_of_week"] || 0]
      "Weekly on #{day} at #{schedule_config['hour'] || 0}:#{schedule_config['minute']&.to_s&.rjust(2, '0') || '00'}"
    when "cron"
      schedule_config["cron_expression"] || "Custom schedule"
    else
      "Manual execution only"
    end
  end

  def transformation_summary
    rules = transformation_rules || {}
    return "No transformations" if rules.empty?

    types = []
    types << "Field mapping" if rules["field_mappings"].present?
    types << "Filtering" if rules["filters"].present?
    types << "Validation" if rules["validations"].present?
    types << "Aggregation" if rules["aggregations"].present?

    types.any? ? types.join(", ") : "Custom transformations"
  end

  # Class methods
  def self.needs_execution
    active.scheduled.joins(:pipeline_executions)
          .where("pipelines.last_executed_at IS NULL OR pipelines.last_executed_at < ?", 1.hour.ago)
          .or(active.scheduled.left_joins(:pipeline_executions).where(pipeline_executions: { id: nil }))
  end

  def self.performance_summary(account)
    pipelines = where(account: account)

    # Calculate overall success rate across all executions for this account
    total_executions = PipelineExecution.joins(:pipeline).where(pipeline: { account: account })
    success_rate = if total_executions.any?
      successful_executions = total_executions.where(status: "success").count
      successful_executions.to_f / total_executions.count
    else
      0.0
    end

    {
      total_pipelines: pipelines.count,
      active_pipelines: pipelines.active.count,
      avg_success_rate: success_rate,
      total_executions: total_executions.count
    }
  end

  private

  def set_defaults
    self.source_config ||= {}
    self.destination_config ||= {}
    self.transformation_rules ||= {}
    self.schedule_config ||= {}
  end

  def track_status_changes
    if saved_change_to_status?
      # Track status changes for analytics
      Rails.logger.info "Pipeline #{id} status changed to #{status}"

      # Update usage metrics if needed
      if account.present?
        UsageTrackingService.new(account).record_pipeline_status_change(self, status)
      end
    end
  end
end
