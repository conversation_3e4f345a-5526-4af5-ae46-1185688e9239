import { Controller } from "@hotwired/stimulus"

// Sophisticated Profile Manager Controller
// Demonstrates advanced Stimulus patterns with real-time validation, auto-save, and micro-interactions
export default class extends Controller {
  static targets = [
    "form", "saveButton", "saveText", "autoSaveIndicator", "loadingOverlay"
  ]
  
  static values = { 
    autoSave: { type: Boolean, default: true },
    validationEnabled: { type: Boolean, default: true },
    saveDelay: { type: Number, default: 2000 } // Auto-save delay in milliseconds
  }

  connect() {
    this.setupAutoSave()
    this.setupKeyboardShortcuts()
    this.setupAccessibility()
    this.initializeTooltips()
    this.hasUnsavedChanges = false
    
    // Initialize form state
    this.originalFormData = this.getFormData()
    
    console.log("Profile Manager connected with sophisticated features")
  }

  disconnect() {
    this.cleanupAutoSave()
    this.cleanupKeyboardShortcuts()
  }

  // Auto-save functionality with debouncing
  setupAutoSave() {
    if (!this.autoSaveValue) return
    
    this.autoSaveTimeout = null
    this.autoSaveDelay = this.saveDelayValue
    
    // Listen for form changes
    if (this.hasFormTarget) {
      this.formTarget.addEventListener('input', this.handleFormChange.bind(this))
      this.formTarget.addEventListener('change', this.handleFormChange.bind(this))
    }
  }

  cleanupAutoSave() {
    if (this.autoSaveTimeout) {
      clearTimeout(this.autoSaveTimeout)
    }
  }

  handleFormChange(event) {
    this.hasUnsavedChanges = true
    this.updateSaveButtonState()
    
    if (this.autoSaveValue) {
      this.debounceAutoSave()
    }
  }

  debounceAutoSave() {
    if (this.autoSaveTimeout) {
      clearTimeout(this.autoSaveTimeout)
    }
    
    this.autoSaveTimeout = setTimeout(() => {
      this.performAutoSave()
    }, this.autoSaveDelay)
  }

  async performAutoSave() {
    if (!this.hasUnsavedChanges) return
    
    try {
      const formData = this.getFormData()
      const response = await this.submitFormData(formData, { autoSave: true })
      
      if (response.ok) {
        this.showAutoSaveSuccess()
        this.hasUnsavedChanges = false
        this.originalFormData = formData
        this.updateSaveButtonState()
      }
    } catch (error) {
      console.error('Auto-save failed:', error)
      this.showAutoSaveError()
    }
  }

  // Manual save with loading states and feedback
  async saveProfile(event) {
    event?.preventDefault()
    
    if (!this.hasUnsavedChanges) {
      this.showNoChangesMessage()
      return
    }
    
    this.showLoadingState()
    
    try {
      const formData = this.getFormData()
      const response = await this.submitFormData(formData)
      
      if (response.ok) {
        this.showSaveSuccess()
        this.hasUnsavedChanges = false
        this.originalFormData = formData
        this.updateSaveButtonState()
      } else {
        throw new Error('Save failed')
      }
    } catch (error) {
      console.error('Save failed:', error)
      this.showSaveError()
    } finally {
      this.hideLoadingState()
    }
  }

  // Form data management
  getFormData() {
    if (!this.hasFormTarget) return {}
    
    const formData = new FormData(this.formTarget)
    const data = {}
    
    for (let [key, value] of formData.entries()) {
      data[key] = value
    }
    
    return data
  }

  async submitFormData(data, options = {}) {
    const url = this.formTarget.action
    const method = this.formTarget.method || 'PATCH'
    
    const headers = {
      'Content-Type': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
    }
    
    if (options.autoSave) {
      headers['X-Auto-Save'] = 'true'
    }
    
    return fetch(url, {
      method: method,
      headers: headers,
      body: JSON.stringify({ user: data })
    })
  }

  // UI State Management
  updateSaveButtonState() {
    if (!this.hasSaveButtonTarget) return
    
    if (this.hasUnsavedChanges) {
      this.saveButtonTarget.disabled = false
      this.saveButtonTarget.classList.remove('opacity-50', 'cursor-not-allowed')
      this.saveButtonTarget.classList.add('hover:scale-105')
      
      if (this.hasSaveTextTarget) {
        this.saveTextTarget.textContent = 'Save Changes'
      }
    } else {
      this.saveButtonTarget.disabled = true
      this.saveButtonTarget.classList.add('opacity-50', 'cursor-not-allowed')
      this.saveButtonTarget.classList.remove('hover:scale-105')
      
      if (this.hasSaveTextTarget) {
        this.saveTextTarget.textContent = 'No Changes'
      }
    }
  }

  showLoadingState() {
    if (this.hasLoadingOverlayTarget) {
      this.loadingOverlayTarget.classList.remove('hidden')
      this.loadingOverlayTarget.classList.add('flex')
    }
    
    if (this.hasSaveButtonTarget) {
      this.saveButtonTarget.disabled = true
      this.saveButtonTarget.innerHTML = `
        <svg class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Saving...
      `
    }
  }

  hideLoadingState() {
    if (this.hasLoadingOverlayTarget) {
      this.loadingOverlayTarget.classList.add('hidden')
      this.loadingOverlayTarget.classList.remove('flex')
    }
    
    if (this.hasSaveButtonTarget) {
      this.saveButtonTarget.innerHTML = `
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
        </svg>
        <span data-profile-manager-target="saveText">Save Changes</span>
      `
      this.updateSaveButtonState()
    }
  }

  // Feedback Messages
  showAutoSaveSuccess() {
    if (this.hasAutoSaveIndicatorTarget) {
      this.autoSaveIndicatorTarget.classList.remove('hidden')
      
      setTimeout(() => {
        this.autoSaveIndicatorTarget.classList.add('hidden')
      }, 3000)
    }
    
    this.showToast('Changes saved automatically', 'success')
  }

  showAutoSaveError() {
    this.showToast('Auto-save failed. Please save manually.', 'error')
  }

  showSaveSuccess() {
    this.showToast('Profile updated successfully!', 'success')
  }

  showSaveError() {
    this.showToast('Failed to save changes. Please try again.', 'error')
  }

  showNoChangesMessage() {
    this.showToast('No changes to save', 'info')
  }

  showToast(message, type = 'info') {
    // Create and show toast notification
    const toast = document.createElement('div')
    toast.className = `fixed top-4 right-4 z-50 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 transform transition-all duration-300 translate-x-full`
    
    const bgColor = {
      success: 'bg-green-50 border-green-200',
      error: 'bg-red-50 border-red-200',
      info: 'bg-blue-50 border-blue-200'
    }[type] || 'bg-gray-50 border-gray-200'
    
    const iconColor = {
      success: 'text-green-400',
      error: 'text-red-400',
      info: 'text-blue-400'
    }[type] || 'text-gray-400'
    
    const icon = {
      success: 'M5 13l4 4L19 7',
      error: 'M6 18L18 6M6 6l12 12',
      info: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
    }[type] || 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
    
    toast.innerHTML = `
      <div class="p-4 ${bgColor} border rounded-lg">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 ${iconColor}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${icon}"/>
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900">${message}</p>
          </div>
          <div class="ml-auto pl-3">
            <button type="button" class="inline-flex text-gray-400 hover:text-gray-600" onclick="this.closest('.fixed').remove()">
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    `
    
    document.body.appendChild(toast)
    
    // Animate in
    requestAnimationFrame(() => {
      toast.classList.remove('translate-x-full')
    })
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      toast.classList.add('translate-x-full')
      setTimeout(() => toast.remove(), 300)
    }, 5000)
  }

  // Navigation and Accessibility
  navigateToSection(event) {
    event.preventDefault()
    const sectionId = event.currentTarget.dataset.section
    const section = document.getElementById(sectionId)
    
    if (section) {
      section.scrollIntoView({ behavior: 'smooth', block: 'start' })
      
      // Update active navigation state
      this.updateActiveNavigation(event.currentTarget)
      
      // Focus management for accessibility
      section.focus()
    }
  }

  updateActiveNavigation(activeLink) {
    // Remove active state from all nav links
    const navLinks = document.querySelectorAll('[data-action*="navigateToSection"]')
    navLinks.forEach(link => {
      link.classList.remove('bg-indigo-50', 'border-r-2', 'border-indigo-500', 'text-indigo-700')
      link.classList.add('text-gray-700', 'hover:text-gray-900', 'hover:bg-gray-50')
    })
    
    // Add active state to clicked link
    activeLink.classList.add('bg-indigo-50', 'border-r-2', 'border-indigo-500', 'text-indigo-700')
    activeLink.classList.remove('text-gray-700', 'hover:text-gray-900', 'hover:bg-gray-50')
  }

  setupKeyboardShortcuts() {
    this.keyboardHandler = (event) => {
      // Ctrl/Cmd + S to save
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault()
        this.saveProfile()
      }
      
      // Escape to cancel/reset
      if (event.key === 'Escape' && this.hasUnsavedChanges) {
        this.resetForm()
      }
    }
    
    document.addEventListener('keydown', this.keyboardHandler)
  }

  cleanupKeyboardShortcuts() {
    if (this.keyboardHandler) {
      document.removeEventListener('keydown', this.keyboardHandler)
    }
  }

  setupAccessibility() {
    // Announce changes to screen readers
    this.announcer = document.createElement('div')
    this.announcer.setAttribute('aria-live', 'polite')
    this.announcer.setAttribute('aria-atomic', 'true')
    this.announcer.className = 'sr-only'
    document.body.appendChild(this.announcer)
  }

  announceToScreenReader(message) {
    if (this.announcer) {
      this.announcer.textContent = message
    }
  }

  initializeTooltips() {
    // Initialize any tooltips that need dynamic content
    const tooltips = this.element.querySelectorAll('[data-controller*="tooltip"]')
    tooltips.forEach(tooltip => {
      // Add any dynamic tooltip initialization here
    })
  }

  // Additional sophisticated features
  exportProfile() {
    const profileData = this.getFormData()
    const dataStr = JSON.stringify(profileData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    
    const link = document.createElement('a')
    link.href = URL.createObjectURL(dataBlob)
    link.download = 'profile-data.json'
    link.click()
    
    this.showToast('Profile data exported successfully', 'success')
  }

  resetForm() {
    if (confirm('Are you sure you want to discard all changes?')) {
      this.formTarget.reset()
      this.hasUnsavedChanges = false
      this.updateSaveButtonState()
      this.showToast('Changes discarded', 'info')
    }
  }

  // Handle input events for real-time feedback
  handleInput(event) {
    this.handleFormChange(event)
    
    // Add visual feedback for the changed field
    const field = event.target
    field.classList.add('border-yellow-300', 'bg-yellow-50')
    
    setTimeout(() => {
      field.classList.remove('border-yellow-300', 'bg-yellow-50')
    }, 1000)
  }

  handleChange(event) {
    this.handleFormChange(event)
  }
}
