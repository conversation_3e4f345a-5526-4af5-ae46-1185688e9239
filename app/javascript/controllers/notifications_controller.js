import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["badge", "container", "loading", "empty", "list", "markAllButton"]
  static values = { 
    url: String,
    countUrl: String,
    refreshInterval: { type: Number, default: 30000 }
  }

  connect() {
    this.loadNotificationCount()
    this.startPeriodicRefresh()
  }

  async loadNotifications() {
    if (!this.urlValue) return

    try {
      this.showLoading()
      
      const response = await fetch(this.urlValue, {
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        }
      })

      if (response.ok) {
        const data = await response.json()
        this.displayNotifications(data.notifications)
        this.updateBadge(data.unread_count)
      } else {
        this.showError('Failed to load notifications')
      }
    } catch (error) {
      console.error('Failed to load notifications:', error)
      this.showError('Network error occurred')
    } finally {
      this.hideLoading()
    }
  }

  async loadNotificationCount() {
    if (!this.countUrlValue) return

    try {
      const response = await fetch(this.countUrlValue, {
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        }
      })

      if (response.ok) {
        const data = await response.json()
        this.updateBadge(data.unread_count)
      }
    } catch (error) {
      console.error('Failed to load notification count:', error)
    }
  }

  async markAsRead(event) {
    const notificationId = event.currentTarget.dataset.notificationId
    if (!notificationId) return

    try {
      const response = await fetch(`/notifications/${notificationId}/mark_as_read`, {
        method: 'PATCH',
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        }
      })

      if (response.ok) {
        // Update the notification appearance
        const notificationElement = event.currentTarget.closest('[data-notification-id]')
        if (notificationElement) {
          notificationElement.classList.remove('bg-blue-50')
          notificationElement.classList.add('bg-white')
          
          const unreadIndicator = notificationElement.querySelector('[data-unread-indicator]')
          if (unreadIndicator) {
            unreadIndicator.style.display = 'none'
          }
        }
        
        // Refresh count
        this.loadNotificationCount()
      }
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
    }
  }

  async markAllAsRead() {
    try {
      const response = await fetch('/notifications/mark_all_as_read', {
        method: 'PATCH',
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        }
      })

      if (response.ok) {
        // Update all notification appearances
        const notifications = this.listTarget.querySelectorAll('[data-notification-id]')
        notifications.forEach(notification => {
          notification.classList.remove('bg-blue-50')
          notification.classList.add('bg-white')
          
          const unreadIndicator = notification.querySelector('[data-unread-indicator]')
          if (unreadIndicator) {
            unreadIndicator.style.display = 'none'
          }
        })
        
        // Update badge
        this.updateBadge(0)
        
        // Hide mark all button
        if (this.hasMarkAllButtonTarget) {
          this.markAllButtonTarget.style.display = 'none'
        }
      }
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error)
    }
  }

  displayNotifications(notifications) {
    if (!this.hasListTarget) return

    if (notifications.length === 0) {
      this.showEmpty()
      return
    }

    this.hideEmpty()
    
    const notificationsHtml = notifications.map(notification => 
      this.createNotificationHtml(notification)
    ).join('')
    
    this.listTarget.innerHTML = notificationsHtml
    
    // Show/hide mark all button based on unread notifications
    const hasUnread = notifications.some(n => !n.read)
    if (this.hasMarkAllButtonTarget) {
      this.markAllButtonTarget.style.display = hasUnread ? 'block' : 'none'
    }
  }

  createNotificationHtml(notification) {
    const isUnread = !notification.read
    const bgClass = isUnread ? 'bg-blue-50' : 'bg-white'
    const unreadIndicator = isUnread ? 
      '<div class="absolute top-2 right-2 h-2 w-2 bg-blue-600 rounded-full" data-unread-indicator></div>' : 
      ''

    return `
      <div class="relative ${bgClass} hover:bg-gray-50 border-b border-gray-100 transition-colors duration-150"
           data-notification-id="${notification.id}">
        ${unreadIndicator}
        <a href="${notification.action_url || '#'}" 
           class="block px-4 py-3"
           data-action="click->notifications#markAsRead"
           data-notification-id="${notification.id}">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <div class="h-8 w-8 rounded-full flex items-center justify-center ${this.getNotificationBgClass(notification.notification_type)}">
                ${notification.icon_svg}
              </div>
            </div>
            <div class="ml-3 flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                ${notification.title}
              </p>
              <p class="text-sm text-gray-600 line-clamp-2">
                ${notification.message}
              </p>
              <p class="text-xs text-gray-500 mt-1">
                ${notification.time_ago}
              </p>
            </div>
          </div>
        </a>
      </div>
    `
  }

  getNotificationBgClass(type) {
    const bgClasses = {
      'pipeline_success': 'bg-green-100',
      'pipeline_failure': 'bg-red-100',
      'connector_issue': 'bg-yellow-100',
      'storage_warning': 'bg-orange-100',
      'plan_limit': 'bg-red-100',
      'team_update': 'bg-blue-100',
      'security_alert': 'bg-red-100',
      'maintenance': 'bg-blue-100',
      'feature_announcement': 'bg-purple-100'
    }
    return bgClasses[type] || 'bg-gray-100'
  }

  updateBadge(count) {
    if (!this.hasBadgeTarget) return

    if (count > 0) {
      this.badgeTarget.style.display = 'block'
      this.badgeTarget.setAttribute('aria-label', `${count} unread notifications`)
    } else {
      this.badgeTarget.style.display = 'none'
      this.badgeTarget.removeAttribute('aria-label')
    }
  }

  showLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.remove('hidden')
    }
    if (this.hasEmptyTarget) {
      this.emptyTarget.classList.add('hidden')
    }
  }

  hideLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.add('hidden')
    }
  }

  showEmpty() {
    if (this.hasEmptyTarget) {
      this.emptyTarget.classList.remove('hidden')
    }
  }

  hideEmpty() {
    if (this.hasEmptyTarget) {
      this.emptyTarget.classList.add('hidden')
    }
  }

  showError(message) {
    if (this.hasListTarget) {
      this.listTarget.innerHTML = `
        <div class="px-4 py-8 text-center">
          <svg class="mx-auto h-12 w-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <p class="mt-2 text-sm text-red-600">${message}</p>
          <button class="mt-2 text-sm text-indigo-600 hover:text-indigo-500" 
                  data-action="click->notifications#loadNotifications">
            Try again
          </button>
        </div>
      `
    }
  }

  startPeriodicRefresh() {
    this.refreshTimer = setInterval(() => {
      this.loadNotificationCount()
    }, this.refreshIntervalValue)
  }

  stopPeriodicRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  disconnect() {
    this.stopPeriodicRefresh()
  }
}
