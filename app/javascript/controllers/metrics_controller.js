import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["value", "trend", "status"]
  static values = { 
    metric: String,
    currentValue: Number,
    previousValue: Number,
    format: { type: String, default: "number" },
    updateUrl: String
  }

  connect() {
    this.updateDisplay()
    this.calculateTrend()
  }

  updateDisplay() {
    if (this.hasValueTarget) {
      this.valueTarget.textContent = this.formatValue(this.currentValueValue)
    }
  }

  calculateTrend() {
    if (this.hasTrendTarget && this.previousValueValue !== undefined) {
      const change = this.currentValueValue - this.previousValueValue
      const percentChange = this.previousValueValue !== 0 
        ? (change / this.previousValueValue) * 100 
        : 0

      this.updateTrendDisplay(change, percentChange)
    }
  }

  updateTrendDisplay(change, percentChange) {
    const trendElement = this.trendTarget
    const isPositive = change > 0
    const isNegative = change < 0

    // Clear existing classes
    trendElement.classList.remove("text-green-600", "text-red-600", "text-gray-500")

    if (isPositive) {
      trendElement.classList.add("text-green-600")
      trendElement.innerHTML = `
        <svg class="inline w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L10 4.414 6.707 7.707a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
        </svg>
        +${Math.abs(percentChange).toFixed(1)}%
      `
    } else if (isNegative) {
      trendElement.classList.add("text-red-600")
      trendElement.innerHTML = `
        <svg class="inline w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L10 15.586l3.293-3.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
        </svg>
        -${Math.abs(percentChange).toFixed(1)}%
      `
    } else {
      trendElement.classList.add("text-gray-500")
      trendElement.innerHTML = `
        <svg class="inline w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
        </svg>
        0%
      `
    }
  }

  formatValue(value) {
    switch (this.formatValue) {
      case "currency":
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(value)
      
      case "percentage":
        return `${value.toFixed(1)}%`
      
      case "bytes":
        return this.formatBytes(value)
      
      case "duration":
        return this.formatDuration(value)
      
      case "compact":
        return new Intl.NumberFormat('en-US', {
          notation: 'compact',
          maximumFractionDigits: 1
        }).format(value)
      
      default:
        return new Intl.NumberFormat('en-US').format(value)
    }
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  formatDuration(seconds) {
    if (seconds < 60) {
      return `${seconds}s`
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)}m ${seconds % 60}s`
    } else {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return `${hours}h ${minutes}m`
    }
  }

  async refresh() {
    const metricType = this.metricValue
    let endpoint = '/dashboard/metrics'

    // Use specific endpoint based on metric type
    switch (metricType) {
      case 'total_pipelines':
      case 'active_pipelines':
        endpoint = '/dashboard/pipeline_metrics'
        break
      case 'connector_health':
      case 'total_connectors':
        endpoint = '/dashboard/connector_metrics'
        break
      case 'data_processed':
      case 'storage_usage':
        endpoint = '/dashboard/usage_metrics'
        break
      case 'system_health':
        endpoint = '/dashboard/system_health'
        break
      default:
        endpoint = this.updateUrlValue || '/dashboard/metrics'
    }

    try {
      this.setLoading(true)

      const response = await fetch(endpoint, {
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        }
      })

      if (response.ok) {
        const data = await response.json()
        this.updateMetric(data)
      } else {
        console.error('Failed to refresh metric:', response.statusText)
        this.showError('Failed to update metrics')
      }
    } catch (error) {
      console.error('Failed to refresh metric:', error)
      this.showError('Network error occurred')
    } finally {
      this.setLoading(false)
    }
  }

  updateMetric(data) {
    // Extract the relevant metric value from the response
    let newValue = this.currentValueValue
    const metricType = this.metricValue

    if (data.pipeline_metrics && (metricType === 'total_pipelines' || metricType === 'active_pipelines')) {
      newValue = data.pipeline_metrics[metricType] || this.currentValueValue
    } else if (data.connector_metrics && (metricType === 'total_connectors' || metricType === 'connector_health')) {
      newValue = data.connector_metrics.total || data.connector_metrics.healthy || this.currentValueValue
    } else if (data.usage_metrics && (metricType === 'data_processed' || metricType === 'storage_usage')) {
      newValue = data.usage_metrics.data_processed_mb || data.usage_metrics.storage_used_mb || this.currentValueValue
    } else if (data.system_health && metricType === 'system_health') {
      newValue = data.system_health.response_time_ms || this.currentValueValue
    } else if (data.value !== undefined) {
      newValue = data.value
    }

    this.previousValueValue = this.currentValueValue
    this.currentValueValue = newValue

    this.updateDisplay()
    this.calculateTrend()

    // Animate the change
    this.animateChange()
  }

  animateChange() {
    if (this.hasValueTarget) {
      this.valueTarget.classList.add("animate-pulse")
      setTimeout(() => {
        this.valueTarget.classList.remove("animate-pulse")
      }, 500)
    }
  }

  setLoading(loading) {
    if (this.hasStatusTarget) {
      if (loading) {
        this.statusTarget.innerHTML = `
          <div class="inline-block w-3 h-3 border border-gray-300 border-t-transparent rounded-full animate-spin"></div>
        `
      } else {
        this.statusTarget.innerHTML = ""
      }
    }
  }

  showError(message) {
    if (this.hasStatusTarget) {
      this.statusTarget.innerHTML = `
        <div class="inline-flex items-center text-red-500 text-xs" title="${message}">
          <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
          </svg>
          Error
        </div>
      `

      // Clear error after 3 seconds
      setTimeout(() => {
        if (this.hasStatusTarget) {
          this.statusTarget.innerHTML = ""
        }
      }, 3000)
    }
  }

  // Auto-refresh functionality
  startAutoRefresh(intervalMs = 30000) {
    this.stopAutoRefresh()
    this.refreshInterval = setInterval(() => {
      this.refresh()
    }, intervalMs)
  }

  stopAutoRefresh() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval)
      this.refreshInterval = null
    }
  }

  disconnect() {
    this.stopAutoRefresh()
  }
}
