import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = { autoDismiss: Number }

  connect() {
    if (this.autoDismissValue > 0) {
      this.timeout = setTimeout(() => {
        this.dismiss()
      }, this.autoDismissValue)
    }
  }

  dismiss() {
    // Clear timeout if it exists
    if (this.timeout) {
      clearTimeout(this.timeout)
    }

    // Animate out
    this.element.classList.add("opacity-0", "transform", "scale-95")
    
    // Remove after animation
    setTimeout(() => {
      if (this.element.parentNode) {
        this.element.remove()
      }
    }, 300)
  }

  disconnect() {
    // Clear timeout on disconnect
    if (this.timeout) {
      clearTimeout(this.timeout)
    }
  }
}
