import { Controller } from "@hotwired/stimulus"

// Sophisticated Form Validation Controller
// Provides real-time validation with accessibility and user-friendly feedback
export default class extends Controller {
  static targets = ["field", "error"]
  
  static values = {
    rules: { type: Object, default: {} },
    realTime: { type: Boolean, default: true }
  }

  connect() {
    this.setupValidationRules()
    this.initializeFields()
    console.log("Form Validation connected with real-time feedback")
  }

  setupValidationRules() {
    this.validationRules = {
      first_name: {
        required: true,
        minLength: 2,
        pattern: /^[a-zA-Z\s'-]+$/,
        message: "First name must be at least 2 characters and contain only letters"
      },
      last_name: {
        required: true,
        minLength: 2,
        pattern: /^[a-zA-Z\s'-]+$/,
        message: "Last name must be at least 2 characters and contain only letters"
      },
      email: {
        required: true,
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: "Please enter a valid email address"
      },
      phone: {
        required: false,
        pattern: /^[\+]?[1-9][\d]{0,15}$/,
        message: "Please enter a valid phone number"
      },
      bio: {
        required: false,
        maxLength: 500,
        message: "<PERSON><PERSON> must be less than 500 characters"
      }
    }
  }

  initializeFields() {
    this.fieldTargets.forEach(field => {
      this.setupFieldValidation(field)
    })
  }

  setupFieldValidation(field) {
    // Add visual indicators for required fields
    const fieldName = this.getFieldName(field)
    const rules = this.validationRules[fieldName]
    
    if (rules?.required) {
      field.setAttribute('aria-required', 'true')
    }
    
    // Add character counter for fields with maxLength
    if (rules?.maxLength) {
      this.addCharacterCounter(field, rules.maxLength)
    }
  }

  validateField(event) {
    const field = event.target
    const fieldName = this.getFieldName(field)
    const value = field.value.trim()
    const rules = this.validationRules[fieldName]
    
    if (!rules) return
    
    const validation = this.performValidation(value, rules, fieldName)
    this.updateFieldUI(field, validation)
    
    // Announce validation results to screen readers
    if (!validation.isValid) {
      this.announceError(validation.message)
    }
  }

  performValidation(value, rules, fieldName) {
    const result = {
      isValid: true,
      message: '',
      type: 'success'
    }
    
    // Required field validation
    if (rules.required && !value) {
      result.isValid = false
      result.message = `${this.formatFieldName(fieldName)} is required`
      result.type = 'error'
      return result
    }
    
    // Skip other validations if field is empty and not required
    if (!value && !rules.required) {
      return result
    }
    
    // Minimum length validation
    if (rules.minLength && value.length < rules.minLength) {
      result.isValid = false
      result.message = `${this.formatFieldName(fieldName)} must be at least ${rules.minLength} characters`
      result.type = 'error'
      return result
    }
    
    // Maximum length validation
    if (rules.maxLength && value.length > rules.maxLength) {
      result.isValid = false
      result.message = `${this.formatFieldName(fieldName)} must be less than ${rules.maxLength} characters`
      result.type = 'error'
      return result
    }
    
    // Pattern validation
    if (rules.pattern && !rules.pattern.test(value)) {
      result.isValid = false
      result.message = rules.message || `${this.formatFieldName(fieldName)} format is invalid`
      result.type = 'error'
      return result
    }
    
    // Special email validation
    if (fieldName === 'email' && value) {
      const emailValidation = this.validateEmail(value)
      if (!emailValidation.isValid) {
        return emailValidation
      }
    }
    
    // Success state
    result.message = `${this.formatFieldName(fieldName)} looks good!`
    result.type = 'success'
    
    return result
  }

  validateEmail(email) {
    const result = { isValid: true, message: '', type: 'success' }
    
    // Basic format check
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      result.isValid = false
      result.message = 'Please enter a valid email address'
      result.type = 'error'
      return result
    }
    
    // Check for common typos
    const commonDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com']
    const domain = email.split('@')[1]
    
    if (domain && !commonDomains.includes(domain)) {
      // This could be enhanced with a domain suggestion feature
      result.message = 'Email looks good!'
      result.type = 'success'
    }
    
    return result
  }

  updateFieldUI(field, validation) {
    const fieldName = this.getFieldName(field)
    const errorTarget = this.errorTargets.find(target => 
      target.dataset.field === fieldName
    )
    
    // Reset field styles
    field.classList.remove(
      'border-red-300', 'border-green-300', 'border-yellow-300',
      'bg-red-50', 'bg-green-50', 'bg-yellow-50',
      'focus:ring-red-500', 'focus:ring-green-500', 'focus:ring-yellow-500',
      'focus:border-red-500', 'focus:border-green-500', 'focus:border-yellow-500'
    )
    
    // Apply validation styles
    if (validation.isValid) {
      field.classList.add(
        'border-green-300', 'bg-green-50',
        'focus:ring-green-500', 'focus:border-green-500'
      )
      field.setAttribute('aria-invalid', 'false')
    } else {
      field.classList.add(
        'border-red-300', 'bg-red-50',
        'focus:ring-red-500', 'focus:border-red-500'
      )
      field.setAttribute('aria-invalid', 'true')
    }
    
    // Update error message
    if (errorTarget) {
      if (validation.isValid) {
        errorTarget.classList.add('hidden')
        errorTarget.textContent = ''
      } else {
        errorTarget.classList.remove('hidden')
        errorTarget.textContent = validation.message
        errorTarget.setAttribute('role', 'alert')
      }
    }
    
    // Add success/error icon
    this.updateFieldIcon(field, validation)
  }

  updateFieldIcon(field, validation) {
    // Remove existing icons
    const existingIcon = field.parentNode.querySelector('.validation-icon')
    if (existingIcon) {
      existingIcon.remove()
    }
    
    // Create new icon
    const icon = document.createElement('div')
    icon.className = 'absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none validation-icon'
    
    if (validation.isValid) {
      icon.innerHTML = `
        <svg class="h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
        </svg>
      `
    } else {
      icon.innerHTML = `
        <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
        </svg>
      `
    }
    
    // Make field container relative if not already
    if (field.parentNode.style.position !== 'relative') {
      field.parentNode.style.position = 'relative'
    }
    
    field.parentNode.appendChild(icon)
  }

  addCharacterCounter(field, maxLength) {
    const counter = document.createElement('div')
    counter.className = 'mt-1 text-xs text-gray-500 character-counter'
    counter.textContent = `0 / ${maxLength} characters`
    
    field.parentNode.appendChild(counter)
    
    // Update counter on input
    field.addEventListener('input', (event) => {
      const currentLength = event.target.value.length
      counter.textContent = `${currentLength} / ${maxLength} characters`
      
      if (currentLength > maxLength * 0.9) {
        counter.classList.add('text-yellow-600')
        counter.classList.remove('text-gray-500', 'text-red-600')
      } else if (currentLength > maxLength) {
        counter.classList.add('text-red-600')
        counter.classList.remove('text-gray-500', 'text-yellow-600')
      } else {
        counter.classList.add('text-gray-500')
        counter.classList.remove('text-yellow-600', 'text-red-600')
      }
    })
  }

  // Utility methods
  getFieldName(field) {
    return field.name?.split('[').pop()?.replace(']', '') || field.id
  }

  formatFieldName(fieldName) {
    return fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  announceError(message) {
    // Create or update screen reader announcement
    let announcer = document.getElementById('validation-announcer')
    if (!announcer) {
      announcer = document.createElement('div')
      announcer.id = 'validation-announcer'
      announcer.setAttribute('aria-live', 'assertive')
      announcer.setAttribute('aria-atomic', 'true')
      announcer.className = 'sr-only'
      document.body.appendChild(announcer)
    }
    
    announcer.textContent = `Validation error: ${message}`
  }

  // Public API for external validation
  validateForm() {
    let isFormValid = true
    const errors = []
    
    this.fieldTargets.forEach(field => {
      const fieldName = this.getFieldName(field)
      const value = field.value.trim()
      const rules = this.validationRules[fieldName]
      
      if (rules) {
        const validation = this.performValidation(value, rules, fieldName)
        this.updateFieldUI(field, validation)
        
        if (!validation.isValid) {
          isFormValid = false
          errors.push({
            field: fieldName,
            message: validation.message
          })
        }
      }
    })
    
    return {
      isValid: isFormValid,
      errors: errors
    }
  }

  // Reset validation state
  resetValidation() {
    this.fieldTargets.forEach(field => {
      field.classList.remove(
        'border-red-300', 'border-green-300',
        'bg-red-50', 'bg-green-50',
        'focus:ring-red-500', 'focus:ring-green-500',
        'focus:border-red-500', 'focus:border-green-500'
      )
      field.removeAttribute('aria-invalid')
      
      // Remove validation icons
      const icon = field.parentNode.querySelector('.validation-icon')
      if (icon) icon.remove()
    })
    
    this.errorTargets.forEach(errorTarget => {
      errorTarget.classList.add('hidden')
      errorTarget.textContent = ''
    })
  }
}
