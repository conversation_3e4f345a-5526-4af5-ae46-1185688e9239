import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["mobileOverlay", "mobileButton"]

  connect() {
    this.isOpen = false
  }

  toggleMobile() {
    if (this.isOpen) {
      this.closeMobile()
    } else {
      this.openMobile()
    }
  }

  openMobile() {
    this.isOpen = true
    this.mobileOverlayTarget.style.display = "flex"
    
    // Animate in
    requestAnimationFrame(() => {
      this.mobileOverlayTarget.classList.add("opacity-100")
      this.mobileOverlayTarget.classList.remove("opacity-0")
    })
    
    // Prevent body scroll
    document.body.style.overflow = "hidden"
    
    // Add escape key listener
    document.addEventListener("keydown", this.handleEscape.bind(this))
  }

  closeMobile() {
    this.isOpen = false
    
    // Animate out
    this.mobileOverlayTarget.classList.add("opacity-0")
    this.mobileOverlayTarget.classList.remove("opacity-100")
    
    // Hide after animation
    setTimeout(() => {
      if (!this.isOpen) {
        this.mobileOverlayTarget.style.display = "none"
      }
    }, 150)
    
    // Restore body scroll
    document.body.style.overflow = ""
    
    // Remove escape key listener
    document.removeEventListener("keydown", this.handleEscape.bind(this))
  }

  handleEscape(event) {
    if (event.key === "Escape") {
      this.closeMobile()
    }
  }

  disconnect() {
    // Cleanup
    document.body.style.overflow = ""
    document.removeEventListener("keydown", this.handleEscape.bind(this))
  }
}
