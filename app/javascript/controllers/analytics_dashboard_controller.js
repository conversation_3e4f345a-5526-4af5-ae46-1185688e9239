import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "rangeButton",
    "revenueTrendChart", 
    "activityHeatmapChart",
    "pipelinePerformanceChart",
    "dataSourceChart",
    "dataQualityGauge",
    "activityFeed"
  ]
  
  static values = {
    refreshInterval: Number
  }

  connect() {
    console.log("Analytics Dashboard controller connected")
    this.selectedRange = "7d"
    this.refreshTimer = null
    
    // Initialize real-time updates
    this.startAutoRefresh()
    
    // Initialize activity feed
    this.initializeActivityFeed()
    
    // Set up WebSocket for real-time data
    this.setupWebSocket()
  }

  disconnect() {
    this.stopAutoRefresh()
    if (this.websocket) {
      this.websocket.close()
    }
  }

  setRange(event) {
    const button = event.currentTarget
    const range = button.dataset.range
    
    // Update button styles
    this.rangeButtonTargets.forEach(btn => {
      btn.classList.remove("bg-indigo-600", "text-white")
      btn.classList.add("text-gray-700", "hover:bg-gray-100")
    })
    
    button.classList.remove("text-gray-700", "hover:bg-gray-100")
    button.classList.add("bg-indigo-600", "text-white")
    
    this.selectedRange = range
    this.refreshData()
  }

  refresh() {
    console.log("Refreshing analytics data...")
    this.refreshData()
    
    // Add rotation animation to refresh button
    const refreshButton = event.currentTarget
    refreshButton.classList.add("animate-spin")
    setTimeout(() => {
      refreshButton.classList.remove("animate-spin")
    }, 1000)
  }

  export() {
    console.log("Exporting analytics data...")
    
    // Prepare data for export
    const exportData = {
      range: this.selectedRange,
      timestamp: new Date().toISOString(),
      metrics: this.gatherMetrics()
    }
    
    // Create and download CSV
    this.downloadCSV(exportData)
  }

  refreshData() {
    // Fetch new data based on selected range
    fetch(`/api/analytics?range=${this.selectedRange}`)
      .then(response => response.json())
      .then(data => {
        this.updateCharts(data)
        this.updateMetrics(data)
      })
      .catch(error => {
        console.error("Error fetching analytics data:", error)
      })
  }

  updateCharts(data) {
    // Update each chart with new data
    // This would integrate with the Chart.js instances
    console.log("Updating charts with new data", data)
  }

  updateMetrics(data) {
    // Update KPI cards and other metrics
    console.log("Updating metrics with new data", data)
  }

  startAutoRefresh() {
    if (this.refreshIntervalValue && this.refreshIntervalValue > 0) {
      this.refreshTimer = setInterval(() => {
        this.refreshData()
      }, this.refreshIntervalValue)
    }
  }

  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  initializeActivityFeed() {
    // Simulate real-time activity updates
    setInterval(() => {
      this.addActivityItem()
    }, 5000)
  }

  addActivityItem() {
    const activities = [
      {
        type: 'success',
        icon: 'check',
        color: 'green',
        title: 'Pipeline completed',
        detail: 'Customer ETL',
        meta: '1.5M rows processed'
      },
      {
        type: 'warning',
        icon: 'alert',
        color: 'yellow',
        title: 'High latency detected',
        detail: 'API Gateway',
        meta: '> 500ms response time'
      },
      {
        type: 'info',
        icon: 'database',
        color: 'blue',
        title: 'New connection established',
        detail: 'MongoDB Cluster',
        meta: 'Production environment'
      },
      {
        type: 'error',
        icon: 'x',
        color: 'red',
        title: 'Pipeline failed',
        detail: 'Sales Sync',
        meta: 'Connection timeout'
      }
    ]
    
    const activity = activities[Math.floor(Math.random() * activities.length)]
    const timestamp = new Date().toLocaleTimeString()
    
    const activityHtml = `
      <div class="flex items-start space-x-3 animate-fade-in">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-${activity.color}-100 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-${activity.color}-600" fill="currentColor" viewBox="0 0 20 20">
              ${this.getIconPath(activity.icon)}
            </svg>
          </div>
        </div>
        <div class="flex-1">
          <p class="text-sm text-gray-900">
            <span class="font-medium">${activity.title}:</span> ${activity.detail}
          </p>
          <p class="text-xs text-gray-500">${timestamp} • ${activity.meta}</p>
        </div>
      </div>
    `
    
    if (this.hasActivityFeedTarget) {
      // Add new item at the top
      this.activityFeedTarget.insertAdjacentHTML('afterbegin', activityHtml)
      
      // Keep only the last 10 items
      const items = this.activityFeedTarget.querySelectorAll('.flex')
      if (items.length > 10) {
        items[items.length - 1].remove()
      }
    }
  }

  getIconPath(icon) {
    const icons = {
      check: '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>',
      alert: '<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>',
      database: '<path d="M3 12v3c0 1.657 3.134 3 7 3s7-1.343 7-3v-3c0 1.657-3.134 3-7 3s-7-1.343-7-3z"/><path d="M3 7v3c0 1.657 3.134 3 7 3s7-1.343 7-3V7c0 1.657-3.134 3-7 3S3 8.657 3 7z"/><path d="M17 5c0 1.657-3.134 3-7 3S3 6.657 3 5s3.134-3 7-3 7 1.343 7 3z"/>',
      x: '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>'
    }
    return icons[icon] || icons.check
  }

  setupWebSocket() {
    // Set up WebSocket connection for real-time updates
    // This would connect to your ActionCable or other WebSocket server
    console.log("Setting up WebSocket connection for real-time analytics")
  }

  gatherMetrics() {
    // Gather current metrics for export
    return {
      revenue: "$124,567",
      activeUsers: "8,234",
      conversionRate: "3.24%",
      dataProcessed: "2.4TB",
      // Add more metrics as needed
    }
  }

  downloadCSV(data) {
    // Convert data to CSV format
    const csv = this.convertToCSV(data)
    
    // Create download link
    const blob = new Blob([csv], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `analytics_export_${this.selectedRange}_${Date.now()}.csv`
    link.click()
    window.URL.revokeObjectURL(url)
  }

  convertToCSV(data) {
    // Simple CSV conversion
    let csv = "Metric,Value\n"
    Object.entries(data.metrics).forEach(([key, value]) => {
      csv += `"${key}","${value}"\n`
    })
    return csv
  }
}