import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["metricCard", "quickAction"]
  static values = { 
    refreshInterval: { type: Number, default: 30000 }, // 30 seconds
    autoRefresh: { type: <PERSON><PERSON>an, default: false }
  }

  connect() {
    this.setupMetricCards()
    this.setupQuickActions()
    
    if (this.autoRefreshValue) {
      this.startAutoRefresh()
    }
  }

  setupMetricCards() {
    this.metricCardTargets.forEach(card => {
      // Add hover effects
      card.addEventListener("mouseenter", this.handleCardHover.bind(this))
      card.addEventListener("mouseleave", this.handleCardLeave.bind(this))
      
      // Add click analytics tracking
      card.addEventListener("click", this.handleCardClick.bind(this))
    })
  }

  setupQuickActions() {
    this.quickActionTargets.forEach(action => {
      // Add loading states for buttons
      action.addEventListener("click", this.handleQuickActionClick.bind(this))
    })
  }

  handleCardHover(event) {
    const card = event.currentTarget
    card.classList.add("transform", "scale-105", "shadow-lg")
    card.style.transition = "all 0.2s ease-in-out"
  }

  handleCardLeave(event) {
    const card = event.currentTarget
    card.classList.remove("transform", "scale-105", "shadow-lg")
  }

  handleCardClick(event) {
    const card = event.currentTarget
    const metricType = card.dataset.metric

    // Track analytics
    console.log(`Metric card clicked: ${metricType}`)

    // Add click animation
    card.classList.add("animate-pulse")
    setTimeout(() => {
      card.classList.remove("animate-pulse")
    }, 200)

    // Navigate to detailed view based on metric type
    this.navigateToDetailView(metricType)
  }

  navigateToDetailView(metricType) {
    let targetUrl = null

    switch (metricType) {
      case 'projects':
        targetUrl = '/projects'
        break
      case 'pipelines':
        targetUrl = '/pipelines'
        break
      case 'connectors':
        targetUrl = '/connectors'
        break
      case 'analytics':
        targetUrl = '/analytics'
        break
      case 'data_processed':
        targetUrl = '/analytics/usage_trends'
        break
      default:
        // Show detailed modal instead of navigation
        this.showMetricModal(metricType)
        return
    }

    if (targetUrl) {
      // Use Turbo for smooth navigation
      if (window.Turbo) {
        window.Turbo.visit(targetUrl)
      } else {
        window.location.href = targetUrl
      }
    }
  }

  showMetricModal(metricType) {
    // Create and show a modal with detailed metric information
    const modal = this.createMetricModal(metricType)
    document.body.appendChild(modal)

    // Show modal with animation
    requestAnimationFrame(() => {
      modal.classList.remove('opacity-0')
      modal.classList.add('opacity-100')
    })
  }

  createMetricModal(metricType) {
    const modal = document.createElement('div')
    modal.className = 'fixed inset-0 z-50 overflow-y-auto opacity-0 transition-opacity duration-300'
    modal.setAttribute('data-controller', 'modal')
    modal.setAttribute('data-action', 'click->modal#closeOnBackdrop')

    modal.innerHTML = `
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 sm:mx-0 sm:h-10 sm:w-10">
                <svg class="h-6 w-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                </svg>
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                  ${this.getMetricTitle(metricType)}
                </h3>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    ${this.getMetricDescription(metricType)}
                  </p>
                  <div class="mt-4" data-metric-details="${metricType}">
                    ${this.getMetricDetails(metricType)}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="button"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
                    data-action="click->modal#close">
              View Details
            </button>
            <button type="button"
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    data-action="click->modal#close">
              Close
            </button>
          </div>
        </div>
      </div>
    `

    return modal
  }

  getMetricTitle(metricType) {
    const titles = {
      pipelines: 'Pipeline Performance',
      connectors: 'Connection Health',
      data_processed: 'Data Processing',
      system_health: 'System Status'
    }
    return titles[metricType] || 'Metric Details'
  }

  getMetricDescription(metricType) {
    const descriptions = {
      pipelines: 'Detailed information about your data pipelines and their performance metrics.',
      connectors: 'Health status and performance of your data connections.',
      data_processed: 'Volume and trends of data processed through your pipelines.',
      system_health: 'Overall system performance and health indicators.'
    }
    return descriptions[metricType] || 'Detailed metric information and trends.'
  }

  getMetricDetails(metricType) {
    // This would be populated with real data from the metrics
    return `
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">Current Value:</span>
          <span class="text-sm text-gray-900">Loading...</span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">Trend:</span>
          <span class="text-sm text-green-600">↗ +5.2%</span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">Last Updated:</span>
          <span class="text-sm text-gray-900">Just now</span>
        </div>
      </div>
    `
  }

  handleQuickActionClick(event) {
    const button = event.currentTarget
    const actionType = button.dataset.action
    
    // Add loading state
    this.setButtonLoading(button, true)
    
    // Track action (placeholder for future implementation)
    console.log(`Quick action clicked: ${actionType}`)
    
    // Simulate action completion (remove in real implementation)
    setTimeout(() => {
      this.setButtonLoading(button, false)
    }, 1000)
  }

  setButtonLoading(button, loading) {
    if (loading) {
      button.disabled = true
      button.classList.add("opacity-75", "cursor-not-allowed")
      
      // Add spinner if not already present
      if (!button.querySelector('.spinner')) {
        const spinner = document.createElement('div')
        spinner.className = 'spinner inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2'
        button.prepend(spinner)
      }
    } else {
      button.disabled = false
      button.classList.remove("opacity-75", "cursor-not-allowed")
      
      // Remove spinner
      const spinner = button.querySelector('.spinner')
      if (spinner) {
        spinner.remove()
      }
    }
  }

  refreshMetrics() {
    // Placeholder for future AJAX metric refresh
    console.log("Refreshing dashboard metrics...")
    
    // Add visual feedback
    this.metricCardTargets.forEach(card => {
      card.classList.add("animate-pulse")
    })
    
    // Simulate refresh (replace with actual AJAX call)
    setTimeout(() => {
      this.metricCardTargets.forEach(card => {
        card.classList.remove("animate-pulse")
      })
    }, 1000)
  }

  startAutoRefresh() {
    this.refreshTimer = setInterval(() => {
      this.refreshMetrics()
    }, this.refreshIntervalValue)
  }

  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  disconnect() {
    this.stopAutoRefresh()
    
    // Clean up event listeners
    this.metricCardTargets.forEach(card => {
      card.removeEventListener("mouseenter", this.handleCardHover.bind(this))
      card.removeEventListener("mouseleave", this.handleCardLeave.bind(this))
      card.removeEventListener("click", this.handleCardClick.bind(this))
    })
    
    this.quickActionTargets.forEach(action => {
      action.removeEventListener("click", this.handleQuickActionClick.bind(this))
    })
  }
}
