import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "step", "progressBar", "stepNumber", "stepTitle", "stepIndicator",
    "prevButton", "nextButton", "submitButton", "autoSaveIndicator",
    "sourceLabel", "transformLabel", "destinationLabel", "sourceConnection",
    "destinationConnection", "sourceJsonValid", "sourceJsonInvalid",
    "destinationJsonValid", "destinationJsonInvalid", "transformJsonValid",
    "transformJsonInvalid", "previewName", "previewDescription", "previewSource",
    "previewTransform", "previewDestination", "summaryName", "summaryTemplate",
    "summarySchedule", "summaryStatus", "validationResults", "validateButton"
  ]
  static values = { 
    currentStep: Number,
    autoSave: Boolean,
    validationEnabled: Boolean
  }

  connect() {
    console.log('Pipeline wizard controller connected')
    this.currentStepValue = 1
    this.autoSaveValue = true
    this.validationEnabledValue = true
    this.autoSaveTimer = null
    this.validationCache = new Map()

    // Initialize selected template from form field (for edit mode)
    const templateField = this.element.querySelector('[name*="template"]') ||
                         this.element.querySelector('#pipeline_template_type')
    this.selectedTemplate = templateField ? templateField.value : null

    this.updateProgress()
    this.showStep(this.currentStepValue)
    this.updateNavigationButtons()
    this.initializeAutoSave()
    this.bindFormEvents()

    console.log('Pipeline wizard initialized with', this.stepTargets.length, 'steps')
    console.log('Initial selected template:', this.selectedTemplate)
  }

  disconnect() {
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer)
    }
  }

  // Template Selection
  selectTemplate(event) {
    console.log('Template selection clicked', event.currentTarget)
    const templateCard = event.currentTarget
    const templateType = templateCard.dataset.template

    console.log('Selected template type:', templateType)

    // Remove selection from all template cards
    const allCards = this.element.querySelectorAll('.pipeline-template-card')
    allCards.forEach(card => {
      card.classList.remove('selected')
      card.style.borderColor = ''
      card.style.backgroundColor = ''
    })

    // Select current template card
    templateCard.classList.add('selected')
    templateCard.style.borderColor = '#3b82f6'
    templateCard.style.backgroundColor = '#eff6ff'

    this.selectedTemplate = templateType

    // Update hidden form field
    const templateField = this.element.querySelector('[name*="template"]') ||
                         this.element.querySelector('#pipeline_template_type')
    if (templateField) {
      templateField.value = templateType
      console.log('Updated template field value:', templateField.value)
    } else {
      console.warn('Template field not found')
    }

    // Show template details
    this.showTemplateDetails(templateType)

    // Enable next button
    if (this.hasNextButtonTarget) {
      this.nextButtonTarget.disabled = false
      console.log('Next button enabled')
    } else {
      console.warn('Next button target not found')
    }

    // Dispatch custom event
    this.element.dispatchEvent(new CustomEvent('template:selected', {
      detail: { template: templateType }
    }))
  }

  // Schedule Selection
  selectScheduleOption(event) {
    console.log('Schedule option clicked', event.currentTarget)
    const scheduleLabel = event.currentTarget
    const scheduleValue = scheduleLabel.dataset.scheduleValue

    console.log('Selected schedule type:', scheduleValue)

    // Remove selection from all schedule options
    const allOptions = this.element.querySelectorAll('.schedule-option')
    allOptions.forEach(option => {
      option.classList.remove('selected', 'border-indigo-500', 'bg-indigo-50')
      option.classList.add('border-gray-200')

      // Update radio button visual indicator
      const radioIndicator = option.querySelector('.w-4.h-4 > div')
      const radioBorder = option.querySelector('.w-4.h-4')
      if (radioIndicator && radioBorder) {
        radioIndicator.classList.add('hidden')
        radioBorder.classList.remove('border-indigo-600')
        radioBorder.classList.add('border-gray-300')
      }
    })

    // Select current schedule option
    scheduleLabel.classList.add('selected', 'border-indigo-500', 'bg-indigo-50')
    scheduleLabel.classList.remove('border-gray-200')

    // Update radio button visual indicator
    const radioIndicator = scheduleLabel.querySelector('.w-4.h-4 > div')
    const radioBorder = scheduleLabel.querySelector('.w-4.h-4')
    if (radioIndicator && radioBorder) {
      radioIndicator.classList.remove('hidden')
      radioBorder.classList.add('border-indigo-600')
      radioBorder.classList.remove('border-gray-300')
    }

    // Update the actual radio button
    const radioButton = scheduleLabel.querySelector('input[type="radio"]')
    if (radioButton) {
      radioButton.checked = true
      console.log('Updated radio button value:', radioButton.value)
    }

    // Show/hide custom schedule config
    this.toggleCustomScheduleConfig(scheduleValue)

    // Dispatch custom event
    this.element.dispatchEvent(new CustomEvent('schedule:selected', {
      detail: { schedule: scheduleValue }
    }))
  }

  toggleCustomScheduleConfig(scheduleType) {
    const customConfigDiv = this.element.querySelector('#schedule-config')
    if (customConfigDiv) {
      if (scheduleType === 'cron') {
        customConfigDiv.style.display = 'block'
      } else {
        customConfigDiv.style.display = 'none'
      }
    }
  }

  // Data Source Selection
  selectDataSourceType(event) {
    console.log('Data source type clicked', event.currentTarget)
    const sourceOption = event.currentTarget
    const sourceType = sourceOption.dataset.sourceType

    console.log('Selected source type:', sourceType)

    // Remove selection from all source options
    const allOptions = this.element.querySelectorAll('.data-source-option')
    allOptions.forEach(option => {
      option.classList.remove('border-blue-500', 'bg-blue-50')
      option.classList.add('border-blue-200', 'bg-white')

      // Update radio button visual indicator
      const radioIndicator = option.querySelector('.w-4.h-4 > div')
      const radioBorder = option.querySelector('.w-4.h-4')
      if (radioIndicator && radioBorder) {
        radioIndicator.classList.add('hidden')
        radioBorder.classList.remove('border-blue-600')
        radioBorder.classList.add('border-gray-300')
      }
    })

    // Select current source option
    sourceOption.classList.add('border-blue-500', 'bg-blue-50')
    sourceOption.classList.remove('border-blue-200', 'bg-white')

    // Update radio button visual indicator
    const radioIndicator = sourceOption.querySelector('.w-4.h-4 > div')
    const radioBorder = sourceOption.querySelector('.w-4.h-4')
    if (radioIndicator && radioBorder) {
      radioIndicator.classList.remove('hidden')
      radioBorder.classList.add('border-blue-600')
      radioBorder.classList.remove('border-gray-300')
    }

    // Show/hide appropriate configuration forms
    this.showSourceConfigForm(sourceType)

    // Update the configuration
    this.updateSourceConfig()
  }

  // Data Destination Selection
  selectDataDestType(event) {
    console.log('Data destination type clicked', event.currentTarget)
    const destOption = event.currentTarget
    const destType = destOption.dataset.destType

    console.log('Selected destination type:', destType)

    // Remove selection from all destination options
    const allOptions = this.element.querySelectorAll('.data-dest-option')
    allOptions.forEach(option => {
      option.classList.remove('border-green-500', 'bg-green-50')
      option.classList.add('border-green-200', 'bg-white')

      // Update radio button visual indicator
      const radioIndicator = option.querySelector('.w-4.h-4 > div')
      const radioBorder = option.querySelector('.w-4.h-4')
      if (radioIndicator && radioBorder) {
        radioIndicator.classList.add('hidden')
        radioBorder.classList.remove('border-green-600')
        radioBorder.classList.add('border-gray-300')
      }
    })

    // Select current destination option
    destOption.classList.add('border-green-500', 'bg-green-50')
    destOption.classList.remove('border-green-200', 'bg-white')

    // Update radio button visual indicator
    const radioIndicator = destOption.querySelector('.w-4.h-4 > div')
    const radioBorder = destOption.querySelector('.w-4.h-4')
    if (radioIndicator && radioBorder) {
      radioIndicator.classList.remove('hidden')
      radioBorder.classList.add('border-green-600')
      radioBorder.classList.remove('border-gray-300')
    }

    // Show/hide appropriate configuration forms
    this.showDestConfigForm(destType)

    // Update the configuration
    this.updateDestConfig()
  }

  showSourceConfigForm(sourceType) {
    // Hide all source config forms
    const allForms = this.element.querySelectorAll('.source-config-form')
    allForms.forEach(form => {
      form.classList.add('hidden')
    })

    // Show the selected form
    const selectedForm = this.element.querySelector(`.source-config-form[data-source-type="${sourceType}"]`)
    if (selectedForm) {
      selectedForm.classList.remove('hidden')
    }
  }

  showDestConfigForm(destType) {
    // Hide all destination config forms
    const allForms = this.element.querySelectorAll('.dest-config-form')
    allForms.forEach(form => {
      form.classList.add('hidden')
    })

    // Show the selected form
    const selectedForm = this.element.querySelector(`.dest-config-form[data-dest-type="${destType}"]`)
    if (selectedForm) {
      selectedForm.classList.remove('hidden')
    }
  }

  updateSourceConfig() {
    const selectedOption = this.element.querySelector('.data-source-option.border-blue-500')
    if (!selectedOption) return

    const sourceType = selectedOption.dataset.sourceType
    const configForm = this.element.querySelector(`.source-config-form[data-source-type="${sourceType}"]`)

    if (configForm) {
      const config = { type: sourceType }

      // Collect all configuration fields
      const configFields = configForm.querySelectorAll('[data-config-field]')
      configFields.forEach(field => {
        const fieldName = field.dataset.configField
        config[fieldName] = field.value
      })

      // Update the hidden field
      const hiddenField = this.element.querySelector('[data-pipeline-wizard-target="sourceConfigField"]')
      if (hiddenField) {
        hiddenField.value = JSON.stringify(config)
        console.log('Updated source config:', config)
      }
    }
  }

  updateDestConfig() {
    const selectedOption = this.element.querySelector('.data-dest-option.border-green-500')
    if (!selectedOption) return

    const destType = selectedOption.dataset.destType
    const configForm = this.element.querySelector(`.dest-config-form[data-dest-type="${destType}"]`)

    if (configForm) {
      const config = { type: destType }

      // Collect all configuration fields
      const configFields = configForm.querySelectorAll('[data-dest-config-field]')
      configFields.forEach(field => {
        const fieldName = field.dataset.destConfigField
        config[fieldName] = field.value
      })

      // Update the hidden field
      const hiddenField = this.element.querySelector('[data-pipeline-wizard-target="destConfigField"]')
      if (hiddenField) {
        hiddenField.value = JSON.stringify(config)
        console.log('Updated destination config:', config)
      }
    }
  }

  toggleTransformations(event) {
    const checkbox = event.target
    const transformationOptions = this.element.querySelector('#transformation-options')

    if (transformationOptions) {
      if (checkbox.checked) {
        transformationOptions.classList.remove('hidden')
      } else {
        transformationOptions.classList.add('hidden')
        // Clear transformation config
        const hiddenField = this.element.querySelector('[data-pipeline-wizard-target="transformConfigField"]')
        if (hiddenField) {
          hiddenField.value = '{}'
        }
      }
    }
  }

  showTemplateDetails(templateType) {
    // Template configurations
    const templateConfigs = {
      etl: {
        name: 'ETL Pipeline',
        description: 'Extract, Transform, Load data between systems',
        defaultSchedule: 'hourly',
        suggestedConfig: {
          source: { type: 'database', query: 'SELECT * FROM source_table' },
          destination: { type: 'database', table: 'destination_table' },
          transformations: { rules: [{ type: 'mapping', fields: {} }] }
        }
      },
      sync: {
        name: 'Data Sync',
        description: 'Keep data synchronized between systems',
        defaultSchedule: 'real-time',
        suggestedConfig: {
          source: { type: 'api', endpoint: '/api/data' },
          destination: { type: 'database', table: 'synced_data' },
          transformations: { rules: [] }
        }
      },
      stream: {
        name: 'Stream Processing',
        description: 'Process data streams in real-time',
        defaultSchedule: 'continuous',
        suggestedConfig: {
          source: { type: 'stream', topic: 'data-stream' },
          destination: { type: 'database', table: 'processed_stream' },
          transformations: { rules: [{ type: 'filter', condition: 'value > 0' }] }
        }
      },
      batch: {
        name: 'Batch Processing',
        description: 'Process large datasets in scheduled batches',
        defaultSchedule: 'daily',
        suggestedConfig: {
          source: { type: 'file', path: '/data/batch/*.csv' },
          destination: { type: 'database', table: 'batch_processed' },
          transformations: { rules: [{ type: 'aggregation', function: 'sum' }] }
        }
      },
      migration: {
        name: 'Data Migration',
        description: 'Migrate data between different systems',
        defaultSchedule: 'manual',
        suggestedConfig: {
          source: { type: 'database', legacy: true },
          destination: { type: 'database', modern: true },
          transformations: { rules: [{ type: 'schema_mapping', mappings: {} }] }
        }
      },
      custom: {
        name: 'Custom Pipeline',
        description: 'Build a custom pipeline from scratch',
        defaultSchedule: 'manual',
        suggestedConfig: {
          source: { type: 'custom' },
          destination: { type: 'custom' },
          transformations: { rules: [] }
        }
      }
    }

    const config = templateConfigs[templateType]
    if (config) {
      // Pre-populate form fields with template defaults
      this.populateTemplateDefaults(config)
    }
  }

  populateTemplateDefaults(config) {
    // Populate name field
    const nameField = this.element.querySelector('[name*="name"]')
    if (nameField && !nameField.value) {
      nameField.value = config.name
    }

    // Populate description field
    const descriptionField = this.element.querySelector('[name*="description"]')
    if (descriptionField && !descriptionField.value) {
      descriptionField.value = config.description
    }

    // Store suggested config for later steps
    this.templateConfig = config.suggestedConfig
  }

  // Step Navigation
  nextStep() {
    if (this.validateCurrentStep() && this.currentStepValue < this.stepTargets.length) {
      this.currentStepValue++
      this.updateProgress()
      this.showStep(this.currentStepValue)
      this.updateNavigationButtons()
      this.updatePreview()
    }
  }

  previousStep() {
    if (this.currentStepValue > 1) {
      this.currentStepValue--
      this.updateProgress()
      this.showStep(this.currentStepValue)
      this.updateNavigationButtons()
    }
  }

  showStep(stepNumber) {
    this.stepTargets.forEach((step, index) => {
      if (index + 1 === stepNumber) {
        step.classList.remove("hidden")
        step.classList.add("active", "fade-in")
        step.style.display = "block"
      } else {
        step.classList.add("hidden")
        step.classList.remove("active", "fade-in")
        step.style.display = "none"
      }
    })
  }

  updateProgress() {
    const progress = (this.currentStepValue / this.stepTargets.length) * 100
    if (this.hasProgressBarTarget) {
      this.progressBarTarget.style.width = `${progress}%`
    }
    
    // Update step indicators
    this.stepIndicatorTargets.forEach((indicator, index) => {
      const stepNum = index + 1
      if (stepNum < this.currentStepValue) {
        indicator.classList.remove("bg-gray-300", "bg-indigo-600")
        indicator.classList.add("bg-green-500")
      } else if (stepNum === this.currentStepValue) {
        indicator.classList.remove("bg-gray-300", "bg-green-500")
        indicator.classList.add("bg-indigo-600")
      } else {
        indicator.classList.remove("bg-indigo-600", "bg-green-500")
        indicator.classList.add("bg-gray-300")
      }
    })
    
    if (this.hasStepNumberTarget) {
      this.stepNumberTarget.textContent = this.currentStepValue
    }
    
    // Update step title if available
    const currentStep = this.stepTargets[this.currentStepValue - 1]
    if (currentStep && this.hasStepTitleTarget) {
      const title = currentStep.dataset.stepTitle || `Step ${this.currentStepValue}`
      this.stepTitleTarget.textContent = title
    }
  }

  updateNavigationButtons() {
    // Previous button
    if (this.hasPrevButtonTarget) {
      this.prevButtonTarget.disabled = this.currentStepValue === 1
    }

    // Next/Submit buttons
    if (this.hasNextButtonTarget && this.hasSubmitButtonTarget) {
      if (this.currentStepValue === this.stepTargets.length) {
        this.nextButtonTarget.classList.add("hidden")
        this.submitButtonTarget.classList.remove("hidden")
      } else {
        this.nextButtonTarget.classList.remove("hidden")
        this.submitButtonTarget.classList.add("hidden")
      }
    }

    // Enable next button for template selection in edit mode
    if (this.currentStepValue === 1 && this.hasNextButtonTarget) {
      const isEditMode = this.element.querySelector('form').action.includes('/edit') ||
                        this.element.querySelector('form').method.toLowerCase() === 'patch'
      if (isEditMode) {
        this.nextButtonTarget.disabled = false
      }
    }
  }

  // Validation
  validateCurrentStep() {
    const currentStep = this.stepTargets[this.currentStepValue - 1]
    if (!currentStep) return true

    let isValid = true

    // Step-specific validation
    switch (this.currentStepValue) {
      case 1:
        isValid = this.validateTemplateSelection()
        break
      case 2:
        isValid = this.validateBasicSetup()
        break
      case 3:
        isValid = this.validateScheduleSetup()
        break
      case 4:
        isValid = this.validateDataFlow()
        break
      case 5:
        isValid = true // Review step doesn't need validation
        break
      default:
        isValid = this.validateGenericStep(currentStep)
    }

    return isValid
  }

  validateTemplateSelection() {
    // For edit mode, template selection is optional (can keep existing)
    // For new mode, template selection is required
    const isEditMode = this.element.querySelector('form').action.includes('/edit') ||
                      this.element.querySelector('form').method.toLowerCase() === 'patch'

    if (!isEditMode && !this.selectedTemplate) {
      this.showStepError('Please select a pipeline template to continue')
      return false
    }
    this.clearStepError()
    return true
  }

  validateBasicSetup() {
    const currentStep = this.stepTargets[this.currentStepValue - 1]
    const requiredFields = currentStep.querySelectorAll('[required]')
    let isValid = true

    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        this.showFieldError(field, 'This field is required')
        isValid = false
      } else {
        this.clearFieldError(field)
      }
    })

    return isValid
  }

  validateScheduleSetup() {
    const currentStep = this.stepTargets[this.currentStepValue - 1]
    const requiredFields = currentStep.querySelectorAll('[required]')
    let isValid = true

    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        this.showFieldError(field, 'This field is required')
        isValid = false
      } else {
        this.clearFieldError(field)
      }
    })

    // Validate JSON fields if present
    const jsonFields = currentStep.querySelectorAll('.json-editor')
    jsonFields.forEach(field => {
      if (field.value.trim() && !this.isValidJSON(field.value)) {
        this.showFieldError(field, 'Invalid JSON format')
        isValid = false
      }
    })

    return isValid
  }

  validateDataFlow() {
    const currentStep = this.stepTargets[this.currentStepValue - 1]
    let isValid = true

    // Validate required fields
    const requiredFields = currentStep.querySelectorAll('[required]')
    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        this.showFieldError(field, 'This field is required')
        isValid = false
      } else {
        this.clearFieldError(field)
      }
    })

    // Validate JSON fields
    const jsonFields = currentStep.querySelectorAll('.json-editor')
    jsonFields.forEach(field => {
      if (field.value.trim() && !this.isValidJSON(field.value)) {
        this.showFieldError(field, 'Invalid JSON format')
        isValid = false
      }
    })

    return isValid
  }

  validateGenericStep(currentStep) {
    const requiredFields = currentStep.querySelectorAll('[required]')
    let isValid = true

    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        this.showFieldError(field, 'This field is required')
        isValid = false
      } else {
        this.clearFieldError(field)
      }
    })

    // Validate JSON fields
    const jsonFields = currentStep.querySelectorAll('.json-editor')
    jsonFields.forEach(field => {
      if (field.value.trim() && !this.isValidJSON(field.value)) {
        this.showFieldError(field, 'Invalid JSON format')
        isValid = false
      }
    })

    return isValid
  }

  validateJSON(event) {
    const field = event.target
    const value = field.value.trim()
    
    if (!value) {
      this.clearJSONValidation(field)
      return
    }

    if (this.isValidJSON(value)) {
      this.showJSONValid(field)
      this.clearFieldError(field)
    } else {
      this.showJSONInvalid(field)
      this.showFieldError(field, 'Invalid JSON format')
    }
  }

  isValidJSON(str) {
    try {
      JSON.parse(str)
      return true
    } catch (e) {
      return false
    }
  }

  showJSONValid(field) {
    const fieldName = this.getFieldValidationName(field)
    const validTarget = this[`${fieldName}JsonValidTarget`]
    const invalidTarget = this[`${fieldName}JsonInvalidTarget`]
    
    if (validTarget) {
      validTarget.classList.remove("hidden")
    }
    if (invalidTarget) {
      invalidTarget.classList.add("hidden")
    }
  }

  showJSONInvalid(field) {
    const fieldName = this.getFieldValidationName(field)
    const validTarget = this[`${fieldName}JsonValidTarget`]
    const invalidTarget = this[`${fieldName}JsonInvalidTarget`]
    
    if (validTarget) {
      validTarget.classList.add("hidden")
    }
    if (invalidTarget) {
      invalidTarget.classList.remove("hidden")
    }
  }

  clearJSONValidation(field) {
    const fieldName = this.getFieldValidationName(field)
    const validTarget = this[`${fieldName}JsonValidTarget`]
    const invalidTarget = this[`${fieldName}JsonInvalidTarget`]
    
    if (validTarget) {
      validTarget.classList.add("hidden")
    }
    if (invalidTarget) {
      invalidTarget.classList.add("hidden")
    }
  }

  getFieldValidationName(field) {
    if (field.name.includes('source_config')) return 'source'
    if (field.name.includes('destination_config')) return 'destination'
    if (field.name.includes('transformation_rules')) return 'transform'
    return 'default'
  }

  // Field Error Handling
  showFieldError(field, message) {
    this.clearFieldError(field)

    field.classList.add('border-red-500', 'focus:border-red-500', 'focus:ring-red-500')
    field.classList.remove('border-gray-300', 'focus:border-indigo-500', 'focus:ring-indigo-500')

    const errorDiv = document.createElement('div')
    errorDiv.className = 'field-error mt-1 text-sm text-red-600'
    errorDiv.textContent = message

    field.parentNode.appendChild(errorDiv)
  }

  clearFieldError(field) {
    field.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500')
    field.classList.add('border-gray-300', 'focus:border-indigo-500', 'focus:ring-indigo-500')

    const existingError = field.parentNode.querySelector('.field-error')
    if (existingError) {
      existingError.remove()
    }
  }

  // Step Error Handling
  showStepError(message) {
    const currentStep = this.stepTargets[this.currentStepValue - 1]
    if (!currentStep) return

    this.clearStepError()

    const errorDiv = document.createElement('div')
    errorDiv.className = 'step-error mt-4 p-4 bg-red-50 border border-red-200 rounded-lg'
    errorDiv.innerHTML = `
      <div class="flex">
        <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
        </svg>
        <div class="ml-3">
          <p class="text-sm text-red-800">${message}</p>
        </div>
      </div>
    `

    currentStep.appendChild(errorDiv)
  }

  clearStepError() {
    const currentStep = this.stepTargets[this.currentStepValue - 1]
    if (!currentStep) return

    const existingError = currentStep.querySelector('.step-error')
    if (existingError) {
      existingError.remove()
    }
  }

  // Auto-save functionality
  initializeAutoSave() {
    if (!this.autoSaveValue) return

    const form = this.element.querySelector('form')
    if (form) {
      form.addEventListener('input', this.scheduleAutoSave.bind(this))
      form.addEventListener('change', this.scheduleAutoSave.bind(this))
    }
  }

  scheduleAutoSave() {
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer)
    }

    this.autoSaveTimer = setTimeout(() => {
      this.performAutoSave()
    }, 2000) // Auto-save after 2 seconds of inactivity
  }

  async performAutoSave() {
    if (!this.autoSaveValue) return

    try {
      this.showAutoSaveIndicator()
      
      const form = this.element.querySelector('form')
      const formData = new FormData(form)
      formData.append('auto_save', 'true')

      const response = await fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        }
      })

      if (response.ok) {
        this.hideAutoSaveIndicator()
        // Optionally show a brief success indicator
      }
    } catch (error) {
      console.error('Auto-save failed:', error)
      this.hideAutoSaveIndicator()
    }
  }

  showAutoSaveIndicator() {
    if (this.hasAutoSaveIndicatorTarget) {
      this.autoSaveIndicatorTarget.classList.remove("hidden")
      this.autoSaveIndicatorTarget.classList.add("flex")
    }
  }

  hideAutoSaveIndicator() {
    if (this.hasAutoSaveIndicatorTarget) {
      this.autoSaveIndicatorTarget.classList.add("hidden")
      this.autoSaveIndicatorTarget.classList.remove("flex")
    }
  }

  bindFormEvents() {
    const form = this.element.querySelector('form')
    if (form) {
      // Bind JSON validation to text areas
      const jsonFields = form.querySelectorAll('.json-editor')
      jsonFields.forEach(field => {
        field.addEventListener('input', this.validateJSON.bind(this))
        field.addEventListener('blur', this.validateJSON.bind(this))
      })

      // Update configurations when form fields change
      form.addEventListener('input', (event) => {
        if (event.target.matches('[data-config-field]')) {
          this.updateSourceConfig()
        } else if (event.target.matches('[data-dest-config-field]')) {
          this.updateDestConfig()
        }
      })

      form.addEventListener('change', (event) => {
        if (event.target.matches('[data-config-field]')) {
          this.updateSourceConfig()
        } else if (event.target.matches('[data-dest-config-field]')) {
          this.updateDestConfig()
        }
      })
    }
  }

  // Data Flow Configuration
  selectDataSource() {
    // Handle data source selection
    console.log('Data source selection clicked')
    // Could open a modal or navigate to connection selection
  }

  selectDataDestination() {
    // Handle data destination selection
    console.log('Data destination selection clicked')
    // Could open a modal or navigate to connection selection
  }

  configureTransform() {
    // Handle transformation configuration
    console.log('Transform configuration clicked')
    // Could open transformation rule builder
  }

  validateSourceConfig() {
    const sourceConfigField = this.element.querySelector('[name*="source_config"]')
    if (sourceConfigField) {
      this.validateJSON({ target: sourceConfigField })
    }
  }

  validateDestinationConfig() {
    const destinationConfigField = this.element.querySelector('[name*="destination_config"]')
    if (destinationConfigField) {
      this.validateJSON({ target: destinationConfigField })
    }
  }

  openRuleBuilder() {
    // Open transformation rule builder
    console.log('Opening rule builder')
    // Could open a modal with visual rule builder
  }

  // Pipeline Validation and Preview
  async validatePipeline() {
    if (!this.hasValidateButtonTarget) return

    try {
      this.validateButtonTarget.disabled = true
      this.validateButtonTarget.innerHTML = `
        <svg class="animate-spin mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Validating...
      `

      const form = this.element.querySelector('form')
      const formData = new FormData(form)
      formData.append('validate_only', 'true')

      const response = await fetch('/pipelines/validate', {
        method: 'POST',
        body: formData,
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        }
      })

      const result = await response.json()
      this.displayValidationResults(result)

    } catch (error) {
      console.error('Validation failed:', error)
      this.displayValidationError()
    } finally {
      this.validateButtonTarget.disabled = false
      this.validateButtonTarget.innerHTML = `
        <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        Validate Pipeline
      `
    }
  }

  displayValidationResults(result) {
    if (!this.hasValidationResultsTarget) return

    const container = this.validationResultsTarget
    container.innerHTML = ''

    if (result.valid) {
      container.innerHTML = `
        <div class="flex items-center text-sm text-green-600">
          <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
          </svg>
          Pipeline configuration is valid
        </div>
      `
    } else {
      const errors = result.errors || []
      container.innerHTML = errors.map(error => `
        <div class="flex items-center text-sm text-red-600 mb-2">
          <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
          </svg>
          ${error}
        </div>
      `).join('')
    }
  }

  displayValidationError() {
    if (!this.hasValidationResultsTarget) return

    this.validationResultsTarget.innerHTML = `
      <div class="flex items-center text-sm text-red-600">
        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
        </svg>
        Validation failed. Please check your configuration.
      </div>
    `
  }

  updatePreview() {
    // Update the preview section with current form values
    const form = this.element.querySelector('form')
    if (!form) return

    // Update preview name and description
    const nameField = form.querySelector('[name*="name"]')
    const descriptionField = form.querySelector('[name*="description"]')

    if (nameField && this.hasPreviewNameTarget) {
      this.previewNameTarget.textContent = nameField.value || 'Pipeline Name'
    }

    if (descriptionField && this.hasPreviewDescriptionTarget) {
      this.previewDescriptionTarget.textContent = descriptionField.value || 'Pipeline description will appear here'
    }

    // Update summary fields
    if (this.hasSummaryNameTarget && nameField) {
      this.summaryNameTarget.textContent = nameField.value || '-'
    }

    // Update template info
    const templateField = form.querySelector('[name*="template"]')
    if (this.hasSummaryTemplateTarget && templateField) {
      const selectedOption = templateField.options[templateField.selectedIndex]
      this.summaryTemplateTarget.textContent = selectedOption ? selectedOption.text : '-'
    }

    // Update schedule info
    const scheduleField = form.querySelector('[name*="schedule_type"]')
    if (this.hasSummaryScheduleTarget && scheduleField) {
      this.summaryScheduleTarget.textContent = scheduleField.value || '-'
    }

    // Update status
    if (this.hasSummaryStatusTarget) {
      this.summaryStatusTarget.textContent = 'Draft'
    }
  }
}
