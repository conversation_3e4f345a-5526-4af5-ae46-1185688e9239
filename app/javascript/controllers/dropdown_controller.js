import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["button", "menu"]
  static classes = ["open"]

  connect() {
    this.isOpen = false
  }

  toggle(event) {
    event.preventDefault()
    event.stopPropagation()
    
    if (this.isOpen) {
      this.close()
    } else {
      this.open()
    }
  }

  open() {
    // Close other dropdowns first
    this.closeOtherDropdowns()
    
    this.isOpen = true
    this.menuTarget.classList.remove("hidden")
    
    // Animate in
    requestAnimationFrame(() => {
      this.menuTarget.classList.add("opacity-100", "scale-100")
      this.menuTarget.classList.remove("opacity-0", "scale-95")
    })
    
    // Add click outside listener
    document.addEventListener("click", this.handleClickOutside.bind(this))
    document.addEventListener("keydown", this.handleEscape.bind(this))
  }

  close() {
    this.isOpen = false
    
    // Animate out
    this.menuTarget.classList.add("opacity-0", "scale-95")
    this.menuTarget.classList.remove("opacity-100", "scale-100")
    
    // Hide after animation
    setTimeout(() => {
      if (!this.isOpen) {
        this.menuTarget.classList.add("hidden")
      }
    }, 150)
    
    // Remove listeners
    document.removeEventListener("click", this.handleClickOutside.bind(this))
    document.removeEventListener("keydown", this.handleEscape.bind(this))
  }

  handleClickOutside(event) {
    if (!this.element.contains(event.target)) {
      this.close()
    }
  }

  handleEscape(event) {
    if (event.key === "Escape") {
      this.close()
    }
  }

  closeOtherDropdowns() {
    // Find all other dropdown controllers and close them
    const otherDropdowns = document.querySelectorAll('[data-controller*="dropdown"]')
    otherDropdowns.forEach(dropdown => {
      if (dropdown !== this.element) {
        const controller = this.application.getControllerForElementAndIdentifier(dropdown, "dropdown")
        if (controller && controller.isOpen) {
          controller.close()
        }
      }
    })
  }

  disconnect() {
    // Cleanup
    document.removeEventListener("click", this.handleClickOutside.bind(this))
    document.removeEventListener("keydown", this.handleEscape.bind(this))
  }
}
