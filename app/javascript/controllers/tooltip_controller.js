import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["content"]
  static values = { 
    text: String,
    position: { type: String, default: "top" },
    delay: { type: Number, default: 500 },
    accessible: { type: Boolean, default: true }
  }

  connect() {
    this.tooltip = null
    this.showTimeout = null
    this.hideTimeout = null
    
    // Add ARIA attributes for accessibility
    if (this.accessibleValue) {
      this.element.setAttribute('aria-describedby', this.tooltipId)
      this.element.setAttribute('tabindex', '0')
    }
  }

  mouseenter() {
    this.clearTimeouts()
    this.showTimeout = setTimeout(() => {
      this.show()
    }, this.delayValue)
  }

  mouseleave() {
    this.clearTimeouts()
    this.hideTimeout = setTimeout(() => {
      this.hide()
    }, 100)
  }

  focus() {
    this.clearTimeouts()
    this.show()
  }

  blur() {
    this.clearTimeouts()
    this.hide()
  }

  show() {
    if (this.tooltip) return

    this.tooltip = this.createTooltip()
    document.body.appendChild(this.tooltip)
    this.positionTooltip()
    
    // Animate in
    requestAnimationFrame(() => {
      this.tooltip.classList.add('opacity-100', 'scale-100')
      this.tooltip.classList.remove('opacity-0', 'scale-95')
    })

    // Announce to screen readers
    if (this.accessibleValue) {
      this.announceToScreenReader(this.textValue)
    }
  }

  hide() {
    if (!this.tooltip) return

    // Animate out
    this.tooltip.classList.add('opacity-0', 'scale-95')
    this.tooltip.classList.remove('opacity-100', 'scale-100')
    
    setTimeout(() => {
      if (this.tooltip && this.tooltip.parentNode) {
        this.tooltip.parentNode.removeChild(this.tooltip)
      }
      this.tooltip = null
    }, 150)
  }

  createTooltip() {
    const tooltip = document.createElement('div')
    tooltip.id = this.tooltipId
    tooltip.className = `
      absolute z-50 px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm
      opacity-0 scale-95 transform transition-all duration-150 ease-in-out
      max-w-xs break-words
    `.trim()
    
    // Add arrow
    const arrow = document.createElement('div')
    arrow.className = 'tooltip-arrow'
    tooltip.appendChild(arrow)
    
    // Add content
    const content = document.createElement('div')
    content.textContent = this.textValue
    tooltip.appendChild(content)
    
    // Add role for accessibility
    if (this.accessibleValue) {
      tooltip.setAttribute('role', 'tooltip')
      tooltip.setAttribute('aria-live', 'polite')
    }
    
    return tooltip
  }

  positionTooltip() {
    if (!this.tooltip) return

    const rect = this.element.getBoundingClientRect()
    const tooltipRect = this.tooltip.getBoundingClientRect()
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft

    let top, left

    switch (this.positionValue) {
      case 'top':
        top = rect.top + scrollTop - tooltipRect.height - 8
        left = rect.left + scrollLeft + (rect.width - tooltipRect.width) / 2
        break
      case 'bottom':
        top = rect.bottom + scrollTop + 8
        left = rect.left + scrollLeft + (rect.width - tooltipRect.width) / 2
        break
      case 'left':
        top = rect.top + scrollTop + (rect.height - tooltipRect.height) / 2
        left = rect.left + scrollLeft - tooltipRect.width - 8
        break
      case 'right':
        top = rect.top + scrollTop + (rect.height - tooltipRect.height) / 2
        left = rect.right + scrollLeft + 8
        break
      default:
        top = rect.top + scrollTop - tooltipRect.height - 8
        left = rect.left + scrollLeft + (rect.width - tooltipRect.width) / 2
    }

    // Keep tooltip within viewport
    const padding = 8
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    if (left < padding) {
      left = padding
    } else if (left + tooltipRect.width > viewportWidth - padding) {
      left = viewportWidth - tooltipRect.width - padding
    }

    if (top < padding) {
      top = padding
    } else if (top + tooltipRect.height > viewportHeight - padding) {
      top = viewportHeight - tooltipRect.height - padding
    }

    this.tooltip.style.top = `${top}px`
    this.tooltip.style.left = `${left}px`
  }

  announceToScreenReader(text) {
    // Create a live region for screen reader announcements
    const announcement = document.createElement('div')
    announcement.setAttribute('aria-live', 'polite')
    announcement.setAttribute('aria-atomic', 'true')
    announcement.className = 'sr-only'
    announcement.textContent = text
    
    document.body.appendChild(announcement)
    
    // Remove after announcement
    setTimeout(() => {
      if (announcement.parentNode) {
        announcement.parentNode.removeChild(announcement)
      }
    }, 1000)
  }

  clearTimeouts() {
    if (this.showTimeout) {
      clearTimeout(this.showTimeout)
      this.showTimeout = null
    }
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout)
      this.hideTimeout = null
    }
  }

  get tooltipId() {
    return `tooltip-${this.element.id || Math.random().toString(36).substr(2, 9)}`
  }

  disconnect() {
    this.clearTimeouts()
    this.hide()
  }
}
