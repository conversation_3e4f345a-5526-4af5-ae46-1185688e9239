import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["content"]

  connect() {
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden'
    
    // Add escape key listener
    document.addEventListener('keydown', this.handleEscape.bind(this))
    
    // Focus management for accessibility
    this.focusFirstElement()
  }

  close() {
    // Animate out
    this.element.classList.add('opacity-0')
    this.element.classList.remove('opacity-100')
    
    // Remove after animation
    setTimeout(() => {
      this.cleanup()
    }, 300)
  }

  closeOnBackdrop(event) {
    // Only close if clicking the backdrop, not the modal content
    if (event.target === event.currentTarget) {
      this.close()
    }
  }

  handleEscape(event) {
    if (event.key === 'Escape') {
      this.close()
    }
  }

  focusFirstElement() {
    // Focus the first focusable element in the modal
    const focusableElements = this.element.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    
    if (focusableElements.length > 0) {
      focusableElements[0].focus()
    }
  }

  cleanup() {
    // Restore body scroll
    document.body.style.overflow = ''
    
    // Remove escape key listener
    document.removeEventListener('keydown', this.handleEscape.bind(this))
    
    // Remove modal from DOM
    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element)
    }
  }

  disconnect() {
    this.cleanup()
  }
}
