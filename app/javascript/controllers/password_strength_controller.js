import { Controller } from "@hotwired/stimulus"

// Sophisticated Password Strength Controller
// Provides real-time password strength analysis with visual feedback
export default class extends Controller {
  static targets = ["password", "confirmation", "indicator", "bar", "label", "submitButton", "matchError"]
  
  static values = {
    minLength: { type: Number, default: 8 },
    requireUppercase: { type: Boolean, default: true },
    requireLowercase: { type: Boolean, default: true },
    requireNumbers: { type: Boolean, default: true },
    requireSymbols: { type: Boolean, default: true }
  }

  connect() {
    this.setupPasswordStrengthChecking()
    console.log("Password Strength controller connected")
  }

  setupPasswordStrengthChecking() {
    this.strengthLevels = {
      0: { label: 'Very Weak', color: 'bg-red-500', width: '20%' },
      1: { label: 'Weak', color: 'bg-red-400', width: '40%' },
      2: { label: 'Fair', color: 'bg-yellow-500', width: '60%' },
      3: { label: 'Good', color: 'bg-blue-500', width: '80%' },
      4: { label: 'Strong', color: 'bg-green-500', width: '100%' }
    }
    
    this.updateSubmitButtonState()
  }

  checkStrength(event) {
    const password = event.target.value
    const strength = this.calculatePasswordStrength(password)
    
    this.updateStrengthIndicator(strength, password)
    this.updateSubmitButtonState()
    
    // Also check confirmation match if it has a value
    if (this.hasConfirmationTarget && this.confirmationTarget.value) {
      this.checkMatch()
    }
  }

  calculatePasswordStrength(password) {
    if (!password) {
      return { score: 0, feedback: [], requirements: this.getRequirements() }
    }
    
    let score = 0
    const feedback = []
    const requirements = this.getRequirements()
    
    // Length check
    if (password.length >= this.minLengthValue) {
      score += 1
      requirements.length.met = true
    } else {
      feedback.push(`Password must be at least ${this.minLengthValue} characters`)
    }
    
    // Uppercase check
    if (this.requireUppercaseValue) {
      if (/[A-Z]/.test(password)) {
        score += 1
        requirements.uppercase.met = true
      } else {
        feedback.push('Add uppercase letters (A-Z)')
      }
    }
    
    // Lowercase check
    if (this.requireLowercaseValue) {
      if (/[a-z]/.test(password)) {
        score += 1
        requirements.lowercase.met = true
      } else {
        feedback.push('Add lowercase letters (a-z)')
      }
    }
    
    // Numbers check
    if (this.requireNumbersValue) {
      if (/[0-9]/.test(password)) {
        score += 1
        requirements.numbers.met = true
      } else {
        feedback.push('Add numbers (0-9)')
      }
    }
    
    // Symbols check
    if (this.requireSymbolsValue) {
      if (/[^A-Za-z0-9]/.test(password)) {
        score += 1
        requirements.symbols.met = true
      } else {
        feedback.push('Add symbols (!@#$%^&*)')
      }
    }
    
    // Bonus points for length
    if (password.length >= 12) score += 0.5
    if (password.length >= 16) score += 0.5
    
    // Penalty for common patterns
    if (this.hasCommonPatterns(password)) {
      score -= 1
      feedback.push('Avoid common patterns or dictionary words')
    }
    
    // Ensure score is within bounds
    score = Math.max(0, Math.min(4, Math.floor(score)))
    
    return { score, feedback, requirements }
  }

  getRequirements() {
    return {
      length: { met: false, text: `At least ${this.minLengthValue} characters` },
      uppercase: { met: false, text: 'Uppercase letter (A-Z)' },
      lowercase: { met: false, text: 'Lowercase letter (a-z)' },
      numbers: { met: false, text: 'Number (0-9)' },
      symbols: { met: false, text: 'Symbol (!@#$%^&*)' }
    }
  }

  hasCommonPatterns(password) {
    const commonPatterns = [
      /123456/,
      /password/i,
      /qwerty/i,
      /abc123/i,
      /admin/i,
      /letmein/i,
      /welcome/i,
      /monkey/i,
      /dragon/i
    ]
    
    return commonPatterns.some(pattern => pattern.test(password))
  }

  updateStrengthIndicator(strength, password) {
    if (!this.hasIndicatorTarget) return
    
    const level = this.strengthLevels[strength.score]
    
    // Update progress bar
    if (this.hasBarTarget) {
      this.barTarget.className = `h-2 rounded-full transition-all duration-300 ${level.color}`
      this.barTarget.style.width = password ? level.width : '0%'
    }
    
    // Update label
    if (this.hasLabelTarget) {
      if (password) {
        this.labelTarget.textContent = level.label
        this.labelTarget.className = `text-xs font-medium ${this.getLabelColor(strength.score)}`
      } else {
        this.labelTarget.textContent = 'Enter password'
        this.labelTarget.className = 'text-xs font-medium text-gray-500'
      }
    }
    
    // Show/hide requirements checklist
    this.updateRequirementsDisplay(strength.requirements)
  }

  updateRequirementsDisplay(requirements) {
    // Create or update requirements checklist
    let checklist = this.element.querySelector('.password-requirements')
    
    if (!checklist && this.hasIndicatorTarget) {
      checklist = document.createElement('div')
      checklist.className = 'password-requirements mt-3 space-y-1'
      this.indicatorTarget.appendChild(checklist)
    }
    
    if (checklist) {
      checklist.innerHTML = Object.entries(requirements).map(([key, req]) => `
        <div class="flex items-center text-xs">
          <svg class="w-3 h-3 mr-2 ${req.met ? 'text-green-500' : 'text-gray-400'}" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="${req.met ? 
              'M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z' : 
              'M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z'
            }" clip-rule="evenodd"/>
          </svg>
          <span class="${req.met ? 'text-green-700' : 'text-gray-600'}">${req.text}</span>
        </div>
      `).join('')
    }
  }

  getLabelColor(score) {
    const colors = {
      0: 'text-red-600',
      1: 'text-red-500',
      2: 'text-yellow-600',
      3: 'text-blue-600',
      4: 'text-green-600'
    }
    return colors[score] || 'text-gray-500'
  }

  checkMatch(event) {
    if (!this.hasPasswordTarget || !this.hasConfirmationTarget) return
    
    const password = this.passwordTarget.value
    const confirmation = this.confirmationTarget.value
    
    const isMatching = password === confirmation
    const hasConfirmationValue = confirmation.length > 0
    
    // Update confirmation field styling
    this.confirmationTarget.classList.remove(
      'border-red-300', 'border-green-300',
      'bg-red-50', 'bg-green-50',
      'focus:ring-red-500', 'focus:ring-green-500',
      'focus:border-red-500', 'focus:border-green-500'
    )
    
    if (hasConfirmationValue) {
      if (isMatching) {
        this.confirmationTarget.classList.add(
          'border-green-300', 'bg-green-50',
          'focus:ring-green-500', 'focus:border-green-500'
        )
        this.confirmationTarget.setAttribute('aria-invalid', 'false')
      } else {
        this.confirmationTarget.classList.add(
          'border-red-300', 'bg-red-50',
          'focus:ring-red-500', 'focus:border-red-500'
        )
        this.confirmationTarget.setAttribute('aria-invalid', 'true')
      }
    }
    
    // Update error message
    if (this.hasMatchErrorTarget) {
      if (hasConfirmationValue && !isMatching) {
        this.matchErrorTarget.classList.remove('hidden')
        this.matchErrorTarget.setAttribute('role', 'alert')
      } else {
        this.matchErrorTarget.classList.add('hidden')
      }
    }
    
    this.updateSubmitButtonState()
  }

  updateSubmitButtonState() {
    if (!this.hasSubmitButtonTarget) return
    
    const password = this.hasPasswordTarget ? this.passwordTarget.value : ''
    const confirmation = this.hasConfirmationTarget ? this.confirmationTarget.value : ''
    
    const strength = this.calculatePasswordStrength(password)
    const isStrongEnough = strength.score >= 2 // Minimum "Fair" strength
    const passwordsMatch = password === confirmation
    const hasRequiredFields = password && confirmation
    
    const isValid = isStrongEnough && passwordsMatch && hasRequiredFields
    
    this.submitButtonTarget.disabled = !isValid
    
    if (isValid) {
      this.submitButtonTarget.classList.remove('opacity-50', 'cursor-not-allowed')
      this.submitButtonTarget.classList.add('hover:bg-red-700')
    } else {
      this.submitButtonTarget.classList.add('opacity-50', 'cursor-not-allowed')
      this.submitButtonTarget.classList.remove('hover:bg-red-700')
    }
  }

  // Generate secure password suggestion
  generatePassword() {
    const length = Math.max(12, this.minLengthValue)
    const charset = {
      lowercase: 'abcdefghijklmnopqrstuvwxyz',
      uppercase: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      numbers: '0123456789',
      symbols: '!@#$%^&*()_+-=[]{}|;:,.<>?'
    }
    
    let password = ''
    let availableChars = ''
    
    // Ensure at least one character from each required set
    if (this.requireLowercaseValue) {
      password += this.getRandomChar(charset.lowercase)
      availableChars += charset.lowercase
    }
    
    if (this.requireUppercaseValue) {
      password += this.getRandomChar(charset.uppercase)
      availableChars += charset.uppercase
    }
    
    if (this.requireNumbersValue) {
      password += this.getRandomChar(charset.numbers)
      availableChars += charset.numbers
    }
    
    if (this.requireSymbolsValue) {
      password += this.getRandomChar(charset.symbols)
      availableChars += charset.symbols
    }
    
    // Fill remaining length with random characters
    for (let i = password.length; i < length; i++) {
      password += this.getRandomChar(availableChars)
    }
    
    // Shuffle the password
    password = password.split('').sort(() => Math.random() - 0.5).join('')
    
    return password
  }

  getRandomChar(charset) {
    return charset.charAt(Math.floor(Math.random() * charset.length))
  }

  // Public API for external use
  getPasswordStrength() {
    const password = this.hasPasswordTarget ? this.passwordTarget.value : ''
    return this.calculatePasswordStrength(password)
  }

  isPasswordValid() {
    const strength = this.getPasswordStrength()
    const password = this.hasPasswordTarget ? this.passwordTarget.value : ''
    const confirmation = this.hasConfirmationTarget ? this.confirmationTarget.value : ''
    
    return strength.score >= 2 && password === confirmation
  }
}
