/* Dashboard-specific responsive styles */

/* Mobile-first approach with enhanced touch targets */
@media (max-width: 767px) {
  /* Ensure minimum touch target size of 44px */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Improved spacing for mobile */
  .mobile-spacing {
    padding: 1rem;
  }
  
  /* Better text sizing for mobile */
  .mobile-text {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
  
  /* Optimized card layouts for mobile */
  .mobile-card {
    padding: 1rem;
    margin-bottom: 0.75rem;
  }
  
  /* Simplified navigation for mobile */
  .mobile-nav {
    padding: 0.75rem 1rem;
  }
  
  /* Better form inputs for mobile */
  .mobile-input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.75rem;
  }
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 1023px) {
  /* Optimized grid layouts for tablet */
  .tablet-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  /* Better sidebar for tablet */
  .tablet-sidebar {
    width: 16rem;
  }
}

/* Desktop enhancements */
@media (min-width: 1024px) {
  /* Enhanced hover states for desktop */
  .desktop-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  
  /* Better spacing for large screens */
  .desktop-spacing {
    padding: 2rem;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Crisp borders and shadows */
  .crisp-border {
    border-width: 0.5px;
  }
  
  .crisp-shadow {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  .dark-mode-card {
    background-color: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .dark-mode-text {
    color: #e5e7eb;
  }
  
  .dark-mode-muted {
    color: #9ca3af;
  }
}

/* Reduced motion support for accessibility */
@media (prefers-reduced-motion: reduce) {
  .respect-motion-preference {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-friendly {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  .print-page-break {
    page-break-before: always;
  }
}

/* Focus management for keyboard navigation */
.focus-visible:focus {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Better focus indicators */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

/* Improved button states */
.btn-primary {
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
  transition: all 0.2s ease-in-out;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #4338ca 0%, #2563eb 100%);
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(0);
}

/* Card hover effects */
.card-interactive {
  transition: all 0.2s ease-in-out;
}

.card-interactive:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Status indicators */
.status-indicator {
  position: relative;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-healthy::before {
  background-color: #10b981;
}

.status-warning::before {
  background-color: #f59e0b;
}

.status-error::before {
  background-color: #ef4444;
}

/* Responsive typography */
.responsive-text-xs { font-size: clamp(0.75rem, 2vw, 0.875rem); }
.responsive-text-sm { font-size: clamp(0.875rem, 2.5vw, 1rem); }
.responsive-text-base { font-size: clamp(1rem, 3vw, 1.125rem); }
.responsive-text-lg { font-size: clamp(1.125rem, 3.5vw, 1.25rem); }
.responsive-text-xl { font-size: clamp(1.25rem, 4vw, 1.5rem); }
.responsive-text-2xl { font-size: clamp(1.5rem, 5vw, 2rem); }
