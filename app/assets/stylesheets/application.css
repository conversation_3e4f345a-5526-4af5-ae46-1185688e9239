/*
 * This is a manifest file that'll be compiled into application.css.
 *
 * With <PERSON>pshaft, assets are served efficiently without preprocessing steps. You can still include
 * application-wide styles in this file, but keep in mind that CSS precedence will follow the standard
 * cascading order, meaning styles declared later in the document or manifest will override earlier ones,
 * depending on specificity.
 *
 * Consider organizing styles into separate files for maintainability.
 */

/* Sophisticated Profile Interface Styles */
/* Demonstrates advanced CSS patterns with DataReflow's design system */

/* Custom Properties for Design System */
:root {
  --primary-gradient: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
  --secondary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --error-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  --border-radius-sm: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;

  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
}

/* Analytics Dashboard Styles */
.analytics-kpi-card {
  background: linear-gradient(135deg, var(--primary-gradient));
  transition: all var(--transition-normal);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  position: relative;
}

.analytics-kpi-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
}

.analytics-kpi-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.analytics-chart-container {
  background: white;
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.analytics-chart-container:hover {
  box-shadow: var(--shadow-xl);
}

/* Real-time Activity Feed */
.activity-item {
  animation: slideInRight 0.3s ease-out;
  border-left: 3px solid transparent;
  transition: all var(--transition-fast);
  padding: 1rem;
  border-radius: var(--border-radius-md);
}

.activity-item:hover {
  background-color: #f8fafc;
  border-left-color: #4f46e5;
  transform: translateX(4px);
}

.activity-status-indicator {
  position: relative;
  display: inline-block;
}

.activity-status-indicator::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* Gauge Chart Styles */
.gauge-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gauge-score {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
}

/* Progress Bars */
.progress-bar {
  background: #e5e7eb;
  border-radius: 9999px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  border-radius: inherit;
  transition: width var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.3), 
    transparent
  );
  animation: shimmer 2s infinite;
}

/* Data Quality Indicators */
.quality-metric {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f3f4f6;
  transition: all var(--transition-fast);
}

.quality-metric:hover {
  background-color: #f8fafc;
  padding-left: 0.5rem;
  border-radius: var(--border-radius-sm);
}

.quality-score {
  font-weight: 600;
  font-size: 0.875rem;
}

.quality-score.excellent { color: #10b981; }
.quality-score.good { color: #3b82f6; }
.quality-score.fair { color: #f59e0b; }
.quality-score.poor { color: #ef4444; }

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-fade-in { animation: fadeIn 0.5s ease-out; }
.animate-pulse { animation: pulse 2s infinite; }
.animate-spin { animation: spin 1s linear infinite; }

/* Enhanced Form Styling */
.profile-form-field {
  position: relative;
  transition: all var(--transition-fast);
}

.profile-form-field input,
.profile-form-field textarea,
.profile-form-field select {
  transition: all var(--transition-fast);
  border: 2px solid transparent;
  background-color: white;
  box-shadow: var(--shadow-sm);
}

.profile-form-field input:focus,
.profile-form-field textarea:focus,
.profile-form-field select:focus {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  border-color: #4f46e5;
  outline: none;
  ring: 2px;
  ring-color: rgba(79, 70, 229, 0.2);
}

/* Floating Label Animation */
.profile-form-field.floating-label {
  position: relative;
}

.profile-form-field.floating-label label {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: white;
  padding: 0 4px;
  color: #6b7280;
  transition: all var(--transition-fast);
  pointer-events: none;
  z-index: 1;
}

.profile-form-field.floating-label input:focus + label,
.profile-form-field.floating-label input:not(:placeholder-shown) + label,
.profile-form-field.floating-label textarea:focus + label,
.profile-form-field.floating-label textarea:not(:placeholder-shown) + label {
  top: 0;
  transform: translateY(-50%) scale(0.85);
  color: #4f46e5;
  font-weight: 500;
}

/* Advanced Button Styles */
.btn-sophisticated {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-fast);
  transform-style: preserve-3d;
}

.btn-sophisticated::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-normal);
}

.btn-sophisticated:hover::before {
  left: 100%;
}

.btn-sophisticated:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-lg);
}

.btn-sophisticated:active {
  transform: translateY(0) scale(0.98);
  transition: all 100ms ease-in-out;
}

/* Gradient Backgrounds with Animation */
.gradient-bg-animated {
  background: var(--primary-gradient);
  background-size: 200% 200%;
  animation: gradientShift 4s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Card Hover Effects */
.card-sophisticated {
  transition: all var(--transition-normal);
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

.card-sophisticated:hover {
  transform: translateY(-4px) rotateX(2deg);
  box-shadow: var(--shadow-xl);
}

.card-sophisticated::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  opacity: 0;
  transition: opacity var(--transition-fast);
  pointer-events: none;
  border-radius: inherit;
}

.card-sophisticated:hover::after {
  opacity: 1;
}

/* Progress Indicators */
.progress-sophisticated {
  position: relative;
  background: #e5e7eb;
  border-radius: 9999px;
  overflow: hidden;
}

.progress-sophisticated::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.3) 0%, transparent 50%, rgba(255, 255, 255, 0.3) 100%);
  animation: shimmer 2s infinite;
  width: 30%;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(400%); }
}

/* Toggle Switch Styling */
.toggle-sophisticated {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.toggle-sophisticated input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-sophisticated .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #cbd5e1;
  transition: var(--transition-fast);
  border-radius: 24px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-sophisticated .slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 2px;
  bottom: 2px;
  background: white;
  transition: var(--transition-fast);
  border-radius: 50%;
  box-shadow: var(--shadow-sm);
}

.toggle-sophisticated input:checked + .slider {
  background: var(--primary-gradient);
}

.toggle-sophisticated input:checked + .slider:before {
  transform: translateX(24px);
  box-shadow: var(--shadow-md);
}

.toggle-sophisticated input:focus + .slider {
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

/* Notification Toast Animations */
.toast-sophisticated {
  transform: translateX(100%);
  transition: transform var(--transition-normal) cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.toast-sophisticated.show {
  transform: translateX(0);
}

.toast-sophisticated.hide {
  transform: translateX(100%);
  transition: transform var(--transition-fast) ease-in;
}

/* Loading States */
.loading-sophisticated {
  position: relative;
  overflow: hidden;
}

.loading-sophisticated::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading-sweep 1.5s infinite;
}

@keyframes loading-sweep {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Accessibility Enhancements */
.focus-sophisticated:focus {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
  border-radius: var(--border-radius-sm);
}

.focus-sophisticated:focus:not(:focus-visible) {
  outline: none;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .card-sophisticated,
  .btn-sophisticated,
  .profile-form-field input,
  .profile-form-field textarea,
  .profile-form-field select,
  .toggle-sophisticated .slider,
  .toggle-sophisticated .slider:before,
  .toast-sophisticated {
    transition: none !important;
    animation: none !important;
  }

  .card-sophisticated:hover {
    transform: none;
  }

  .btn-sophisticated:hover {
    transform: none;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .profile-form-field input,
  .profile-form-field textarea,
  .profile-form-field select {
    border: 2px solid #000;
  }

  .btn-sophisticated {
    border: 2px solid #000;
  }

  .card-sophisticated {
    border: 2px solid #000;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-gradient: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  }

  .profile-form-field input,
  .profile-form-field textarea,
  .profile-form-field select {
    background-color: #1f2937;
    color: #f9fafb;
    border-color: #374151;
  }

  .card-sophisticated {
    background-color: #1f2937;
    color: #f9fafb;
  }
}

/* Print Styles */
@media print {
  .btn-sophisticated,
  .toggle-sophisticated,
  .loading-sophisticated::after,
  .card-sophisticated::after {
    display: none !important;
  }

  .card-sophisticated {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }

  .profile-form-field input,
  .profile-form-field textarea,
  .profile-form-field select {
    border: 1px solid #000 !important;
    background: white !important;
    color: black !important;
  }
}
