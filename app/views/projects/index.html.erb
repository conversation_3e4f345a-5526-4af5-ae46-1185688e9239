<% content_for :title, "Projects - DataReflow" %>
<% content_for :page_title, "Projects" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Enhanced Header Section -->
  <div class="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-2xl p-8 mb-8 shadow-lg border border-indigo-100">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
      <div class="flex-1">
        <div class="flex items-center space-x-4 mb-4">
          <div class="h-16 w-16 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
            <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
            </svg>
          </div>
          <div>
            <h1 class="text-4xl font-bold text-gray-900 tracking-tight">
              Project Hub
            </h1>
            <p class="text-lg text-gray-700 mt-2">
              Organize and manage your data infrastructure with intelligent project workflows
            </p>
          </div>
        </div>
        
        <!-- Quick Actions Bar -->
        <div class="flex flex-wrap items-center gap-3 mt-6">
          <%= link_to new_project_path,
              class: "inline-flex items-center px-6 py-3 bg-indigo-600 text-white rounded-lg shadow-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 btn-sophisticated" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
            </svg>
            Create New Project
          <% end %>
          
          <button class="inline-flex items-center px-4 py-3 bg-white text-gray-700 border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"/>
            </svg>
            Filter Projects
          </button>
          
          <button class="inline-flex items-center px-4 py-3 bg-white text-gray-700 border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            Export Data
          </button>
        </div>
      </div>
      
      <!-- Real-time Status Indicator -->
      <div class="mt-6 lg:mt-0 lg:ml-8">
        <div class="bg-white rounded-xl p-4 shadow-md border border-gray-200">
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span class="text-sm font-medium text-gray-700">System Status: Healthy</span>
          </div>
          <div class="text-xs text-gray-500 mt-1">
            Last sync: <%= Time.current.strftime("%H:%M") %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Stats Dashboard -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Projects -->
    <div class="bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-lg p-6 text-white transform hover:scale-105 transition-all duration-200">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-indigo-100 text-sm font-medium">Total Projects</p>
          <p class="text-3xl font-bold mt-2"><%= @project_stats[:total_projects] %></p>
          <div class="flex items-center mt-2">
            <svg class="h-4 w-4 text-indigo-200 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="text-indigo-200 text-sm">Across all environments</span>
          </div>
        </div>
        <div class="bg-white bg-opacity-20 rounded-lg p-3">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- Active Projects -->
    <div class="bg-gradient-to-br from-green-500 to-teal-600 rounded-xl shadow-lg p-6 text-white transform hover:scale-105 transition-all duration-200">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-green-100 text-sm font-medium">Active Projects</p>
          <p class="text-3xl font-bold mt-2"><%= @project_stats[:active_projects] %></p>
          <div class="flex items-center mt-2">
            <svg class="h-4 w-4 text-green-200 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
            <span class="text-green-200 text-sm">Currently operational</span>
          </div>
        </div>
        <div class="bg-white bg-opacity-20 rounded-lg p-3">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- Total Connectors -->
    <div class="bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl shadow-lg p-6 text-white transform hover:scale-105 transition-all duration-200">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-blue-100 text-sm font-medium">Total Connectors</p>
          <p class="text-3xl font-bold mt-2"><%= @project_stats[:total_connectors] %></p>
          <div class="flex items-center mt-2">
            <svg class="h-4 w-4 text-blue-200 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"/>
            </svg>
            <span class="text-blue-200 text-sm">Data connections</span>
          </div>
        </div>
        <div class="bg-white bg-opacity-20 rounded-lg p-3">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- Active Connectors -->
    <div class="bg-gradient-to-br from-orange-500 to-red-600 rounded-xl shadow-lg p-6 text-white transform hover:scale-105 transition-all duration-200">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-orange-100 text-sm font-medium">Active Connectors</p>
          <p class="text-3xl font-bold mt-2"><%= @project_stats[:active_connectors] %></p>
          <div class="flex items-center mt-2">
            <svg class="h-4 w-4 text-orange-200 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
            </svg>
            <span class="text-orange-200 text-sm">Live data streams</span>
          </div>
        </div>
        <div class="bg-white bg-opacity-20 rounded-lg p-3">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
          </svg>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Projects Grid -->
  <div class="mb-8">
    <% if @projects.any? %>
      <!-- Projects Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <% @projects.each do |project| %>
          <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 card-sophisticated">
            <!-- Project Header -->
            <div class="relative">
              <div class="bg-gradient-to-r from-indigo-500 to-purple-600 h-32 flex items-center justify-center">
                <div class="text-center">
                  <div class="inline-flex items-center justify-center w-16 h-16 bg-white bg-opacity-20 rounded-full mb-2">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                    </svg>
                  </div>
                </div>
              </div>
              <!-- Status Badge -->
              <div class="absolute top-4 right-4">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <%= project.active? ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %> shadow-sm">
                  <% if project.active? %>
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                  <% end %>
                  <%= project.status.humanize %>
                </span>
              </div>
            </div>
            
            <!-- Project Content -->
            <div class="p-6">
              <div class="mb-4">
                <h3 class="text-xl font-bold text-gray-900 mb-2">
                  <%= project.name %>
                </h3>
                <% if project.description.present? %>
                  <p class="text-gray-600 text-sm mb-3">
                    <%= truncate(project.description, length: 100) %>
                  </p>
                <% end %>
              </div>
              
              <!-- Project Stats -->
              <div class="grid grid-cols-2 gap-4 mb-6">
                <div class="bg-gray-50 rounded-lg p-3 text-center">
                  <div class="text-2xl font-bold text-indigo-600">
                    <%= project.connector_count || 0 %>
                  </div>
                  <div class="text-xs text-gray-500">
                    <%= 'Connector'.pluralize(project.connector_count || 0) %>
                  </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-3 text-center">
                  <div class="text-2xl font-bold text-green-600">
                    <%= project.active_connector_count || 0 %>
                  </div>
                  <div class="text-xs text-gray-500">Active</div>
                </div>
              </div>
              
              <!-- Project Meta -->
              <div class="border-t border-gray-200 pt-4 mb-4">
                <div class="flex items-center justify-between text-xs text-gray-500 mb-2">
                  <span>Created <%= time_ago_in_words(project.created_at) %> ago</span>
                  <span>By <%= project.created_by.first_name %></span>
                </div>
                <div class="text-xs text-gray-500">
                  Updated <%= time_ago_in_words(project.updated_at) %> ago
                </div>
              </div>
              
              <!-- Action Buttons -->
              <div class="flex space-x-2">
                <%= link_to project_path(project),
                    class: "flex-1 inline-flex justify-center items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 btn-sophisticated" do %>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                  </svg>
                  View
                <% end %>
                
                <div class="relative">
                  <button class="inline-flex items-center p-2 border border-gray-300 rounded-lg text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"/>
                    </svg>
                  </button>
                  <!-- Dropdown would go here -->
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <!-- Enhanced Empty State -->
      <div class="text-center py-16 bg-white rounded-2xl shadow-lg border border-gray-200">
        <div class="relative">
          <!-- Background Pattern -->
          <div class="absolute inset-0 bg-gradient-to-r from-indigo-50 to-blue-50 rounded-2xl opacity-50"></div>
          
          <div class="relative z-10">
            <!-- Large Icon -->
            <div class="mx-auto h-24 w-24 bg-gradient-to-br from-indigo-100 to-blue-200 rounded-full flex items-center justify-center mb-8">
              <svg class="h-12 w-12 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
              </svg>
            </div>
            
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Ready to Start Your First Project?</h3>
            <p class="text-lg text-gray-600 mb-8 max-w-md mx-auto">
              Projects help you organize your data connectors, pipelines, and workflows. Create your first project to get started with DataReflow.
            </p>
            
            <!-- Enhanced CTA -->
            <div class="space-y-4">
              <%= link_to new_project_path,
                  class: "inline-flex items-center px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white text-lg font-semibold rounded-xl shadow-lg hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 transform hover:scale-105 btn-sophisticated" do %>
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                </svg>
                Create Your First Project
              <% end %>
              
              <div class="text-sm text-gray-500">
                <span>Or</span>
                <a href="#" class="text-indigo-600 hover:text-indigo-700 font-medium ml-2">
                  explore sample projects
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
