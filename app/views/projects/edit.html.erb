<% content_for :title, "Edit #{@project.name} - DataReflow" %>

<div class="space-y-6">
  <!-- Header Section -->
  <div class="md:flex md:items-center md:justify-between">
    <div class="min-w-0 flex-1">
      <!-- Breadcrumb -->
      <nav class="flex mb-4" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <%= link_to projects_path, class: "inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600" do %>
              <svg class="mr-2 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L9 5.414V17a1 1 0 102 0V5.414l5.293 5.293a1 1 0 001.414-1.414l-7-7z"/>
              </svg>
              Projects
            <% end %>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <%= link_to @project, class: "ml-1 text-sm font-medium text-gray-700 hover:text-indigo-600 md:ml-2" do %>
                <%= @project.name %>
              <% end %>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Edit</span>
            </div>
          </li>
        </ol>
      </nav>

      <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
        Edit Project
      </h1>
      <p class="mt-1 text-sm text-gray-500">
        Update your project details and settings
      </p>
    </div>
  </div>

  <!-- Form Section -->
  <div class="bg-white shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <%= form_with model: @project, local: true, class: "space-y-6" do |form| %>
        <% if @project.errors.any? %>
          <div class="rounded-md bg-red-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  There were <%= pluralize(@project.errors.count, "error") %> with your submission:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc space-y-1 pl-5">
                    <% @project.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Project Name -->
        <div>
          <%= form.label :name, class: "block text-sm font-medium leading-6 text-gray-900" do %>
            Project Name
            <span class="text-red-500">*</span>
          <% end %>
          <div class="mt-2">
            <%= form.text_field :name,
                class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                required: true %>
          </div>
        </div>

        <!-- Project Description -->
        <div>
          <%= form.label :description, class: "block text-sm font-medium leading-6 text-gray-900" do %>
            Description
          <% end %>
          <div class="mt-2">
            <%= form.text_area :description,
                rows: 4,
                class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" %>
          </div>
        </div>

        <!-- Project Status -->
        <div>
          <%= form.label :status, class: "block text-sm font-medium leading-6 text-gray-900" do %>
            Status
          <% end %>
          <div class="mt-2">
            <%= form.select :status,
                options_for_select([
                  ['Active', 'active'],
                  ['Paused', 'paused'],
                  ['Archived', 'archived']
                ], @project.status),
                {},
                class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" %>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
          <div>
            <% if @project.can_be_deleted? %>
              <%= link_to @project,
                  method: :delete,
                  data: { 
                    confirm: "Are you sure you want to delete this project? This action cannot be undone." 
                  },
                  class: "rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600" do %>
                Delete Project
              <% end %>
            <% else %>
              <p class="text-sm text-gray-500">
                Cannot delete project with existing data connectors or pipelines
              </p>
            <% end %>
          </div>
          
          <div class="flex items-center space-x-3">
            <%= link_to @project,
                class: "rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" do %>
              Cancel
            <% end %>
            <%= form.submit "Update Project",
                class: "rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Project Info -->
  <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium text-gray-800">
          Project Information
        </h3>
        <div class="mt-2 text-sm text-gray-600">
          <ul class="space-y-1">
            <li><strong>Created:</strong> <%= @project.created_at.strftime("%B %d, %Y at %I:%M %p") %></li>
            <li><strong>Created by:</strong> <%= @project.created_by.full_name %></li>
            <li><strong>Data Connectors:</strong> <%= pluralize(@project.connector_count, 'connector') %></li>
            <li><strong>Last Updated:</strong> <%= @project.updated_at.strftime("%B %d, %Y at %I:%M %p") %></li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
