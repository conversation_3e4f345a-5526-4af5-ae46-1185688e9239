<% content_for :title, "#{@project.name} - DataReflow" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Enhanced Breadcrumb Navigation -->
  <nav class="flex mb-6" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-3">
      <li class="inline-flex items-center">
        <%= link_to projects_path, class: "inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200" do %>
          <svg class="mr-2 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L9 5.414V17a1 1 0 102 0V5.414l5.293 5.293a1 1 0 001.414-1.414l-7-7z"/>
          </svg>
          Project Hub
        <% end %>
      </li>
      <li>
        <div class="flex items-center">
          <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
          </svg>
          <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2"><%= @project.name %></span>
        </div>
      </li>
    </ol>
  </nav>

  <!-- Enhanced Project Header -->
  <div class="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-2xl p-8 mb-8 shadow-lg border border-indigo-100 card-sophisticated">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
      <div class="flex items-start space-x-6">
        <!-- Project Icon -->
        <div class="h-20 w-20 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-xl flex-shrink-0">
          <svg class="h-10 w-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
          </svg>
        </div>

        <div class="flex-1">
          <div class="flex items-center space-x-4 mb-3">
            <h1 class="text-4xl font-bold text-gray-900 tracking-tight">
              <%= @project.name %>
            </h1>
            
            <!-- Status Badge -->
            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold shadow-sm <%= @project.active? ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-gray-100 text-gray-800 border border-gray-200' %>">
              <% if @project.active? %>
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
              <% end %>
              <%= @project.status.humanize %>
            </span>
          </div>
          
          <% if @project.description.present? %>
            <p class="text-lg text-gray-700 mb-4 max-w-3xl">
              <%= @project.description %>
            </p>
          <% end %>

          <!-- Project Meta Information -->
          <div class="flex flex-wrap items-center gap-6 text-sm text-gray-600">
            <div class="flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a2 2 0 012 2v1a2 2 0 01-2 2h-1v2a2 2 0 01-2 2H9a2 2 0 01-2-2v-2H6a2 2 0 01-2-2V9a2 2 0 012-2h2z"/>
              </svg>
              Created <%= time_ago_in_words(@project.created_at) %> ago
            </div>
            
            <div class="flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              Updated <%= time_ago_in_words(@project.updated_at) %> ago
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="mt-6 lg:mt-0 flex flex-col sm:flex-row gap-3 lg:ml-8">
        <%= link_to new_project_data_connector_path(@project),
            class: "inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white text-sm font-semibold rounded-lg shadow-lg hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 transform hover:scale-105 btn-sophisticated" do %>
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
          </svg>
          Add Connector
        <% end %>
        
        <%= link_to edit_project_path(@project),
            class: "inline-flex items-center justify-center px-6 py-3 bg-white text-gray-700 border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 btn-sophisticated" do %>
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
          </svg>
          Edit Project
        <% end %>
      </div>
    </div>
  </div>

  <!-- Enhanced Project Health Dashboard -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Connectors -->
    <div class="bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-lg p-6 text-white transform hover:scale-105 transition-all duration-200 card-sophisticated">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-indigo-100 text-sm font-medium">Total Connectors</p>
          <p class="text-3xl font-bold mt-2"><%= @health_summary[:total_connectors] %></p>
          <div class="flex items-center mt-2">
            <svg class="h-4 w-4 text-indigo-200 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"/>
            </svg>
            <span class="text-indigo-200 text-sm">Data connections</span>
          </div>
        </div>
        <div class="bg-white bg-opacity-20 rounded-lg p-3">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- Active Connectors -->
    <div class="bg-gradient-to-br from-green-500 to-teal-600 rounded-xl shadow-lg p-6 text-white transform hover:scale-105 transition-all duration-200 card-sophisticated">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-green-100 text-sm font-medium">Active Connectors</p>
          <p class="text-3xl font-bold mt-2"><%= @health_summary[:active_connectors] %></p>
          <div class="flex items-center mt-2">
            <svg class="h-4 w-4 text-green-200 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
            <span class="text-green-200 text-sm">Currently operational</span>
          </div>
        </div>
        <div class="bg-white bg-opacity-20 rounded-lg p-3">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- Healthy Connectors -->
    <div class="bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl shadow-lg p-6 text-white transform hover:scale-105 transition-all duration-200 card-sophisticated">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-emerald-100 text-sm font-medium">Healthy Connectors</p>
          <p class="text-3xl font-bold mt-2"><%= @health_summary[:healthy_connectors] %></p>
          <div class="flex items-center mt-2">
            <svg class="h-4 w-4 text-emerald-200 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="text-emerald-200 text-sm">Performing well</span>
          </div>
        </div>
        <div class="bg-white bg-opacity-20 rounded-lg p-3">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- Needs Attention -->
    <div class="bg-gradient-to-br from-orange-500 to-red-600 rounded-xl shadow-lg p-6 text-white transform hover:scale-105 transition-all duration-200 card-sophisticated">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-orange-100 text-sm font-medium">Needs Attention</p>
          <p class="text-3xl font-bold mt-2"><%= @health_summary[:needs_attention] %></p>
          <div class="flex items-center mt-2">
            <svg class="h-4 w-4 text-orange-200 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
            <span class="text-orange-200 text-sm">Requires review</span>
          </div>
        </div>
        <div class="bg-white bg-opacity-20 rounded-lg p-3">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
          </svg>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Data Connectors Section -->
  <div class="mb-8">
    <!-- Section Header -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 mb-6 shadow-sm border border-blue-100">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="h-12 w-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
            <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
            </svg>
          </div>
          <div>
            <h2 class="text-2xl font-bold text-gray-900">Data Connectors</h2>
            <p class="text-gray-600 mt-1">Manage and monitor your project's data connections</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-3">
          <%= link_to project_data_connectors_path(@project),
              class: "inline-flex items-center px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 btn-sophisticated" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
            View All
          <% end %>
        </div>
      </div>
    </div>

    <% if @data_connectors.any? %>
      <!-- Connectors Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <% @data_connectors.each do |connector| %>
          <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 card-sophisticated">
            <!-- Connector Header -->
            <div class="relative">
              <div class="bg-gradient-to-r from-gray-50 to-blue-50 h-20 flex items-center justify-center">
                <div class="inline-flex items-center justify-center w-12 h-12 bg-white bg-opacity-90 rounded-lg shadow-sm">
                  <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.79 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.79 4 8 4s8-1.79 8-4M4 7c0-2.21 3.79-4 8-4s8 1.79 8 4"/>
                  </svg>
                </div>
              </div>
              <!-- Status Badge -->
              <div class="absolute top-3 right-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold shadow-sm <%= connector.active? ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-gray-100 text-gray-800 border border-gray-200' %>">
                  <% if connector.active? %>
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                  <% end %>
                  <%= connector.status.humanize %>
                </span>
              </div>
            </div>
            
            <!-- Connector Content -->
            <div class="p-6">
              <div class="mb-4">
                <h3 class="text-lg font-bold text-gray-900 mb-2">
                  <%= connector.name %>
                </h3>
                <div class="flex items-center space-x-2 mb-3">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                    <%= connector.connector_type.humanize %>
                  </span>
                  
                  <% if connector.connection_healthy? %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                      </svg>
                      Healthy
                    </span>
                  <% elsif connector.needs_testing? %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                      </svg>
                      Needs Testing
                    </span>
                  <% end %>
                </div>
              </div>
              
              <!-- Connector Meta -->
              <div class="border-t border-gray-200 pt-4 mb-4">
                <div class="text-xs text-gray-500 space-y-1">
                  <div>Updated <%= time_ago_in_words(connector.updated_at) %> ago</div>
                  <% if connector.last_tested_at.present? %>
                    <div>Last tested <%= time_ago_in_words(connector.last_tested_at) %> ago</div>
                  <% end %>
                </div>
              </div>
              
              <!-- Action Buttons -->
              <div class="flex space-x-2">
                <%= link_to project_data_connector_path(@project, connector),
                    class: "flex-1 inline-flex justify-center items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 btn-sophisticated" do %>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                  </svg>
                  View
                <% end %>
                
                <div class="relative">
                  <button class="inline-flex items-center p-2 border border-gray-300 rounded-lg text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"/>
                    </svg>
                  </button>
                  <!-- Dropdown would go here -->
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <!-- Enhanced Empty State -->
      <div class="text-center py-16 bg-white rounded-2xl shadow-lg border border-gray-200">
        <div class="relative">
          <!-- Background Pattern -->
          <div class="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl opacity-50"></div>
          
          <div class="relative z-10">
            <!-- Large Icon -->
            <div class="mx-auto h-20 w-20 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-full flex items-center justify-center mb-6">
              <svg class="h-10 w-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
              </svg>
            </div>
            
            <h3 class="text-xl font-bold text-gray-900 mb-3">Ready to Connect Your Data?</h3>
            <p class="text-lg text-gray-600 mb-8 max-w-md mx-auto">
              Start building your data pipeline by adding your first connector to this project.
            </p>
            
            <!-- Enhanced CTA -->
            <div class="space-y-4">
              <%= link_to new_project_data_connector_path(@project),
                  class: "inline-flex items-center px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white text-lg font-semibold rounded-xl shadow-lg hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 transform hover:scale-105 btn-sophisticated" do %>
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                </svg>
                Add Your First Connector
              <% end %>
              
              <div class="text-sm text-gray-500">
                <span>Or</span>
                <a href="#" class="text-indigo-600 hover:text-indigo-700 font-medium ml-2">
                  browse connector templates
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
</div>
