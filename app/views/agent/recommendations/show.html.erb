<div class="container mx-auto px-4 py-8">
  <!-- Breadcrumb -->
  <nav class="flex mb-6" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-1">
      <li>
        <%= link_to agent_recommendations_path, class: "text-gray-500 hover:text-gray-700" do %>
          AI Recommendations
        <% end %>
      </li>
      <li>
        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
        </svg>
      </li>
      <li class="text-gray-900"><%= truncate(@recommendation.title, length: 50) %></li>
    </ol>
  </nav>

  <!-- Header -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="p-6">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <div class="flex items-center mb-4">
            <h1 class="text-2xl font-bold text-gray-900 mr-4"><%= @recommendation.title %></h1>
            
            <!-- Status Badge -->
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
              <%= case @recommendation.status
                  when 'pending' then 'bg-yellow-100 text-yellow-800'
                  when 'accepted' then 'bg-blue-100 text-blue-800'  
                  when 'implemented' then 'bg-green-100 text-green-800'
                  when 'rejected' then 'bg-red-100 text-red-800'
                  else 'bg-gray-100 text-gray-800'
                  end %>">
              <%= @recommendation.status.humanize %>
            </span>
            
            <!-- Type Badge -->
            <span class="ml-2 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
              <%= @recommendation.recommendation_type.humanize %>
            </span>
          </div>
          
          <p class="text-gray-600 text-lg mb-4"><%= @recommendation.description %></p>
          
          <!-- Metrics -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="flex items-center">
                <div class="p-2 rounded-full bg-blue-100">
                  <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-gray-600">Confidence Score</p>
                  <p class="text-lg font-semibold text-gray-900"><%= @recommendation.confidence_score %>%</p>
                </div>
              </div>
            </div>
            
            <% if @recommendation.estimated_value.present? %>
              <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex items-center">
                  <div class="p-2 rounded-full bg-green-100">
                    <svg class="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm text-gray-600">Estimated Value</p>
                    <p class="text-lg font-semibold text-gray-900">$<%= number_with_precision(@recommendation.estimated_value, precision: 2) %></p>
                  </div>
                </div>
              </div>
            <% end %>
            
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="flex items-center">
                <div class="p-2 rounded-full bg-purple-100">
                  <svg class="h-5 w-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-gray-600">Created</p>
                  <p class="text-lg font-semibold text-gray-900"><%= time_ago_in_words(@recommendation.created_at) %> ago</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex flex-col space-y-3 ml-6">
          <% if @recommendation.pending? %>
            <%= link_to "Accept Recommendation", accept_agent_recommendation_path(@recommendation), 
                method: :patch, 
                class: "btn btn-success", 
                data: { 
                  turbo_method: :patch,
                  confirm: "Are you sure you want to accept this recommendation?" 
                } %>
            <%= link_to "Reject", reject_agent_recommendation_path(@recommendation), 
                method: :patch, 
                class: "btn btn-outline", 
                data: { 
                  turbo_method: :patch,
                  confirm: "Are you sure you want to reject this recommendation?" 
                } %>
          <% elsif @recommendation.accepted? %>
            <%= link_to "Mark as Implemented", implement_agent_recommendation_path(@recommendation), 
                method: :post, 
                class: "btn btn-primary", 
                data: { 
                  turbo_method: :post,
                  confirm: "Have you successfully implemented this recommendation?" 
                } %>
          <% elsif @recommendation.implemented? %>
            <div class="flex items-center text-green-600">
              <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              Successfully Implemented
            </div>
            <% if @recommendation.implemented_at %>
              <p class="text-sm text-gray-600">
                on <%= @recommendation.implemented_at.strftime("%B %d, %Y") %>
              </p>
            <% end %>
          <% elsif @recommendation.rejected? %>
            <div class="flex items-center text-red-600">
              <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
              Rejected
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Content Sections -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-8">
      <!-- Implementation Steps -->
      <% if @implementation_steps.present? %>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Implementation Steps</h2>
            <div class="space-y-4">
              <% @implementation_steps.each_with_index do |step, index| %>
                <div class="flex">
                  <div class="flex-shrink-0">
                    <div class="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                      <span class="text-sm font-medium text-blue-600"><%= index + 1 %></span>
                    </div>
                  </div>
                  <div class="ml-4">
                    <h3 class="text-sm font-medium text-gray-900"><%= step['title'] || "Step #{index + 1}" %></h3>
                    <p class="text-sm text-gray-600 mt-1"><%= step['description'] %></p>
                    <% if step['estimated_time'] %>
                      <p class="text-xs text-gray-500 mt-1">Estimated time: <%= step['estimated_time'] %></p>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>

      <!-- AI Analysis -->
      <% if @recommendation.ai_analysis.present? %>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">AI Analysis</h2>
            <div class="prose max-w-none">
              <% @recommendation.ai_analysis.each do |key, value| %>
                <% unless key.in?(['raw_data', 'metadata', 'rejection_reason', 'rejected_at']) %>
                  <div class="mb-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-2"><%= key.humanize %></h4>
                    <% if value.is_a?(Array) %>
                      <ul class="list-disc list-inside text-sm text-gray-600 space-y-1">
                        <% value.each do |item| %>
                          <li><%= item %></li>
                        <% end %>
                      </ul>
                    <% elsif value.is_a?(Hash) %>
                      <dl class="text-sm">
                        <% value.each do |sub_key, sub_value| %>
                          <dt class="font-medium text-gray-900"><%= sub_key.humanize %></dt>
                          <dd class="text-gray-600 mb-2"><%= sub_value %></dd>
                        <% end %>
                      </dl>
                    <% else %>
                      <p class="text-sm text-gray-600"><%= value %></p>
                    <% end %>
                  </div>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Related Pipeline -->
      <% if @pipeline %>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Related Pipeline</h2>
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-medium text-gray-900"><%= @pipeline.name %></h3>
                <p class="text-gray-600"><%= @pipeline.description if @pipeline.description.present? %></p>
                <div class="flex items-center mt-2 text-sm text-gray-500">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                    <%= @pipeline.active? ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %>">
                    <%= @pipeline.status&.humanize || 'Unknown' %>
                  </span>
                  <span class="ml-4">Last run: <%= @pipeline.updated_at ? time_ago_in_words(@pipeline.updated_at) + ' ago' : 'Never' %></span>
                </div>
              </div>
              <div>
                <%= link_to "View Pipeline", pipeline_path(@pipeline), class: "btn btn-outline" %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
      <!-- Similar Recommendations -->
      <% if @similar_recommendations.any? %>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Similar Recommendations</h3>
            <div class="space-y-4">
              <% @similar_recommendations.each do |similar| %>
                <div class="border-l-4 border-blue-200 pl-4">
                  <%= link_to agent_recommendation_path(similar), class: "block hover:bg-gray-50 -ml-4 -mr-6 px-4 py-2" do %>
                    <h4 class="text-sm font-medium text-gray-900"><%= truncate(similar.title, length: 60) %></h4>
                    <p class="text-xs text-gray-600 mt-1">
                      <%= similar.recommendation_type.humanize %> • 
                      <%= similar.confidence_score %>% confidence •
                      <%= similar.status.humanize %>
                    </p>
                  <% end %>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Quick Actions -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div class="space-y-3">
            <%= link_to agent_recommendations_path, class: "flex items-center text-sm text-gray-700 hover:text-blue-600" do %>
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
              Back to Recommendations
            <% end %>
            
            <%= link_to analytics_agent_recommendations_path, class: "flex items-center text-sm text-gray-700 hover:text-blue-600" do %>
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
              View Analytics
            <% end %>
            
            <% if @pipeline %>
              <%= link_to pipeline_path(@pipeline), class: "flex items-center text-sm text-gray-700 hover:text-blue-600" do %>
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                </svg>
                View Related Pipeline
              <% end %>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Revenue Impact -->
      <% if @recommendation.revenue_generated_cents && @recommendation.revenue_generated_cents > 0 %>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Revenue Impact</h3>
            <div class="text-center">
              <p class="text-3xl font-bold text-green-600">
                $<%= number_with_precision(@recommendation.revenue_generated_cents / 100.0, precision: 2) %>
              </p>
              <p class="text-sm text-gray-600 mt-1">Generated revenue</p>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>