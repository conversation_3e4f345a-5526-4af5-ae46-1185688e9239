<div class="container mx-auto px-4 py-8">
  <!-- Header Section -->
  <div class="flex justify-between items-center mb-8">
    <div>
      <h1 class="text-3xl font-bold text-gray-900">AI Agent Recommendations</h1>
      <p class="text-gray-600 mt-2">Intelligent suggestions to optimize your data pipelines and increase revenue</p>
    </div>
    <div class="flex space-x-4">
      <%= link_to "Generate New", generate_agent_recommendations_path, 
          method: :post, 
          class: "btn btn-primary", 
          data: { turbo_method: :post } %>
      <%= link_to "Analytics", analytics_agent_recommendations_path, 
          class: "btn btn-outline" %>
    </div>
  </div>

  <!-- Revenue Summary Cards -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-green-100">
          <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
          </svg>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">This Month Revenue</p>
          <p class="text-2xl font-semibold text-gray-900">$<%= number_with_precision(@revenue_summary[:this_month], precision: 2) %></p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-blue-100">
          <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
          </svg>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Adoption Rate</p>
          <p class="text-2xl font-semibold text-gray-900"><%= number_with_precision(@adoption_rate, precision: 1) %>%</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-purple-100">
          <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
          </svg>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Total Value Generated</p>
          <p class="text-2xl font-semibold text-gray-900">$<%= number_with_precision(@total_value_generated, precision: 2) %></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
    <%= form_with url: agent_recommendations_path, method: :get, local: true, 
        class: "flex flex-wrap gap-4 items-end" do |form| %>
      <div>
        <%= form.label :status, "Filter by Status", class: "block text-sm font-medium text-gray-700 mb-1" %>
        <%= form.select :status, 
            options_for_select([
              ['All', ''], 
              ['Pending', 'pending'], 
              ['Accepted', 'accepted'], 
              ['Implemented', 'implemented'], 
              ['Rejected', 'rejected']
            ], params[:status]), 
            {}, 
            { class: "form-select rounded-md border-gray-300" } %>
      </div>
      
      <div>
        <%= form.label :type, "Filter by Type", class: "block text-sm font-medium text-gray-700 mb-1" %>
        <%= form.select :type, 
            options_for_select([
              ['All', ''], 
              ['Optimization', 'optimization'], 
              ['Monetization', 'monetization'], 
              ['Quality', 'quality'],
              ['Template', 'template']
            ], params[:type]), 
            {}, 
            { class: "form-select rounded-md border-gray-300" } %>
      </div>
      
      <div>
        <%= form.submit "Apply Filters", class: "btn btn-primary" %>
        <%= link_to "Clear", agent_recommendations_path, class: "btn btn-outline" %>
      </div>
    <% end %>
  </div>

  <!-- Recommendations List -->
  <div class="space-y-6">
    <% if @recommendations.any? %>
      <% @recommendations.each do |recommendation| %>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div class="p-6">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center mb-2">
                  <h3 class="text-lg font-semibold text-gray-900 mr-3">
                    <%= link_to recommendation.title, agent_recommendation_path(recommendation), 
                        class: "hover:text-blue-600" %>
                  </h3>
                  
                  <!-- Status Badge -->
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                    <%= case recommendation.status
                        when 'pending' then 'bg-yellow-100 text-yellow-800'
                        when 'accepted' then 'bg-blue-100 text-blue-800'  
                        when 'implemented' then 'bg-green-100 text-green-800'
                        when 'rejected' then 'bg-red-100 text-red-800'
                        else 'bg-gray-100 text-gray-800'
                        end %>">
                    <%= recommendation.status.humanize %>
                  </span>
                  
                  <!-- Type Badge -->
                  <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    <%= recommendation.recommendation_type.humanize %>
                  </span>
                </div>
                
                <p class="text-gray-600 mb-3"><%= truncate(recommendation.description, length: 200) %></p>
                
                <div class="flex items-center text-sm text-gray-500 space-x-4">
                  <div class="flex items-center">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Confidence: <%= recommendation.confidence_score %>%
                  </div>
                  
                  <% if recommendation.estimated_value.present? %>
                    <div class="flex items-center">
                      <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                      </svg>
                      Est. Value: $<%= number_with_precision(recommendation.estimated_value, precision: 2) %>
                    </div>
                  <% end %>
                  
                  <div class="flex items-center">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <%= time_ago_in_words(recommendation.created_at) %> ago
                  </div>
                </div>
              </div>
              
              <!-- Actions -->
              <div class="flex flex-col space-y-2 ml-6">
                <% if recommendation.pending? %>
                  <%= link_to "Accept", accept_agent_recommendation_path(recommendation), 
                      method: :patch, 
                      class: "btn btn-sm btn-success", 
                      data: { 
                        turbo_method: :patch,
                        confirm: "Are you sure you want to accept this recommendation?" 
                      } %>
                  <%= link_to "Reject", reject_agent_recommendation_path(recommendation), 
                      method: :patch, 
                      class: "btn btn-sm btn-outline", 
                      data: { 
                        turbo_method: :patch,
                        confirm: "Are you sure you want to reject this recommendation?" 
                      } %>
                <% elsif recommendation.accepted? %>
                  <%= link_to "Mark Implemented", implement_agent_recommendation_path(recommendation), 
                      method: :post, 
                      class: "btn btn-sm btn-primary", 
                      data: { 
                        turbo_method: :post,
                        confirm: "Have you implemented this recommendation?" 
                      } %>
                <% elsif recommendation.implemented? %>
                  <div class="flex items-center text-green-600 text-sm">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Implemented
                  </div>
                  <% if recommendation.revenue_generated_cents && recommendation.revenue_generated_cents > 0 %>
                    <div class="text-sm text-gray-600">
                      Revenue: $<%= number_with_precision(recommendation.revenue_generated_cents / 100.0, precision: 2) %>
                    </div>
                  <% end %>
                <% end %>
                
                <%= link_to "View Details", agent_recommendation_path(recommendation), 
                    class: "btn btn-sm btn-outline" %>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Pagination -->
      <div class="flex justify-center mt-8">
        <%= paginate @recommendations if respond_to?(:paginate) %>
      </div>
    <% else %>
      <!-- Empty State -->
      <div class="text-center py-12">
        <div class="mx-auto h-12 w-12 text-gray-400">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="h-12 w-12">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
          </svg>
        </div>
        <h3 class="mt-4 text-lg font-semibold text-gray-900">No Recommendations Yet</h3>
        <p class="mt-2 text-gray-600 max-w-md mx-auto">
          Our AI agent will analyze your data pipelines and generate personalized recommendations to help optimize performance and increase revenue.
        </p>
        <div class="mt-6">
          <%= link_to "Generate Recommendations", generate_agent_recommendations_path, 
              method: :post, 
              class: "btn btn-primary", 
              data: { turbo_method: :post } %>
        </div>
      </div>
    <% end %>
  </div>
</div>