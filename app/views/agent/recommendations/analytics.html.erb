<div class="container mx-auto px-4 py-8">
  <!-- Header -->
  <div class="flex justify-between items-center mb-8">
    <div>
      <h1 class="text-3xl font-bold text-gray-900">Recommendation Analytics</h1>
      <p class="text-gray-600 mt-2">Performance insights and metrics for your AI recommendations</p>
    </div>
    <div class="flex space-x-4">
      <%= form_with url: analytics_agent_recommendations_path, method: :get, local: true, class: "flex items-center space-x-2" do |form| %>
        <%= form.select :period, 
            options_for_select([
              ['Last 7 days', 7], 
              ['Last 30 days', 30], 
              ['Last 90 days', 90], 
              ['Last 6 months', 180]
            ], params[:period] || 30), 
            {}, 
            { class: "form-select rounded-md border-gray-300", onchange: "this.form.submit()" } %>
      <% end %>
      <%= link_to "Back to Recommendations", agent_recommendations_path, class: "btn btn-outline" %>
    </div>
  </div>

  <!-- Key Metrics Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-blue-100">
          <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
          </svg>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Acceptance Rate</p>
          <p class="text-2xl font-semibold text-gray-900"><%= @analytics_data[:acceptance_rate] %>%</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-green-100">
          <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Implementation Rate</p>
          <p class="text-2xl font-semibold text-gray-900"><%= @analytics_data[:implementation_rate] %>%</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-purple-100">
          <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
          </svg>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Value Generated</p>
          <p class="text-2xl font-semibold text-gray-900">$<%= number_with_precision(@analytics_data[:value_generated], precision: 2) %></p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-yellow-100">
          <svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
          </svg>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Total Recommendations</p>
          <p class="text-2xl font-semibold text-gray-900">
            <%= @analytics_data[:recommendations_by_type].values.sum %>
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts and Analytics -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Recommendations by Type -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Recommendations by Type</h2>
        <div class="space-y-4">
          <% total_recommendations = @analytics_data[:recommendations_by_type].values.sum %>
          <% @analytics_data[:recommendations_by_type].each do |type, statuses| %>
            <% type_total = statuses.is_a?(Hash) ? statuses.values.sum : statuses.to_i %>
            <% percentage = total_recommendations > 0 ? ((type_total.to_f / total_recommendations) * 100).round(1) : 0 %>
            <div>
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700"><%= type.to_s.humanize %></span>
                <span class="text-sm text-gray-600"><%= type_total %> (<%= percentage %>%)</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full" style="width: <%= percentage %>%"></div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Top Performing Recommendations -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Top Performing Recommendations</h2>
        <% if @analytics_data[:top_performing_recommendations].any? %>
          <div class="space-y-3">
            <% @analytics_data[:top_performing_recommendations].each_with_index do |recommendation, index| %>
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <span class="inline-flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                      <span class="text-sm font-medium text-blue-600"><%= index + 1 %></span>
                    </span>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900"><%= truncate(recommendation[:title], length: 40) %></p>
                  </div>
                </div>
                <div class="flex-shrink-0">
                  <span class="text-sm font-medium text-green-600">
                    $<%= number_with_precision(recommendation[:revenue], precision: 2) %>
                  </span>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="text-center py-4">
            <p class="text-gray-500">No revenue-generating recommendations yet</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Pipeline Performance -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-4">Pipeline Performance</h2>
      <% if @analytics_data[:pipeline_performance].any? %>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pipeline</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Recommendations</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Implemented</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estimated Value</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Success Rate</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% @analytics_data[:pipeline_performance].each do |pipeline| %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">
                      <%= link_to truncate(pipeline[:pipeline_name], length: 30), pipeline_path(pipeline[:pipeline_id]), 
                          class: "hover:text-blue-600" %>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <%= pipeline[:recommendations_count] %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <%= pipeline[:implemented_count] %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    $<%= number_with_precision(pipeline[:estimated_value], precision: 2) %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <% success_rate = pipeline[:recommendations_count] > 0 ? 
                       ((pipeline[:implemented_count].to_f / pipeline[:recommendations_count]) * 100).round(1) : 0 %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                      <%= success_rate >= 70 ? 'bg-green-100 text-green-800' : 
                          success_rate >= 40 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' %>">
                      <%= success_rate %>%
                    </span>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      <% else %>
        <div class="text-center py-8">
          <p class="text-gray-500">No pipeline data available</p>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Trends Chart (Placeholder) -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-4">Recommendation Trends</h2>
      <div class="h-64 flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg">
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">Trends Chart</h3>
          <p class="mt-1 text-sm text-gray-500">Chart visualization would be implemented here with a JavaScript charting library</p>
          <div class="mt-4 text-xs text-gray-400">
            <p>Data points: <%= @analytics_data[:trends][:recommendations].count %> recommendation entries</p>
            <p>Acceptances: <%= @analytics_data[:trends][:acceptances].count %> acceptance entries</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for future chart implementation -->
<script>
  // Future: Implement charts with Chart.js or similar
  console.log('Analytics data:', <%= @analytics_data.to_json.html_safe %>);
</script>