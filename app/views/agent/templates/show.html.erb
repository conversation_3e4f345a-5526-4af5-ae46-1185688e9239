<div class="container mx-auto px-4 py-8">
  <!-- Breadcrumb -->
  <nav class="flex mb-6" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-1">
      <li>
        <%= link_to agent_templates_path, class: "text-gray-500 hover:text-gray-700" do %>
          Template Marketplace
        <% end %>
      </li>
      <li>
        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
        </svg>
      </li>
      <li class="text-gray-900"><%= truncate(@template.name, length: 50) %></li>
    </ol>
  </nav>

  <!-- Template Header -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="p-8">
      <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between">
        <div class="flex-1 lg:mr-8">
          <div class="flex items-center mb-4">
            <h1 class="text-3xl font-bold text-gray-900 mr-4"><%= @template.name %></h1>
            
            <!-- Category Badge -->
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
              <%= @template.category&.humanize || 'General' %>
            </span>
            
            <% if @template.industry.present? %>
              <span class="ml-2 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                <%= @template.industry.humanize %>
              </span>
            <% end %>
          </div>
          
          <p class="text-gray-600 text-lg mb-6"><%= @template.description %></p>
          
          <!-- Template Metrics -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="flex items-center">
              <svg class="h-5 w-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              <span class="text-sm text-gray-600">By <%= @creator&.name || 'DataReflow Team' %></span>
            </div>
            
            <div class="flex items-center">
              <svg class="h-5 w-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span class="text-sm text-gray-600"><%= pluralize(@template.purchases_count || 0, 'purchase') %></span>
            </div>
            
            <% if @template.average_rating.present? %>
              <div class="flex items-center">
                <svg class="h-5 w-5 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
                <span class="text-sm text-gray-600">
                  <%= number_with_precision(@template.average_rating, precision: 1) %> rating
                </span>
              </div>
            <% end %>
          </div>
        </div>
        
        <!-- Purchase Section -->
        <div class="lg:flex-shrink-0 lg:w-80">
          <div class="bg-gray-50 rounded-lg p-6">
            <div class="text-center mb-6">
              <div class="text-4xl font-bold text-gray-900 mb-2">
                <%= @template.price_cents > 0 ? "$#{@template.price}" : 'Free' %>
              </div>
              <p class="text-sm text-gray-600">One-time purchase</p>
            </div>
            
            <div class="space-y-3">
              <% if @can_purchase %>
                <%= link_to "Purchase Template", purchase_agent_template_path(@template), 
                    method: :post,
                    class: "w-full btn btn-primary btn-lg",
                    data: { 
                      turbo_method: :post,
                      confirm: @template.price_cents > 0 ? "Purchase this template for $#{@template.price}?" : "Add this free template to your account?"
                    } %>
              <% else %>
                <div class="w-full btn btn-disabled btn-lg">
                  <% if @template.creator_account_id == current_user&.account&.id %>
                    Your Template
                  <% elsif @template.pipelines.where(account: current_user&.account).exists? %>
                    Already Purchased
                  <% else %>
                    Not Available
                  <% end %>
                </div>
              <% end %>
              
              <%= link_to "Preview Template", preview_agent_template_path(@template),
                  class: "w-full btn btn-outline",
                  data: { 
                    turbo_frame: "template_preview_modal",
                    turbo_action: "advance"
                  } %>
            </div>
            
            <div class="mt-6 space-y-3 text-sm text-gray-600">
              <div class="flex items-center">
                <svg class="h-4 w-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Ready-to-use pipeline configuration
              </div>
              <div class="flex items-center">
                <svg class="h-4 w-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Pre-configured transformations
              </div>
              <div class="flex items-center">
                <svg class="h-4 w-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Detailed setup instructions
              </div>
              <div class="flex items-center">
                <svg class="h-4 w-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Community support
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Template Details Tabs -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="border-b border-gray-200">
      <nav class="flex space-x-8 px-6">
        <button class="template-tab active py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600" 
                data-tab="overview">
          Overview
        </button>
        <button class="template-tab py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" 
                data-tab="configuration">
          Configuration
        </button>
        <button class="template-tab py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" 
                data-tab="reviews">
          Reviews
        </button>
      </nav>
    </div>
    
    <!-- Overview Tab -->
    <div id="overview-tab" class="template-tab-content p-6">
      <!-- Use Cases -->
      <% if @template.use_cases.present? && @template.use_cases.any? %>
        <div class="mb-8">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Use Cases</h3>
          <div class="flex flex-wrap gap-2">
            <% @template.use_cases.each do |use_case| %>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                <%= use_case %>
              </span>
            <% end %>
          </div>
        </div>
      <% end %>
      
      <!-- Template Overview -->
      <div class="mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">What's Included</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 class="font-medium text-gray-900 mb-2">Data Sources</h4>
            <p class="text-sm text-gray-600">
              <%= @template.source_type&.humanize || 'Configurable source connector' %>
            </p>
          </div>
          <div>
            <h4 class="font-medium text-gray-900 mb-2">Destinations</h4>
            <p class="text-sm text-gray-600">
              <%= @template.destination_type&.humanize || 'Configurable destination connector' %>
            </p>
          </div>
          <% if @template.transformation_template.present? %>
            <div>
              <h4 class="font-medium text-gray-900 mb-2">Transformations</h4>
              <p class="text-sm text-gray-600">
                <%= pluralize(@template.transformation_template.keys.count, 'transformation rule') %>
              </p>
            </div>
          <% end %>
          <% if @template.schedule_template.present? %>
            <div>
              <h4 class="font-medium text-gray-900 mb-2">Schedule</h4>
              <p class="text-sm text-gray-600">
                Pre-configured scheduling options
              </p>
            </div>
          <% end %>
        </div>
      </div>
      
      <!-- Benefits -->
      <div>
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Benefits</h3>
        <ul class="space-y-2 text-sm text-gray-600">
          <li class="flex items-start">
            <svg class="h-4 w-4 text-green-500 mt-1 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Skip the complex setup - get started in minutes
          </li>
          <li class="flex items-start">
            <svg class="h-4 w-4 text-green-500 mt-1 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Tested configurations reduce errors and downtime
          </li>
          <li class="flex items-start">
            <svg class="h-4 w-4 text-green-500 mt-1 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Best practices built-in for optimal performance
          </li>
          <li class="flex items-start">
            <svg class="h-4 w-4 text-green-500 mt-1 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Customizable to fit your specific needs
          </li>
        </ul>
      </div>
    </div>
    
    <!-- Configuration Tab -->
    <div id="configuration-tab" class="template-tab-content p-6 hidden">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Technical Configuration</h3>
      
      <div class="space-y-6">
        <% if @template.source_config_template.present? %>
          <div>
            <h4 class="font-medium text-gray-900 mb-3">Source Configuration</h4>
            <div class="bg-gray-50 rounded-lg p-4">
              <pre class="text-sm text-gray-700 whitespace-pre-wrap"><%= JSON.pretty_generate(@template.source_config_template) %></pre>
            </div>
          </div>
        <% end %>
        
        <% if @template.destination_config_template.present? %>
          <div>
            <h4 class="font-medium text-gray-900 mb-3">Destination Configuration</h4>
            <div class="bg-gray-50 rounded-lg p-4">
              <pre class="text-sm text-gray-700 whitespace-pre-wrap"><%= JSON.pretty_generate(@template.destination_config_template) %></pre>
            </div>
          </div>
        <% end %>
        
        <% if @template.transformation_template.present? %>
          <div>
            <h4 class="font-medium text-gray-900 mb-3">Transformation Rules</h4>
            <div class="bg-gray-50 rounded-lg p-4">
              <pre class="text-sm text-gray-700 whitespace-pre-wrap"><%= JSON.pretty_generate(@template.transformation_template) %></pre>
            </div>
          </div>
        <% end %>
        
        <% if @template.schedule_template.present? %>
          <div>
            <h4 class="font-medium text-gray-900 mb-3">Schedule Template</h4>
            <div class="bg-gray-50 rounded-lg p-4">
              <pre class="text-sm text-gray-700 whitespace-pre-wrap"><%= JSON.pretty_generate(@template.schedule_template) %></pre>
            </div>
          </div>
        <% end %>
      </div>
    </div>
    
    <!-- Reviews Tab -->
    <div id="reviews-tab" class="template-tab-content p-6 hidden">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Customer Reviews</h3>
      
      <% if @reviews.any? %>
        <div class="space-y-6">
          <% @reviews.each do |review| %>
            <div class="border-b border-gray-200 pb-6 last:border-b-0 last:pb-0">
              <div class="flex items-center mb-2">
                <div class="flex text-yellow-400 mr-2">
                  <% review[:rating].times do %>
                    <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                  <% end %>
                </div>
                <span class="text-sm font-medium text-gray-900"><%= review[:user_name] %></span>
                <span class="text-sm text-gray-500 ml-2">• <%= time_ago_in_words(review[:created_at]) %> ago</span>
              </div>
              <p class="text-sm text-gray-700"><%= review[:comment] %></p>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="text-center py-8">
          <div class="mx-auto h-12 w-12 text-gray-400">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="h-12 w-12">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
          </div>
          <h3 class="mt-4 text-lg font-medium text-gray-900">No Reviews Yet</h3>
          <p class="mt-2 text-sm text-gray-600">Be the first to review this template!</p>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Similar Templates -->
  <% if @similar_templates&.any? %>
    <div class="mb-8">
      <h2 class="text-2xl font-bold text-gray-900 mb-6">Similar Templates</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <% @similar_templates.each do |template| %>
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">
              <%= link_to template.name, agent_template_path(template), class: "hover:text-blue-600" %>
            </h3>
            <p class="text-sm text-gray-600 mb-4"><%= truncate(template.description, length: 100) %></p>
            <div class="flex justify-between items-center">
              <span class="text-lg font-bold text-gray-900">
                <%= template.price_cents > 0 ? "$#{template.price}" : 'Free' %>
              </span>
              <%= link_to "View", agent_template_path(template), class: "btn btn-sm btn-outline" %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  <% end %>

  <!-- Personalized Recommendations -->
  <% if @personalized_recommendations&.any? %>
    <div class="bg-blue-50 rounded-lg border border-blue-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">
        <svg class="h-5 w-5 text-blue-600 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
        </svg>
        Recommended for You
      </h3>
      <div class="space-y-3">
        <% @personalized_recommendations.first(3).each do |rec| %>
          <div class="bg-white rounded-lg p-4">
            <div class="flex justify-between items-start">
              <div class="flex-1">
                <h4 class="font-medium text-gray-900">
                  <%= link_to rec[:template].name, agent_template_path(rec[:template]), class: "hover:text-blue-600" %>
                </h4>
                <p class="text-sm text-gray-600 mt-1">
                  Relevance: <%= (rec[:relevance_score] * 100).round %>%
                </p>
              </div>
              <div class="flex-shrink-0 ml-4">
                <%= link_to "View", agent_template_path(rec[:template]), class: "btn btn-xs btn-outline" %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  <% end %>
</div>

<!-- Preview Modal Frame -->
<turbo-frame id="template_preview_modal">
  <!-- Preview content will be loaded here -->
</turbo-frame>

<script>
  // Tab switching functionality
  document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('.template-tab');
    const contents = document.querySelectorAll('.template-tab-content');
    
    tabs.forEach(tab => {
      tab.addEventListener('click', function() {
        const targetTab = this.dataset.tab;
        
        // Update tab styles
        tabs.forEach(t => {
          t.classList.remove('active', 'border-blue-500', 'text-blue-600');
          t.classList.add('border-transparent', 'text-gray-500');
        });
        this.classList.add('active', 'border-blue-500', 'text-blue-600');
        this.classList.remove('border-transparent', 'text-gray-500');
        
        // Update content visibility
        contents.forEach(content => {
          if (content.id === `${targetTab}-tab`) {
            content.classList.remove('hidden');
          } else {
            content.classList.add('hidden');
          }
        });
      });
    });
  });
</script>