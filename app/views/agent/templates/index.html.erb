<div class="container mx-auto px-4 py-8">
  <!-- Header Section -->
  <div class="flex justify-between items-center mb-8">
    <div>
      <h1 class="text-3xl font-bold text-gray-900">Template Marketplace</h1>
      <p class="text-gray-600 mt-2">Discover and purchase pre-built pipeline templates to accelerate your data integration</p>
    </div>
    <div class="flex space-x-4">
      <%= link_to "My Templates", my_templates_agent_templates_path, class: "btn btn-outline" %>
      <%= link_to "Analytics", marketplace_analytics_agent_templates_path, class: "btn btn-outline" %>
    </div>
  </div>

  <!-- Featured Templates -->
  <% if @featured_templates&.any? %>
    <div class="mb-8">
      <h2 class="text-2xl font-bold text-gray-900 mb-4">Featured Templates</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <% @featured_templates.each do |template| %>
          <div class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg border border-blue-200 overflow-hidden">
            <div class="p-6">
              <div class="flex items-start justify-between mb-3">
                <div class="flex-1">
                  <h3 class="text-lg font-semibold text-gray-900">
                    <%= link_to template.name, agent_template_path(template), class: "hover:text-blue-600" %>
                  </h3>
                  <p class="text-sm text-gray-600 mt-1"><%= truncate(template.description, length: 100) %></p>
                </div>
                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                  Featured
                </span>
              </div>
              
              <div class="flex items-center justify-between text-sm text-gray-600">
                <div class="flex items-center space-x-4">
                  <span><%= template.category&.humanize %></span>
                  <span>• <%= pluralize(template.purchases_count || 0, 'purchase') %></span>
                </div>
                <div class="text-lg font-bold text-gray-900">
                  <%= template.price_cents > 0 ? "$#{template.price}" : 'Free' %>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  <% end %>

  <!-- Search and Filters -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <%= form_with url: agent_templates_path, method: :get, local: true, class: "space-y-4" do |form| %>
      <!-- Search Bar -->
      <div class="flex space-x-4">
        <div class="flex-1">
          <%= form.text_field :search, 
              placeholder: "Search templates...", 
              value: params[:search],
              class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" %>
        </div>
        <div>
          <%= form.submit "Search", class: "btn btn-primary" %>
        </div>
      </div>
      
      <!-- Filters Row -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <%= form.label :category, "Category", class: "block text-sm font-medium text-gray-700 mb-1" %>
          <%= form.select :category, 
              options_for_select([['All Categories', '']] + @categories.map { |cat| [cat.humanize, cat] }, params[:category]), 
              {}, 
              { class: "w-full rounded-md border-gray-300" } %>
        </div>
        
        <div>
          <%= form.label :industry, "Industry", class: "block text-sm font-medium text-gray-700 mb-1" %>
          <%= form.select :industry, 
              options_for_select([['All Industries', '']] + @industries.map { |ind| [ind.humanize, ind] }, params[:industry]), 
              {}, 
              { class: "w-full rounded-md border-gray-300" } %>
        </div>
        
        <div>
          <%= form.label :price_range, "Price Range", class: "block text-sm font-medium text-gray-700 mb-1" %>
          <%= form.select :price_range, 
              options_for_select([
                ['All Prices', ''],
                ['Free', 'free'],
                ['Under $10', 'under_10'],
                ['Under $25', 'under_25'],
                ['$25+', 'over_25']
              ], params[:price_range]), 
              {}, 
              { class: "w-full rounded-md border-gray-300" } %>
        </div>
        
        <div>
          <%= form.label :sort, "Sort By", class: "block text-sm font-medium text-gray-700 mb-1" %>
          <%= form.select :sort, 
              options_for_select([
                ['Name', ''],
                ['Most Popular', 'popular'],
                ['Highest Rated', 'rating'],
                ['Price: Low to High', 'price_low'],
                ['Price: High to Low', 'price_high'],
                ['Newest', 'newest']
              ], params[:sort]), 
              {}, 
              { class: "w-full rounded-md border-gray-300" } %>
        </div>
      </div>
      
      <div class="flex justify-between">
        <%= form.submit "Apply Filters", class: "btn btn-primary" %>
        <%= link_to "Clear All", agent_templates_path, class: "btn btn-outline" %>
      </div>
    <% end %>
  </div>

  <!-- Templates Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <% if @templates.any? %>
      <% @templates.each do |template| %>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
          <!-- Template Header -->
          <div class="p-6">
            <div class="flex items-start justify-between mb-3">
              <div class="flex-1">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                  <%= link_to template.name, agent_template_path(template), class: "hover:text-blue-600" %>
                </h3>
                <p class="text-sm text-gray-600 mb-3"><%= truncate(template.description, length: 120) %></p>
              </div>
            </div>
            
            <!-- Template Metadata -->
            <div class="space-y-2 mb-4">
              <div class="flex items-center text-sm text-gray-600">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                </svg>
                <span class="capitalize"><%= template.category %></span>
                <% if template.industry.present? %>
                  <span class="mx-2">•</span>
                  <span class="capitalize"><%= template.industry %></span>
                <% end %>
              </div>
              
              <div class="flex items-center text-sm text-gray-600">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                By <%= template.creator_account&.name || 'DataReflow Team' %>
              </div>
              
              <div class="flex items-center text-sm text-gray-600">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <%= pluralize(template.purchases_count || 0, 'purchase') %>
                <% if template.average_rating.present? %>
                  <span class="mx-2">•</span>
                  <div class="flex items-center">
                    <svg class="h-4 w-4 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    <%= number_with_precision(template.average_rating, precision: 1) %>
                  </div>
                <% end %>
              </div>
            </div>
            
            <!-- Use Cases Tags -->
            <% if template.use_cases.present? && template.use_cases.is_a?(Array) %>
              <div class="flex flex-wrap gap-2 mb-4">
                <% template.use_cases.first(3).each do |use_case| %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    <%= use_case %>
                  </span>
                <% end %>
                <% if template.use_cases.count > 3 %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    +<%= template.use_cases.count - 3 %> more
                  </span>
                <% end %>
              </div>
            <% end %>
            
            <!-- Price and Actions -->
            <div class="flex items-center justify-between">
              <div class="text-2xl font-bold text-gray-900">
                <%= template.price_cents > 0 ? "$#{template.price}" : 'Free' %>
              </div>
              
              <div class="flex space-x-2">
                <%= link_to "Preview", preview_agent_template_path(template), 
                    class: "btn btn-sm btn-outline",
                    data: { turbo_frame: "template_preview" } %>
                <%= link_to "View", agent_template_path(template), 
                    class: "btn btn-sm btn-primary" %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    <% else %>
      <!-- Empty State -->
      <div class="col-span-full">
        <div class="text-center py-12">
          <div class="mx-auto h-12 w-12 text-gray-400">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="h-12 w-12">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
          </div>
          <h3 class="mt-4 text-lg font-semibold text-gray-900">No Templates Found</h3>
          <p class="mt-2 text-gray-600 max-w-md mx-auto">
            No templates match your current filters. Try adjusting your search criteria or browse all templates.
          </p>
          <div class="mt-6">
            <%= link_to "Clear Filters", agent_templates_path, class: "btn btn-primary" %>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Pagination -->
  <% if @templates.respond_to?(:current_page) %>
    <div class="flex justify-center mt-8">
      <%= paginate @templates %>
    </div>
  <% end %>
</div>

<!-- Template Preview Modal Placeholder -->
<turbo-frame id="template_preview">
  <!-- Preview content will be loaded here -->
</turbo-frame>

<script>
  // Auto-submit form on select changes
  document.addEventListener('DOMContentLoaded', function() {
    const selects = document.querySelectorAll('select[name="category"], select[name="industry"], select[name="price_range"], select[name="sort"]');
    selects.forEach(select => {
      select.addEventListener('change', function() {
        this.form.submit();
      });
    });
  });
</script>