<div class="container mx-auto px-4 py-8">
  <!-- Header Section -->
  <div class="flex justify-between items-center mb-8">
    <div>
      <h1 class="text-3xl font-bold text-gray-900">Revenue Dashboard</h1>
      <p class="text-gray-600 mt-2">Track your AI agent revenue and passive income performance</p>
    </div>
    <div class="flex space-x-4">
      <%= link_to "Analytics", analytics_agent_revenue_index_path, class: "btn btn-outline" %>
      <%= link_to "MRR Analysis", mrr_analysis_agent_revenue_index_path, class: "btn btn-outline" %>
      <%= link_to "Export Data", export_agent_revenue_index_path(format: :csv), class: "btn btn-outline" %>
    </div>
  </div>

  <!-- Revenue Summary Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-green-100">
          <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
          </svg>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Total Revenue</p>
          <p class="text-2xl font-semibold text-gray-900">$<%= number_with_precision(@revenue_summary[:total_revenue], precision: 2) %></p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-blue-100">
          <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Monthly Revenue</p>
          <p class="text-2xl font-semibold text-gray-900">$<%= number_with_precision(@revenue_summary[:monthly_revenue], precision: 2) %></p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-purple-100">
          <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
          </svg>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Weekly Revenue</p>
          <p class="text-2xl font-semibold text-gray-900">$<%= number_with_precision(@revenue_summary[:weekly_revenue], precision: 2) %></p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-yellow-100">
          <svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Daily Average</p>
          <p class="text-2xl font-semibold text-gray-900">$<%= number_with_precision(@revenue_summary[:daily_average], precision: 2) %></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Growth Metrics -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Growth Rate</h3>
      <div class="flex items-center">
        <div class="text-3xl font-bold 
          <%= @revenue_summary[:growth_rate] > 0 ? 'text-green-600' : 
              @revenue_summary[:growth_rate] < 0 ? 'text-red-600' : 'text-gray-600' %>">
          <%= @revenue_summary[:growth_rate] > 0 ? '+' : '' %><%= number_with_precision(@revenue_summary[:growth_rate], precision: 1) %>%
        </div>
        <div class="ml-3">
          <% if @revenue_summary[:growth_rate] > 0 %>
            <svg class="h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
          <% elsif @revenue_summary[:growth_rate] < 0 %>
            <svg class="h-5 w-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
            </svg>
          <% else %>
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
            </svg>
          <% end %>
        </div>
      </div>
      <p class="text-sm text-gray-600 mt-2">Month-over-month change</p>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Active Revenue Streams</h3>
      <div class="text-3xl font-bold text-gray-900">
        <%= @revenue_summary[:active_revenue_streams] %>
      </div>
      <p class="text-sm text-gray-600 mt-2">Revenue-generating features</p>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Avg per Recommendation</h3>
      <div class="text-3xl font-bold text-gray-900">
        $<%= number_with_precision(@revenue_summary.dig(:average_per_recommendation) || 0, precision: 2) %>
      </div>
      <p class="text-sm text-gray-600 mt-2">Revenue per implemented recommendation</p>
    </div>
  </div>

  <!-- Monthly Trend Chart -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="p-6">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Monthly Revenue Trend</h3>
        <div class="flex space-x-2 text-sm">
          <span class="text-gray-600">Last 12 months</span>
        </div>
      </div>
      
      <!-- Chart Placeholder -->
      <div class="h-64 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">Revenue Trend Chart</h3>
          <p class="mt-1 text-sm text-gray-500">Chart visualization would be implemented here</p>
          <div class="mt-2 text-xs text-gray-400">
            Data points: <%= @monthly_trend.length %> months
          </div>
        </div>
      </div>
      
      <!-- Monthly Data Table -->
      <div class="mt-6 overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Change</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% @monthly_trend.reverse.first(6).each_with_index do |month, index| %>
              <% prev_month = @monthly_trend.reverse[index + 1] %>
              <% change = prev_month ? ((month[:revenue] - prev_month[:revenue]) / prev_month[:revenue] * 100).round(1) : 0 if prev_month&.dig(:revenue)&.> 0 %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  <%= month[:formatted_month] %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  $<%= number_with_precision(month[:revenue], precision: 2) %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                  <% if change && change != 0 %>
                    <span class="<%= change > 0 ? 'text-green-600' : 'text-red-600' %>">
                      <%= change > 0 ? '+' : '' %><%= change %>%
                    </span>
                  <% else %>
                    <span class="text-gray-400">-</span>
                  <% end %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Revenue Sources -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Revenue by Source</h3>
        <% if @revenue_sources.any? %>
          <div class="space-y-4">
            <% @revenue_sources.each do |source| %>
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="w-4 h-4 bg-blue-500 rounded-full mr-3"></div>
                  <span class="text-sm font-medium text-gray-900"><%= source[:source] %></span>
                </div>
                <div class="flex items-center space-x-3">
                  <span class="text-sm text-gray-600"><%= source[:percentage] %>%</span>
                  <span class="text-sm font-medium text-gray-900">$<%= number_with_precision(source[:amount], precision: 2) %></span>
                </div>
              </div>
              <div class="ml-7">
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-blue-500 h-2 rounded-full" style="width: <%= source[:percentage] %>%"></div>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="text-center py-8">
            <p class="text-gray-500">No revenue sources yet</p>
          </div>
        <% end %>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Performing Recommendations</h3>
        <% if @top_recommendations.any? %>
          <div class="space-y-4">
            <% @top_recommendations.each_with_index do |rec, index| %>
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                  <span class="inline-flex items-center justify-center w-6 h-6 bg-blue-100 rounded-full text-xs font-medium text-blue-600 mr-3">
                    <%= index + 1 %>
                  </span>
                  <div>
                    <p class="text-sm font-medium text-gray-900"><%= truncate(rec[:title], length: 30) %></p>
                    <p class="text-xs text-gray-600"><%= rec[:type] %> • <%= rec[:confidence_score] %>% confidence</p>
                  </div>
                </div>
                <div class="text-right">
                  <p class="text-sm font-medium text-green-600">$<%= number_with_precision(rec[:revenue_generated], precision: 2) %></p>
                  <% if rec[:implemented_at] %>
                    <p class="text-xs text-gray-500"><%= time_ago_in_words(rec[:implemented_at]) %> ago</p>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="text-center py-8">
            <p class="text-gray-500">No revenue-generating recommendations yet</p>
            <%= link_to "View Recommendations", agent_recommendations_path, class: "text-blue-600 hover:text-blue-500 text-sm" %>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Growth Metrics Details -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Growth Metrics</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="text-center">
          <div class="text-2xl font-bold 
            <%= @growth_metrics[:month_over_month] > 0 ? 'text-green-600' : 
                @growth_metrics[:month_over_month] < 0 ? 'text-red-600' : 'text-gray-600' %>">
            <%= @growth_metrics[:month_over_month] > 0 ? '+' : '' %><%= number_with_precision(@growth_metrics[:month_over_month], precision: 1) %>%
          </div>
          <p class="text-sm text-gray-600 mt-1">Month-over-Month</p>
        </div>
        
        <div class="text-center">
          <div class="text-2xl font-bold 
            <%= @growth_metrics[:quarter_over_quarter] > 0 ? 'text-green-600' : 
                @growth_metrics[:quarter_over_quarter] < 0 ? 'text-red-600' : 'text-gray-600' %>">
            <%= @growth_metrics[:quarter_over_quarter] > 0 ? '+' : '' %><%= number_with_precision(@growth_metrics[:quarter_over_quarter], precision: 1) %>%
          </div>
          <p class="text-sm text-gray-600 mt-1">Quarter-over-Quarter</p>
        </div>
        
        <div class="text-center">
          <div class="text-2xl font-bold 
            <%= @growth_metrics[:year_over_year] > 0 ? 'text-green-600' : 
                @growth_metrics[:year_over_year] < 0 ? 'text-red-600' : 'text-gray-600' %>">
            <%= @growth_metrics[:year_over_year] > 0 ? '+' : '' %><%= number_with_precision(@growth_metrics[:year_over_year], precision: 1) %>%
          </div>
          <p class="text-sm text-gray-600 mt-1">Year-over-Year</p>
        </div>
      </div>
      
      <div class="mt-6 text-center">
        <div class="inline-flex items-center">
          <% case @growth_metrics[:trend_direction] %>
          <% when 'up' %>
            <svg class="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
            <span class="text-green-600 font-medium">Growing</span>
          <% when 'down' %>
            <svg class="h-5 w-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
            </svg>
            <span class="text-red-600 font-medium">Declining</span>
          <% else %>
            <svg class="h-5 w-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
            </svg>
            <span class="text-gray-600 font-medium">Stable</span>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for future chart implementation -->
<script>
  // Revenue data for charts
  const monthlyData = <%= @monthly_trend.to_json.html_safe %>;
  const revenueSourceData = <%= @revenue_sources.to_json.html_safe %>;
  
  console.log('Revenue dashboard data loaded:', {
    monthly: monthlyData,
    sources: revenueSourceData
  });
  
  // Future: Implement charts with Chart.js or similar
</script>