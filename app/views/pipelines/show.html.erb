<% content_for :page_title, @pipeline.name %>

<div class="max-w-7xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <%= link_to pipelines_path,
            class: "inline-flex items-center text-sm text-gray-500 hover:text-gray-700" do %>
          <svg class="mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          Back to Pipelines
        <% end %>
        <div>
          <h1 class="text-2xl font-bold text-gray-900"><%= @pipeline.name %></h1>
          <div class="flex items-center mt-1 space-x-4">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= @pipeline.status == 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %>">
              <%= @pipeline.status.humanize %>
            </span>
            <span class="text-sm text-gray-500">
              Created <%= time_ago_in_words(@pipeline.created_at) %> ago
            </span>
          </div>
        </div>
      </div>
      <div class="flex items-center space-x-3">
        <% if @pipeline.status == 'active' %>
          <%= link_to "#",
              class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500" do %>
            <svg class="mr-2 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
            </svg>
            Run Pipeline
          <% end %>
        <% end %>
        <%= link_to edit_pipeline_path(@pipeline),
            class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
          <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
          </svg>
          Edit
        <% end %>
      </div>
    </div>
  </div>

  <!-- Pipeline Overview -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-8">
      <!-- Description -->
      <% if @pipeline.description.present? %>
        <div class="bg-white shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Description</h3>
          <p class="text-gray-700"><%= simple_format(@pipeline.description) %></p>
        </div>
      <% end %>

      <!-- Recent Executions -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Recent Executions</h3>
        </div>
        <div class="p-6">
          <% if @pipeline.respond_to?(:pipeline_executions) && @pipeline.pipeline_executions.any? %>
            <div class="space-y-4">
              <% @pipeline.pipeline_executions.recent.limit(5).each do |execution| %>
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                      <% if execution.respond_to?(:status) %>
                        <% case execution.status %>
                        <% when 'completed' %>
                          <div class="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="h-5 w-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                          </div>
                        <% when 'failed' %>
                          <div class="h-8 w-8 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="h-5 w-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                            </svg>
                          </div>
                        <% else %>
                          <div class="h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                            <svg class="h-5 w-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                            </svg>
                          </div>
                        <% end %>
                      <% else %>
                        <div class="h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center">
                          <svg class="h-5 w-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                          </svg>
                        </div>
                      <% end %>
                    </div>
                    <div>
                      <p class="text-sm font-medium text-gray-900">
                        <%= execution.respond_to?(:status) ? execution.status.humanize : 'Completed' %>
                      </p>
                      <p class="text-sm text-gray-500">
                        <%= time_ago_in_words(execution.created_at) %> ago
                      </p>
                    </div>
                  </div>
                  <div class="text-right">
                    <% if execution.respond_to?(:duration) && execution.duration %>
                      <p class="text-sm text-gray-900"><%= execution.duration %>s</p>
                    <% end %>
                    <% if execution.respond_to?(:records_processed) && execution.records_processed %>
                      <p class="text-sm text-gray-500"><%= number_with_delimiter(execution.records_processed) %> records</p>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No executions yet</h3>
              <p class="mt-1 text-sm text-gray-500">
                This pipeline hasn't been executed yet.
              </p>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
      <!-- Pipeline Details -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Pipeline Details</h3>
        <dl class="space-y-3">
          <div>
            <dt class="text-sm font-medium text-gray-500">Schedule Type</dt>
            <dd class="text-sm text-gray-900">
              <%= @pipeline.schedule_type.humanize %>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Status</dt>
            <dd class="text-sm text-gray-900">
              <%= @pipeline.status.humanize %>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Created by</dt>
            <dd class="text-sm text-gray-900">
              <%= @pipeline.created_by&.full_name || 'System' %>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Last updated</dt>
            <dd class="text-sm text-gray-900">
              <%= time_ago_in_words(@pipeline.updated_at) %> ago
            </dd>
          </div>
        </dl>
      </div>

      <!-- Data Flow -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Data Flow</h3>
        <div class="space-y-4">
          <div>
            <dt class="text-sm font-medium text-gray-500 mb-1">Source</dt>
            <dd class="text-sm text-gray-900">
              <% if @pipeline.source_config.present? %>
                <div class="flex items-center">
                  <div class="h-6 w-6 bg-blue-100 rounded flex items-center justify-center mr-2">
                    <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
                    </svg>
                  </div>
                  <%= @pipeline.source_type.humanize %>
                </div>
              <% else %>
                <span class="text-gray-400">Not configured</span>
              <% end %>
            </dd>
          </div>

          <div class="flex justify-center">
            <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
            </svg>
          </div>

          <div>
            <dt class="text-sm font-medium text-gray-500 mb-1">Destination</dt>
            <dd class="text-sm text-gray-900">
              <% if @pipeline.destination_config.present? %>
                <div class="flex items-center">
                  <div class="h-6 w-6 bg-green-100 rounded flex items-center justify-center mr-2">
                    <svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
                    </svg>
                  </div>
                  <%= @pipeline.destination_type.humanize %>
                </div>
              <% else %>
                <span class="text-gray-400">Not configured</span>
              <% end %>
            </dd>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div class="space-y-3">
          <%= link_to "#",
              class: "w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
            View Logs
          <% end %>
          <%= link_to "#",
              class: "w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            Export Configuration
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
