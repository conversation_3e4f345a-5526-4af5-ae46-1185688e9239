<% content_for :page_title, "Edit Pipeline" %>

<!-- Enhanced Styles for Pipeline Form -->
<style>
  .pipeline-wizard-step { display: none; }
  .pipeline-wizard-step.active { display: block !important; }
  .step-indicator.completed { background-color: #10b981 !important; color: white !important; }
  .step-indicator.active { background-color: #3b82f6 !important; color: white !important; }
  .step-indicator { background-color: #e5e7eb; color: #6b7280; transition: all 0.3s ease; }
  .pipeline-template-card { transition: all 0.2s ease; cursor: pointer; }
  .pipeline-template-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
  .pipeline-template-card.selected { border-color: #3b82f6 !important; background-color: #eff6ff !important; }
  .schedule-option { transition: all 0.2s ease; cursor: pointer; }
  .schedule-option:hover { background-color: #f9fafb; }
  .schedule-option.selected { border-color: #3b82f6; background-color: #eff6ff; }
  .data-flow-connector { transition: all 0.3s ease; }
  .data-flow-connector:hover { transform: scale(1.05); }
  .json-editor { font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; }
  .pipeline-preview { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
  .fade-in { animation: fadeIn 0.3s ease-in; }
  @keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
  .step-error { animation: slideDown 0.3s ease-out; }
  @keyframes slideDown { from { opacity: 0; transform: translateY(-10px); } to { opacity: 1; transform: translateY(0); } }
</style>

<div class="max-w-7xl mx-auto" data-controller="pipeline-wizard" data-pipeline-wizard-current-step-value="1">
  <!-- Enhanced Header with Progress -->
  <div class="mb-8">
    <!-- Breadcrumb Navigation -->
    <nav class="flex mb-4" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        <li class="inline-flex items-center">
          <%= link_to pipelines_path, class: "inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600" do %>
            <svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
            </svg>
            Pipelines
          <% end %>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <%= link_to @pipeline.name, @pipeline, class: "ml-1 text-sm font-medium text-gray-700 hover:text-indigo-600 md:ml-2" %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Edit</span>
          </div>
        </li>
      </ol>
    </nav>

    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Edit Pipeline</h1>
        <p class="mt-2 text-lg text-gray-600">
          Update your pipeline configuration and settings
        </p>
      </div>
      
      <!-- Pipeline Status Badge -->
      <div class="flex items-center space-x-3">
        <%= status_badge(@pipeline.status) %>
        <% if @pipeline.last_executed_at %>
          <span class="text-sm text-gray-500">
            Last run: <%= time_ago_in_words(@pipeline.last_executed_at) %> ago
          </span>
        <% end %>
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="mt-6">
      <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
        <span>Edit Progress</span>
        <span><span data-pipeline-wizard-target="stepNumber">1</span> of 5</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div class="bg-indigo-600 h-2 rounded-full transition-all duration-300"
             style="width: 20%"
             data-pipeline-wizard-target="progressBar"></div>
      </div>
    </div>

    <!-- Step Indicators -->
    <div class="mt-4 flex items-center justify-center space-x-4">
      <div class="flex items-center">
        <div class="step-indicator w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-indigo-600 text-white"
             data-pipeline-wizard-target="stepIndicator" data-step="1">1</div>
        <span class="ml-2 text-sm font-medium text-gray-900">Template</span>
      </div>
      <div class="flex-1 h-0.5 bg-gray-200"></div>
      <div class="flex items-center">
        <div class="step-indicator w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-gray-300 text-gray-600"
             data-pipeline-wizard-target="stepIndicator" data-step="2">2</div>
        <span class="ml-2 text-sm font-medium text-gray-500">Basic Info</span>
      </div>
      <div class="flex-1 h-0.5 bg-gray-200"></div>
      <div class="flex items-center">
        <div class="step-indicator w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-gray-300 text-gray-600"
             data-pipeline-wizard-target="stepIndicator" data-step="3">3</div>
        <span class="ml-2 text-sm font-medium text-gray-500">Schedule</span>
      </div>
      <div class="flex-1 h-0.5 bg-gray-200"></div>
      <div class="flex items-center">
        <div class="step-indicator w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-gray-300 text-gray-600"
             data-pipeline-wizard-target="stepIndicator" data-step="4">4</div>
        <span class="ml-2 text-sm font-medium text-gray-500">Data Flow</span>
      </div>
      <div class="flex-1 h-0.5 bg-gray-200"></div>
      <div class="flex items-center">
        <div class="step-indicator w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-gray-300 text-gray-600"
             data-pipeline-wizard-target="stepIndicator" data-step="5">5</div>
        <span class="ml-2 text-sm font-medium text-gray-500">Review</span>
      </div>
    </div>
  </div>

  <!-- Form Container -->
  <div class="bg-white shadow-xl rounded-2xl overflow-hidden">
    <%= form_with model: @pipeline, 
        local: true,
        class: "pipeline-form",
        data: { 
          controller: "pipeline-validation",
          pipeline_validation_target: "form",
          auto_save_url: pipeline_path(@pipeline),
          auto_save_interval: 30000
        } do |form| %>

      <!-- Hidden Fields for Form State -->
      <%= form.hidden_field :template_type, id: "pipeline_template_type", value: @pipeline.template_type %>

      <!-- Step 1: Template Selection -->
      <div class="pipeline-wizard-step active fade-in" data-pipeline-wizard-target="step" data-step="1">
        <div class="p-8">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Choose Pipeline Template</h2>
            <p class="text-gray-600">Select a template type for your pipeline or keep the current one</p>
            <% if @pipeline.template_type.present? %>
              <div class="mt-4 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                Current: <%= @pipeline.template_type.humanize %>
              </div>
            <% end %>
          </div>

          <!-- Pipeline Template Cards -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <% if (@pipeline_templates || []).any? %>
              <% (@pipeline_templates || []).each do |template| %>
                <div class="pipeline-template-card border-2 border-gray-200 rounded-xl p-6 cursor-pointer hover:border-indigo-300 transition-all duration-200 <%= 'selected' if @pipeline.template_type == template[:id] %>"
                     data-action="click->pipeline-wizard#selectTemplate"
                     data-template="<%= template[:id] %>"
                     data-template-name="<%= template[:name] %>"
                     data-template-description="<%= template[:description] %>"
                     style="<%= @pipeline.template_type == template[:id] ? 'border-color: #3b82f6; background-color: #eff6ff;' : '' %>">
                  <div class="text-center">
                    <div class="w-16 h-16 <%= template_icon_bg_class(template[:id]) %> rounded-full flex items-center justify-center mx-auto mb-4">
                      <%= render_template_icon(template[:icon], template[:id]) %>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2"><%= template[:name] %></h3>
                    <p class="text-sm text-gray-600 mb-4"><%= template[:description] %></p>
                    <div class="flex flex-wrap gap-1 justify-center">
                      <% template[:tags].each do |tag| %>
                        <span class="<%= template_tag_class(tag) %>"><%= tag %></span>
                      <% end %>
                    </div>
                    <% if @pipeline.template_type == template[:id] %>
                      <div class="mt-3 flex items-center justify-center text-sm text-blue-600">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        Currently Selected
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>
            <% else %>
              <div class="col-span-full text-center py-8">
                <div class="text-gray-500">
                  <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                  </svg>
                  <p class="text-lg font-medium text-gray-900 mb-2">No templates available</p>
                  <p class="text-sm text-gray-600">Pipeline templates are loading...</p>
                </div>
              </div>
            <% end %>
          </div>

          <!-- Template Change Warning -->
          <div class="max-w-2xl mx-auto">
            <div class="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-amber-800">Template Change Notice</h3>
                  <div class="mt-2 text-sm text-amber-700">
                    <p>Changing the template type will update the suggested configurations in the following steps. Your existing configurations will be preserved but you may want to review them for compatibility.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 2: Basic Information -->
      <div class="pipeline-wizard-step fade-in" data-pipeline-wizard-target="step" data-step="2">
        <div class="p-8">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Basic Information</h2>
            <p class="text-gray-600">Update your pipeline's basic details and settings</p>
          </div>

          <div class="max-w-2xl mx-auto space-y-6">
            <!-- Pipeline Name -->
            <div>
              <%= form.label :name, class: "block text-sm font-medium text-gray-700 mb-2" do %>
                Pipeline Name
                <span class="text-red-500">*</span>
              <% end %>
              <%= form.text_field :name,
                  class: "block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                  placeholder: "Enter a descriptive name for your pipeline",
                  required: true,
                  data: { 
                    action: "input->pipeline-validation#validateField",
                    validation_rules: "required|minLength:3|maxLength:100"
                  } %>
            </div>

            <!-- Pipeline Description -->
            <div>
              <%= form.label :description, class: "block text-sm font-medium text-gray-700 mb-2" do %>
                Description
              <% end %>
              <%= form.text_area :description,
                  rows: 3,
                  class: "block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                  placeholder: "Describe what this pipeline does and its purpose" %>
            </div>

            <!-- Pipeline Status -->
            <div>
              <%= form.label :status, class: "block text-sm font-medium text-gray-700 mb-2" do %>
                Status
              <% end %>
              <%= form.select :status,
                  options_for_select([
                    ['Draft', 'draft'],
                    ['Active', 'active'],
                    ['Paused', 'paused'],
                    ['Archived', 'archived']
                  ], @pipeline.status),
                  {},
                  class: "block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
            </div>

            <!-- Template Type (Read-only) -->
            <% if @pipeline.template_type.present? %>
              <div>
                <%= form.label :template_type, class: "block text-sm font-medium text-gray-700 mb-2" do %>
                  Pipeline Template
                <% end %>
                <div class="block w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-700 sm:text-sm">
                  <%= @pipeline.template_type.humanize %>
                </div>
                <p class="mt-1 text-sm text-gray-500">Template type cannot be changed after creation</p>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Step 3: Schedule Configuration -->
      <div class="pipeline-wizard-step fade-in" data-pipeline-wizard-target="step" data-step="3">
        <div class="p-8">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Schedule Configuration</h2>
            <p class="text-gray-600">Configure when and how often your pipeline should run</p>
          </div>

          <div class="max-w-2xl mx-auto space-y-6">
            <!-- Schedule Type -->
            <div>
              <%= form.label :schedule_type, class: "block text-sm font-medium text-gray-700 mb-3" do %>
                Schedule Type
                <span class="text-red-500">*</span>
              <% end %>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <% [
                  ['manual', 'Manual', 'Run manually when needed'],
                  ['real_time', 'Real-time', 'Process data as it arrives'],
                  ['hourly', 'Hourly', 'Run every hour'],
                  ['daily', 'Daily', 'Run once per day'],
                  ['weekly', 'Weekly', 'Run once per week'],
                  ['cron', 'Custom', 'Custom cron expression']
                ].each do |value, label, description| %>
                  <label class="schedule-option relative flex items-start p-4 border-2 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200 <%= @pipeline.schedule_type == value ? 'selected border-indigo-500 bg-indigo-50' : 'border-gray-200' %>"
                         data-action="click->pipeline-wizard#selectScheduleOption"
                         data-schedule-value="<%= value %>">
                    <%= form.radio_button :schedule_type, value,
                        class: "sr-only",
                        checked: @pipeline.schedule_type == value,
                        data: { pipeline_wizard_target: "scheduleRadio" } %>
                    <div class="flex items-center space-x-3">
                      <div class="w-4 h-4 border-2 <%= @pipeline.schedule_type == value ? 'border-indigo-600' : 'border-gray-300' %> rounded-full flex items-center justify-center">
                        <div class="w-2 h-2 bg-indigo-600 rounded-full <%= @pipeline.schedule_type == value ? 'block' : 'hidden' %>"></div>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-900"><%= label %></div>
                        <div class="text-xs text-gray-500"><%= description %></div>
                      </div>
                    </div>
                  </label>
                <% end %>
              </div>
            </div>

            <!-- Custom Schedule Config -->
            <div id="schedule-config" style="display: none;">
              <%= form.label :schedule_config, class: "block text-sm font-medium text-gray-700 mb-2" do %>
                Schedule Configuration
              <% end %>
              <%= form.text_area :schedule_config,
                  rows: 3,
                  class: "json-editor block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                  placeholder: '{"cron": "0 0 * * *", "timezone": "UTC"}',
                  data: { 
                    action: "input->pipeline-validation#validateJSON",
                    validation_rules: "json"
                  } %>
              <p class="mt-2 text-sm text-gray-500">
                JSON configuration for custom scheduling. 
                <a href="#" class="text-indigo-600 hover:text-indigo-500">View examples</a>
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 4: Data Flow Setup -->
      <div class="pipeline-wizard-step fade-in" data-pipeline-wizard-target="step" data-step="4">
        <div class="p-8">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Set Up Your Data Flow</h2>
            <p class="text-gray-600">Tell us where your data comes from and where it should go</p>
          </div>

          <!-- Data Flow Visual -->
          <div class="max-w-4xl mx-auto mb-8">
            <div class="flex items-center justify-center space-x-8">
              <div class="flex flex-col items-center">
                <div class="w-20 h-20 bg-blue-100 rounded-xl flex items-center justify-center mb-3">
                  <svg class="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.79 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.79 4 8 4s8-1.79 8-4M4 7c0-2.21 3.79-4 8-4s8 1.79 8 4"/>
                  </svg>
                </div>
                <span class="text-sm font-medium text-gray-700">Data Source</span>
              </div>

              <div class="flex-1 flex items-center">
                <div class="w-full h-0.5 bg-gradient-to-r from-blue-300 to-green-300"></div>
                <svg class="w-6 h-6 text-green-500 mx-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                </svg>
                <div class="w-full h-0.5 bg-gradient-to-r from-green-300 to-green-500"></div>
              </div>

              <div class="flex flex-col items-center">
                <div class="w-20 h-20 bg-green-100 rounded-xl flex items-center justify-center mb-3">
                  <svg class="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                  </svg>
                </div>
                <span class="text-sm font-medium text-gray-700">Destination</span>
              </div>
            </div>
          </div>

          <div class="max-w-4xl mx-auto space-y-8">
            <!-- Data Source Configuration -->
            <div class="bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 rounded-xl p-6">
              <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                  <span class="text-white font-bold text-sm">1</span>
                </div>
                <h3 class="text-lg font-medium text-blue-900">Where does your data come from?</h3>
              </div>

              <!-- Data Source Type Selection -->
              <div class="mb-6">
                <label class="block text-sm font-medium text-blue-800 mb-3">Choose your data source type:</label>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <% current_source_type = @pipeline.source_config.is_a?(Hash) ? @pipeline.source_config['type'] : 'database' %>
                  <% [
                    ['database', 'Database', 'MySQL, PostgreSQL, SQL Server'],
                    ['file', 'Files', 'CSV, Excel, JSON files'],
                    ['api', 'API/Webhook', 'REST APIs, webhooks'],
                    ['cloud', 'Cloud Storage', 'AWS S3, Google Drive'],
                    ['stream', 'Data Stream', 'Kafka, real-time feeds'],
                    ['other', 'Other', 'Custom data source']
                  ].each do |value, label, description| %>
                    <label class="data-source-option relative flex flex-col p-4 border-2 rounded-lg cursor-pointer hover:bg-blue-50 transition-colors duration-200 <%= current_source_type == value ? 'border-blue-500 bg-blue-50' : 'border-blue-200 bg-white' %>"
                           data-action="click->pipeline-wizard#selectDataSourceType"
                           data-source-type="<%= value %>">
                      <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-900"><%= label %></span>
                        <div class="w-4 h-4 border-2 <%= current_source_type == value ? 'border-blue-600' : 'border-gray-300' %> rounded-full flex items-center justify-center">
                          <div class="w-2 h-2 bg-blue-600 rounded-full <%= current_source_type == value ? 'block' : 'hidden' %>"></div>
                        </div>
                      </div>
                      <span class="text-xs text-gray-600"><%= description %></span>
                    </label>
                  <% end %>
                </div>
              </div>

              <!-- Dynamic Source Configuration Forms -->
              <div id="source-config-forms">
                <!-- Database Configuration -->
                <div class="source-config-form <%= current_source_type == 'database' ? 'block' : 'hidden' %>" data-source-type="database">
                  <h4 class="text-md font-medium text-blue-800 mb-3">Database Connection Details</h4>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Database Type</label>
                      <select class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                              data-config-field="db_type">
                        <option value="mysql" <%= 'selected' if @pipeline.source_config.dig('db_type') == 'mysql' %>>MySQL</option>
                        <option value="postgresql" <%= 'selected' if @pipeline.source_config.dig('db_type') == 'postgresql' %>>PostgreSQL</option>
                        <option value="sqlserver" <%= 'selected' if @pipeline.source_config.dig('db_type') == 'sqlserver' %>>SQL Server</option>
                        <option value="oracle" <%= 'selected' if @pipeline.source_config.dig('db_type') == 'oracle' %>>Oracle</option>
                      </select>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Host/Server</label>
                      <input type="text"
                             class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                             placeholder="localhost or server.company.com"
                             value="<%= @pipeline.source_config.dig('host') %>"
                             data-config-field="host">
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Database Name</label>
                      <input type="text"
                             class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                             placeholder="my_database"
                             value="<%= @pipeline.source_config.dig('database') %>"
                             data-config-field="database">
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Port (optional)</label>
                      <input type="text"
                             class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                             placeholder="3306"
                             value="<%= @pipeline.source_config.dig('port') %>"
                             data-config-field="port">
                    </div>
                  </div>
                </div>

                <!-- File Configuration -->
                <div class="source-config-form <%= current_source_type == 'file' ? 'block' : 'hidden' %>" data-source-type="file">
                  <h4 class="text-md font-medium text-blue-800 mb-3">File Source Details</h4>
                  <div class="space-y-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">File Type</label>
                      <select class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                              data-config-field="file_type">
                        <option value="csv" <%= 'selected' if @pipeline.source_config.dig('file_type') == 'csv' %>>CSV Files</option>
                        <option value="excel" <%= 'selected' if @pipeline.source_config.dig('file_type') == 'excel' %>>Excel Files</option>
                        <option value="json" <%= 'selected' if @pipeline.source_config.dig('file_type') == 'json' %>>JSON Files</option>
                        <option value="xml" <%= 'selected' if @pipeline.source_config.dig('file_type') == 'xml' %>>XML Files</option>
                      </select>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">File Location</label>
                      <input type="text"
                             class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                             placeholder="/path/to/files or https://example.com/data.csv"
                             value="<%= @pipeline.source_config.dig('file_path') %>"
                             data-config-field="file_path">
                    </div>
                  </div>
                </div>

                <!-- API Configuration -->
                <div class="source-config-form <%= current_source_type == 'api' ? 'block' : 'hidden' %>" data-source-type="api">
                  <h4 class="text-md font-medium text-blue-800 mb-3">API Connection Details</h4>
                  <div class="space-y-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">API URL</label>
                      <input type="url"
                             class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                             placeholder="https://api.example.com/data"
                             value="<%= @pipeline.source_config.dig('api_url') %>"
                             data-config-field="api_url">
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Authentication Type</label>
                      <select class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                              data-config-field="auth_type">
                        <option value="none" <%= 'selected' if @pipeline.source_config.dig('auth_type') == 'none' %>>No Authentication</option>
                        <option value="api_key" <%= 'selected' if @pipeline.source_config.dig('auth_type') == 'api_key' %>>API Key</option>
                        <option value="bearer" <%= 'selected' if @pipeline.source_config.dig('auth_type') == 'bearer' %>>Bearer Token</option>
                        <option value="basic" <%= 'selected' if @pipeline.source_config.dig('auth_type') == 'basic' %>>Basic Auth</option>
                      </select>
                    </div>
                  </div>
                </div>

                <!-- Other source types can be added here -->
              </div>

              <!-- Hidden field to store the JSON configuration -->
              <%= form.hidden_field :source_config,
                  value: @pipeline.source_config.is_a?(Hash) ? @pipeline.source_config.to_json : @pipeline.source_config,
                  data: { pipeline_wizard_target: "sourceConfigField" } %>
            </div>

            <!-- Data Destination Configuration -->
            <div class="bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-xl p-6">
              <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center mr-3">
                  <span class="text-white font-bold text-sm">2</span>
                </div>
                <h3 class="text-lg font-medium text-green-900">Where should your data go?</h3>
              </div>

              <!-- Data Destination Type Selection -->
              <div class="mb-6">
                <label class="block text-sm font-medium text-green-800 mb-3">Choose your destination:</label>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <% current_dest_type = @pipeline.destination_config.is_a?(Hash) ? @pipeline.destination_config['type'] : 'database' %>
                  <% [
                    ['database', 'Database', 'MySQL, PostgreSQL, SQL Server'],
                    ['file', 'Files', 'CSV, Excel, JSON export'],
                    ['cloud', 'Cloud Storage', 'AWS S3, Google Drive'],
                    ['warehouse', 'Data Warehouse', 'Snowflake, BigQuery'],
                    ['api', 'API/Webhook', 'Send to external API'],
                    ['other', 'Other', 'Custom destination']
                  ].each do |value, label, description| %>
                    <label class="data-dest-option relative flex flex-col p-4 border-2 rounded-lg cursor-pointer hover:bg-green-50 transition-colors duration-200 <%= current_dest_type == value ? 'border-green-500 bg-green-50' : 'border-green-200 bg-white' %>"
                           data-action="click->pipeline-wizard#selectDataDestType"
                           data-dest-type="<%= value %>">
                      <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-900"><%= label %></span>
                        <div class="w-4 h-4 border-2 <%= current_dest_type == value ? 'border-green-600' : 'border-gray-300' %> rounded-full flex items-center justify-center">
                          <div class="w-2 h-2 bg-green-600 rounded-full <%= current_dest_type == value ? 'block' : 'hidden' %>"></div>
                        </div>
                      </div>
                      <span class="text-xs text-gray-600"><%= description %></span>
                    </label>
                  <% end %>
                </div>
              </div>

              <!-- Dynamic Destination Configuration Forms -->
              <div id="dest-config-forms">
                <!-- Database Configuration -->
                <div class="dest-config-form <%= current_dest_type == 'database' ? 'block' : 'hidden' %>" data-dest-type="database">
                  <h4 class="text-md font-medium text-green-800 mb-3">Database Connection Details</h4>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Database Type</label>
                      <select class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-green-500 focus:border-green-500 sm:text-sm"
                              data-dest-config-field="db_type">
                        <option value="mysql" <%= 'selected' if @pipeline.destination_config.dig('db_type') == 'mysql' %>>MySQL</option>
                        <option value="postgresql" <%= 'selected' if @pipeline.destination_config.dig('db_type') == 'postgresql' %>>PostgreSQL</option>
                        <option value="sqlserver" <%= 'selected' if @pipeline.destination_config.dig('db_type') == 'sqlserver' %>>SQL Server</option>
                        <option value="oracle" <%= 'selected' if @pipeline.destination_config.dig('db_type') == 'oracle' %>>Oracle</option>
                      </select>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Host/Server</label>
                      <input type="text"
                             class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-green-500 focus:border-green-500 sm:text-sm"
                             placeholder="localhost or server.company.com"
                             value="<%= @pipeline.destination_config.dig('host') %>"
                             data-dest-config-field="host">
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Database Name</label>
                      <input type="text"
                             class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-green-500 focus:border-green-500 sm:text-sm"
                             placeholder="my_database"
                             value="<%= @pipeline.destination_config.dig('database') %>"
                             data-dest-config-field="database">
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Table Name</label>
                      <input type="text"
                             class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-green-500 focus:border-green-500 sm:text-sm"
                             placeholder="my_table"
                             value="<%= @pipeline.destination_config.dig('table') %>"
                             data-dest-config-field="table">
                    </div>
                  </div>
                </div>

                <!-- File Configuration -->
                <div class="dest-config-form <%= current_dest_type == 'file' ? 'block' : 'hidden' %>" data-dest-type="file">
                  <h4 class="text-md font-medium text-green-800 mb-3">File Export Details</h4>
                  <div class="space-y-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Export Format</label>
                      <select class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-green-500 focus:border-green-500 sm:text-sm"
                              data-dest-config-field="file_type">
                        <option value="csv" <%= 'selected' if @pipeline.destination_config.dig('file_type') == 'csv' %>>CSV Files</option>
                        <option value="excel" <%= 'selected' if @pipeline.destination_config.dig('file_type') == 'excel' %>>Excel Files</option>
                        <option value="json" <%= 'selected' if @pipeline.destination_config.dig('file_type') == 'json' %>>JSON Files</option>
                        <option value="parquet" <%= 'selected' if @pipeline.destination_config.dig('file_type') == 'parquet' %>>Parquet Files</option>
                      </select>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Output Location</label>
                      <input type="text"
                             class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-green-500 focus:border-green-500 sm:text-sm"
                             placeholder="/path/to/output or s3://bucket/path"
                             value="<%= @pipeline.destination_config.dig('file_path') %>"
                             data-dest-config-field="file_path">
                    </div>
                  </div>
                </div>

                <!-- Cloud Storage Configuration -->
                <div class="dest-config-form <%= current_dest_type == 'cloud' ? 'block' : 'hidden' %>" data-dest-type="cloud">
                  <h4 class="text-md font-medium text-green-800 mb-3">Cloud Storage Details</h4>
                  <div class="space-y-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Cloud Provider</label>
                      <select class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-green-500 focus:border-green-500 sm:text-sm"
                              data-dest-config-field="cloud_provider">
                        <option value="aws_s3" <%= 'selected' if @pipeline.destination_config.dig('cloud_provider') == 'aws_s3' %>>AWS S3</option>
                        <option value="google_drive" <%= 'selected' if @pipeline.destination_config.dig('cloud_provider') == 'google_drive' %>>Google Drive</option>
                        <option value="azure_blob" <%= 'selected' if @pipeline.destination_config.dig('cloud_provider') == 'azure_blob' %>>Azure Blob Storage</option>
                        <option value="dropbox" <%= 'selected' if @pipeline.destination_config.dig('cloud_provider') == 'dropbox' %>>Dropbox</option>
                      </select>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Bucket/Folder Path</label>
                      <input type="text"
                             class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-green-500 focus:border-green-500 sm:text-sm"
                             placeholder="my-bucket/data-exports"
                             value="<%= @pipeline.destination_config.dig('bucket_path') %>"
                             data-dest-config-field="bucket_path">
                    </div>
                  </div>
                </div>
              </div>

              <!-- Hidden field to store the JSON configuration -->
              <%= form.hidden_field :destination_config,
                  value: @pipeline.destination_config.is_a?(Hash) ? @pipeline.destination_config.to_json : @pipeline.destination_config,
                  data: { pipeline_wizard_target: "destConfigField" } %>
            </div>

            <!-- Optional Data Transformations -->
            <div class="bg-gradient-to-r from-purple-50 to-purple-100 border border-purple-200 rounded-xl p-6">
              <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center mr-3">
                  <span class="text-white font-bold text-sm">3</span>
                </div>
                <h3 class="text-lg font-medium text-purple-900">Do you need to transform your data? <span class="text-sm font-normal text-purple-700">(Optional)</span></h3>
              </div>

              <div class="space-y-4">
                <div class="flex items-center">
                  <input type="checkbox"
                         id="enable_transformations"
                         class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                         data-action="change->pipeline-wizard#toggleTransformations"
                         <%= 'checked' if @pipeline.transformation_rules.present? && @pipeline.transformation_rules != {} %>>
                  <label for="enable_transformations" class="ml-2 block text-sm text-purple-800">
                    Yes, I need to transform or modify my data during the pipeline
                  </label>
                </div>

                <div id="transformation-options" class="<%= @pipeline.transformation_rules.present? && @pipeline.transformation_rules != {} ? 'block' : 'hidden' %> space-y-4 mt-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Common Transformations</label>
                      <div class="space-y-2">
                        <label class="flex items-center">
                          <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded" data-transform="rename_fields">
                          <span class="ml-2 text-sm text-gray-700">Rename fields/columns</span>
                        </label>
                        <label class="flex items-center">
                          <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded" data-transform="filter_data">
                          <span class="ml-2 text-sm text-gray-700">Filter/exclude certain data</span>
                        </label>
                        <label class="flex items-center">
                          <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded" data-transform="format_dates">
                          <span class="ml-2 text-sm text-gray-700">Format dates and times</span>
                        </label>
                        <label class="flex items-center">
                          <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded" data-transform="aggregate_data">
                          <span class="ml-2 text-sm text-gray-700">Aggregate/summarize data</span>
                        </label>
                      </div>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Additional Notes</label>
                      <textarea rows="4"
                                class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                                placeholder="Describe any specific transformations you need..."
                                data-transform-field="notes"><%= @pipeline.transformation_rules.dig('notes') if @pipeline.transformation_rules.is_a?(Hash) %></textarea>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Hidden field to store the JSON configuration -->
              <%= form.hidden_field :transformation_rules,
                  value: @pipeline.transformation_rules.is_a?(Hash) ? @pipeline.transformation_rules.to_json : (@pipeline.transformation_rules || '{}'),
                  data: { pipeline_wizard_target: "transformConfigField" } %>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 5: Review & Save -->
      <div class="pipeline-wizard-step fade-in" data-pipeline-wizard-target="step" data-step="5">
        <div class="p-8">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Review & Save Changes</h2>
            <p class="text-gray-600">Review your pipeline configuration and save changes</p>
          </div>

          <div class="max-w-4xl mx-auto space-y-8">
            <!-- Pipeline Summary -->
            <div class="bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-200 rounded-xl p-6">
              <h3 class="text-lg font-medium text-indigo-900 mb-4">Pipeline Summary</h3>
              <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <dt class="text-sm font-medium text-indigo-700">Name</dt>
                  <dd class="text-sm text-indigo-900" data-pipeline-wizard-target="summaryName"><%= @pipeline.name %></dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-indigo-700">Status</dt>
                  <dd class="text-sm text-indigo-900" data-pipeline-wizard-target="summaryStatus"><%= @pipeline.status.humanize %></dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-indigo-700">Schedule</dt>
                  <dd class="text-sm text-indigo-900" data-pipeline-wizard-target="summarySchedule"><%= @pipeline.schedule_type.humanize %></dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-indigo-700">Template</dt>
                  <dd class="text-sm text-indigo-900"><%= @pipeline.template_type&.humanize || 'Custom' %></dd>
                </div>
              </dl>
            </div>

            <!-- Configuration Preview -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div class="bg-white border border-gray-200 rounded-xl p-6">
                <h4 class="text-md font-medium text-gray-900 mb-3">Source Configuration</h4>
                <pre class="text-xs text-gray-600 bg-gray-50 p-3 rounded overflow-auto max-h-32"><%= @pipeline.source_config.is_a?(Hash) ? JSON.pretty_generate(@pipeline.source_config) : @pipeline.source_config %></pre>
              </div>
              <div class="bg-white border border-gray-200 rounded-xl p-6">
                <h4 class="text-md font-medium text-gray-900 mb-3">Destination Configuration</h4>
                <pre class="text-xs text-gray-600 bg-gray-50 p-3 rounded overflow-auto max-h-32"><%= @pipeline.destination_config.is_a?(Hash) ? JSON.pretty_generate(@pipeline.destination_config) : @pipeline.destination_config %></pre>
              </div>
            </div>

            <!-- Save Confirmation -->
            <div class="bg-green-50 border border-green-200 rounded-xl p-6">
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <svg class="h-6 w-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-lg font-medium text-green-900 mb-2">Ready to Save Changes</h3>
                  <div class="text-sm text-green-800 space-y-1">
                    <p>✓ Pipeline configuration has been updated</p>
                    <p>✓ All required fields are completed</p>
                    <p>✓ JSON configurations are valid</p>
                    <p class="font-medium">Click "Update Pipeline" to save your changes.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div class="wizard-navigation px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
        <!-- Left side - Previous button -->
        <div>
          <button type="button" 
                  class="wizard-prev-btn inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  data-action="click->pipeline-wizard#previousStep"
                  data-pipeline-wizard-target="prevButton"
                  disabled>
            <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            Previous
          </button>
        </div>

        <!-- Center - Step indicators -->
        <div class="flex items-center space-x-2">
          <div class="step-indicator w-3 h-3 rounded-full bg-indigo-600" data-pipeline-wizard-target="stepIndicator" data-step="1"></div>
          <div class="w-6 h-0.5 bg-gray-300"></div>
          <div class="step-indicator w-3 h-3 rounded-full bg-gray-300" data-pipeline-wizard-target="stepIndicator" data-step="2"></div>
          <div class="w-6 h-0.5 bg-gray-300"></div>
          <div class="step-indicator w-3 h-3 rounded-full bg-gray-300" data-pipeline-wizard-target="stepIndicator" data-step="3"></div>
          <div class="w-6 h-0.5 bg-gray-300"></div>
          <div class="step-indicator w-3 h-3 rounded-full bg-gray-300" data-pipeline-wizard-target="stepIndicator" data-step="4"></div>
          <div class="w-6 h-0.5 bg-gray-300"></div>
          <div class="step-indicator w-3 h-3 rounded-full bg-gray-300" data-pipeline-wizard-target="stepIndicator" data-step="5"></div>
        </div>

        <!-- Right side - Next/Submit button -->
        <div class="flex items-center space-x-3">
          <!-- Cancel button (always visible) -->
          <%= link_to @pipeline,
              class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200" do %>
            Cancel
          <% end %>
          
          <!-- Next button (steps 1-3) -->
          <button type="button" 
                  class="wizard-next-btn inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  data-action="click->pipeline-wizard#nextStep"
                  data-pipeline-wizard-target="nextButton">
            Next
            <svg class="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
          </button>

          <!-- Submit button (step 4 only) -->
          <%= form.submit "Update Pipeline", 
              class: "wizard-submit-btn hidden inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200",
              data: { pipeline_wizard_target: "submitButton" } %>
        </div>
      </div>
    <% end %>
  </div>
</div>
