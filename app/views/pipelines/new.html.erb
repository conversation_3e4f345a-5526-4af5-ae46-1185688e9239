<% content_for :page_title, "Create New Pipeline" %>

<!-- Enhanced Styles for Pipeline Form -->
<style>
  .pipeline-wizard-step { display: none; }
  .pipeline-wizard-step.active { display: block; }
  .pipeline-step-indicator.completed { background-color: #10b981; color: white; }
  .pipeline-step-indicator.active { background-color: #3b82f6; color: white; }
  .pipeline-step-indicator { background-color: #e5e7eb; color: #6b7280; }
  .pipeline-template-card { transition: all 0.2s ease; }
  .pipeline-template-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
  .pipeline-template-card.selected { border-color: #3b82f6; background-color: #eff6ff; }
  .schedule-option { transition: all 0.2s ease; }
  .schedule-option:hover { background-color: #f9fafb; }
  .schedule-option.selected { border-color: #3b82f6; background-color: #eff6ff; }
  .data-flow-connector { transition: all 0.3s ease; }
  .data-flow-connector:hover { transform: scale(1.05); }
  .json-editor { font-family: 'Monaco', 'Men<PERSON>', 'Ubuntu Mono', monospace; }
  .pipeline-preview { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
</style>

<div class="max-w-7xl mx-auto" data-controller="pipeline-wizard" data-pipeline-wizard-current-step-value="1">
  <!-- Enhanced Header with Progress -->
  <div class="mb-8">
    <!-- Breadcrumb Navigation -->
    <nav class="flex mb-4" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        <li class="inline-flex items-center">
          <%= link_to pipelines_path, class: "inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600" do %>
            <svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
            </svg>
            Pipelines
          <% end %>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Create Pipeline</span>
          </div>
        </li>
      </ol>
    </nav>

    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Create New Pipeline</h1>
        <p class="mt-2 text-lg text-gray-600">
          Build powerful data processing workflows with our intuitive pipeline builder
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <!-- Pipeline Status Indicator -->
        <div class="hidden" data-pipeline-wizard-target="statusIndicator">
          <div class="flex items-center text-sm text-green-600">
            <svg class="mr-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
            Pipeline validated
          </div>
        </div>
        <%= link_to pipelines_path,
            class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200" do %>
          <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          Back to Pipelines
        <% end %>
      </div>
    </div>
  </div>

  <!-- Enhanced Progress Indicator -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <div class="flex items-center">
          <div class="pipeline-step-indicator active flex items-center justify-center w-10 h-10 rounded-full text-sm font-medium transition-all duration-300" data-pipeline-wizard-target="stepIndicator" data-step="1">
            1
          </div>
          <span class="ml-3 text-sm font-medium text-gray-900">Template & Type</span>
        </div>
        <div class="flex-1 h-1 bg-gray-200 mx-4 rounded-full">
          <div class="h-1 bg-indigo-600 rounded-full transition-all duration-500" style="width: 25%;" data-pipeline-wizard-target="progressBar"></div>
        </div>
        <div class="flex items-center">
          <div class="pipeline-step-indicator flex items-center justify-center w-10 h-10 rounded-full text-sm font-medium transition-all duration-300" data-pipeline-wizard-target="stepIndicator" data-step="2">
            2
          </div>
          <span class="ml-3 text-sm font-medium text-gray-500">Basic Setup</span>
        </div>
        <div class="flex-1 h-1 bg-gray-200 mx-4 rounded-full"></div>
        <div class="flex items-center">
          <div class="pipeline-step-indicator flex items-center justify-center w-10 h-10 rounded-full text-sm font-medium transition-all duration-300" data-pipeline-wizard-target="stepIndicator" data-step="3">
            3
          </div>
          <span class="ml-3 text-sm font-medium text-gray-500">Data Flow</span>
        </div>
        <div class="flex-1 h-1 bg-gray-200 mx-4 rounded-full"></div>
        <div class="flex items-center">
          <div class="pipeline-step-indicator flex items-center justify-center w-10 h-10 rounded-full text-sm font-medium transition-all duration-300" data-pipeline-wizard-target="stepIndicator" data-step="4">
            4
          </div>
          <span class="ml-3 text-sm font-medium text-gray-500">Review & Deploy</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Pipeline Creation Form with Wizard -->
  <div class="bg-white shadow-xl rounded-xl border border-gray-200">
    <%= form_with model: @pipeline,
        local: true,
        class: "pipeline-form",
        data: {
          controller: "pipeline-validation auto-save",
          pipeline_validation_target: "form",
          auto_save_url: "#",
          auto_save_interval: 30000
        } do |form| %>

      <!-- Step 1: Template & Type Selection -->
      <div class="pipeline-wizard-step active fade-in" data-pipeline-wizard-target="step" data-step="1">
        <div class="p-8">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Choose Pipeline Template</h2>
            <p class="text-gray-600">Start with a pre-built template or create a custom pipeline from scratch</p>
          </div>

          <!-- Pipeline Template Cards -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- ETL Template -->
            <div class="pipeline-template-card border-2 border-gray-200 rounded-xl p-6 cursor-pointer hover:border-indigo-300 transition-all duration-200"
                 data-action="click->pipeline-wizard#selectTemplate"
                 data-template="etl">
              <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">ETL Pipeline</h3>
                <p class="text-sm text-gray-600 mb-4">Extract, Transform, Load data between systems</p>
                <div class="flex flex-wrap gap-1 justify-center">
                  <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">Extract</span>
                  <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">Transform</span>
                  <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded">Load</span>
                </div>
              </div>
            </div>

            <!-- Data Sync Template -->
            <div class="pipeline-template-card border-2 border-gray-200 rounded-xl p-6 cursor-pointer hover:border-indigo-300 transition-all duration-200"
                 data-action="click->pipeline-wizard#selectTemplate"
                 data-template="sync">
              <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"/>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Data Sync</h3>
                <p class="text-sm text-gray-600 mb-4">Keep data synchronized between systems</p>
                <div class="flex flex-wrap gap-1 justify-center">
                  <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">Real-time</span>
                  <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">Bi-directional</span>
                  <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded">Incremental</span>
                </div>
              </div>
            </div>

            <!-- Stream Processing Template -->
            <div class="pipeline-template-card border-2 border-gray-200 rounded-xl p-6 cursor-pointer hover:border-indigo-300 transition-all duration-200"
                 data-action="click->pipeline-wizard#selectTemplate"
                 data-template="stream">
              <div class="text-center">
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Stream Processing</h3>
                <p class="text-sm text-gray-600 mb-4">Process data streams in real-time</p>
                <div class="flex flex-wrap gap-1 justify-center">
                  <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded">Real-time</span>
                  <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded">Event-driven</span>
                  <span class="px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded">Scalable</span>
                </div>
              </div>
            </div>

            <!-- Batch Processing Template -->
            <div class="pipeline-template-card border-2 border-gray-200 rounded-xl p-6 cursor-pointer hover:border-indigo-300 transition-all duration-200"
                 data-action="click->pipeline-wizard#selectTemplate"
                 data-template="batch">
              <div class="text-center">
                <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Batch Processing</h3>
                <p class="text-sm text-gray-600 mb-4">Process large datasets in scheduled batches</p>
                <div class="flex flex-wrap gap-1 justify-center">
                  <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded">Scheduled</span>
                  <span class="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded">High-volume</span>
                  <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">Efficient</span>
                </div>
              </div>
            </div>

            <!-- Data Migration Template -->
            <div class="pipeline-template-card border-2 border-gray-200 rounded-xl p-6 cursor-pointer hover:border-indigo-300 transition-all duration-200"
                 data-action="click->pipeline-wizard#selectTemplate"
                 data-template="migration">
              <div class="text-center">
                <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Data Migration</h3>
                <p class="text-sm text-gray-600 mb-4">Migrate data between different systems</p>
                <div class="flex flex-wrap gap-1 justify-center">
                  <span class="px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded">One-time</span>
                  <span class="px-2 py-1 bg-teal-100 text-teal-800 text-xs rounded">Validation</span>
                  <span class="px-2 py-1 bg-pink-100 text-pink-800 text-xs rounded">Mapping</span>
                </div>
              </div>
            </div>

            <!-- Custom Template -->
            <div class="pipeline-template-card border-2 border-gray-200 rounded-xl p-6 cursor-pointer hover:border-indigo-300 transition-all duration-200"
                 data-action="click->pipeline-wizard#selectTemplate"
                 data-template="custom">
              <div class="text-center">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Custom Pipeline</h3>
                <p class="text-sm text-gray-600 mb-4">Build a custom pipeline from scratch</p>
                <div class="flex flex-wrap gap-1 justify-center">
                  <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">Flexible</span>
                  <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">Custom</span>
                  <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">Advanced</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Template Description -->
          <div class="bg-gray-50 rounded-xl p-6" data-pipeline-wizard-target="templateDescription">
            <div class="text-center text-gray-500">
              <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <p class="text-sm">Select a template above to see detailed information and configuration options</p>
            </div>
          </div>

          <!-- Hidden field to store selected template -->
          <%= form.hidden_field :pipeline_template, data: { pipeline_wizard_target: "templateField" } %>
        </div>
      </div>

      <!-- Step 2: Basic Setup -->
      <div class="pipeline-wizard-step fade-in" data-pipeline-wizard-target="step" data-step="2">
        <div class="p-8">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Basic Pipeline Setup</h2>
            <p class="text-gray-600">Configure the basic settings for your pipeline</p>
          </div>

          <div class="max-w-3xl mx-auto space-y-8">
            <!-- Pipeline Name and Description -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Pipeline Name -->
              <div class="md:col-span-2">
                <%= form.label :name, class: "block text-sm font-medium text-gray-700 mb-2" do %>
                  Pipeline Name
                  <span class="text-red-500">*</span>
                  <button type="button" class="ml-1 text-gray-400 hover:text-gray-600" data-tooltip="Choose a descriptive name for your pipeline">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                    </svg>
                  </button>
                <% end %>
                <div class="relative">
                  <%= form.text_field :name,
                      class: "block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors duration-200",
                      placeholder: "e.g., Customer Data ETL Pipeline",
                      required: true,
                      data: {
                        action: "input->pipeline-validation#validateField blur->auto-save#saveField",
                        pipeline_validation_target: "field",
                        validation_rules: "required|min:3|max:100"
                      } %>
                  <!-- Real-time validation feedback -->
                  <div class="hidden mt-1 text-sm text-red-600" data-pipeline-validation-target="error" data-field="name">
                    <svg class="inline w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                    <span data-pipeline-validation-target="errorMessage"></span>
                  </div>
                  <div class="hidden mt-1 text-sm text-green-600" data-pipeline-validation-target="success" data-field="name">
                    <svg class="inline w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    Looks good!
                  </div>
                </div>
                <!-- Smart suggestions based on template -->
                <div class="hidden mt-2" data-pipeline-wizard-target="nameSuggestions">
                  <p class="text-xs text-gray-500 mb-2">Suggested names:</p>
                  <div class="flex flex-wrap gap-2">
                    <!-- Suggestions will be populated by JavaScript -->
                  </div>
                </div>
              </div>

              <!-- Pipeline Description -->
              <div class="md:col-span-2">
                <%= form.label :description, class: "block text-sm font-medium text-gray-700 mb-2" do %>
                  Description
                  <span class="text-gray-400">(Optional)</span>
                <% end %>
                <div class="relative">
                  <%= form.text_area :description,
                      rows: 4,
                      maxlength: 500,
                      class: "block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors duration-200",
                      placeholder: "Describe what this pipeline does and its purpose...",
                      data: {
                        action: "input->pipeline-validation#updateCharacterCount input->auto-save#saveField",
                        pipeline_validation_target: "field"
                      } %>
                  <div class="absolute bottom-2 right-2 text-xs text-gray-400" data-pipeline-validation-target="characterCount" data-max="500">
                    0/500
                  </div>
                </div>
              </div>
            </div>

            <!-- Schedule Configuration -->
            <div class="bg-gray-50 rounded-xl p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Schedule Configuration</h3>

              <!-- Schedule Type Selection -->
              <div class="space-y-4">
                <%= form.label :schedule_type, class: "block text-sm font-medium text-gray-700 mb-3" do %>
                  How often should this pipeline run?
                  <span class="text-red-500">*</span>
                <% end %>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <!-- Manual -->
                  <label class="schedule-option relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-all duration-200">
                    <%= form.radio_button :schedule_type, "manual",
                        class: "sr-only",
                        data: { action: "change->pipeline-validation#validateField" } %>
                    <div class="flex items-center space-x-3">
                      <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-900">Manual</div>
                        <div class="text-xs text-gray-500">Run on demand</div>
                      </div>
                    </div>
                  </label>

                  <!-- Real-time -->
                  <label class="schedule-option relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-all duration-200">
                    <%= form.radio_button :schedule_type, "real_time",
                        class: "sr-only",
                        data: { action: "change->pipeline-validation#validateField" } %>
                    <div class="flex items-center space-x-3">
                      <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-900">Real-time</div>
                        <div class="text-xs text-gray-500">Continuous processing</div>
                      </div>
                    </div>
                  </label>

                  <!-- Hourly -->
                  <label class="schedule-option relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-all duration-200">
                    <%= form.radio_button :schedule_type, "hourly",
                        class: "sr-only",
                        data: { action: "change->pipeline-validation#validateField" } %>
                    <div class="flex items-center space-x-3">
                      <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-900">Hourly</div>
                        <div class="text-xs text-gray-500">Every hour</div>
                      </div>
                    </div>
                  </label>

                  <!-- Daily -->
                  <label class="schedule-option relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-all duration-200">
                    <%= form.radio_button :schedule_type, "daily",
                        class: "sr-only",
                        data: { action: "change->pipeline-validation#validateField" } %>
                    <div class="flex items-center space-x-3">
                      <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
                        </svg>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-900">Daily</div>
                        <div class="text-xs text-gray-500">Once per day</div>
                      </div>
                    </div>
                  </label>

                  <!-- Weekly -->
                  <label class="schedule-option relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-all duration-200">
                    <%= form.radio_button :schedule_type, "weekly",
                        class: "sr-only",
                        data: { action: "change->pipeline-validation#validateField" } %>
                    <div class="flex items-center space-x-3">
                      <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-900">Weekly</div>
                        <div class="text-xs text-gray-500">Once per week</div>
                      </div>
                    </div>
                  </label>

                  <!-- Custom (Cron) -->
                  <label class="schedule-option relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-all duration-200">
                    <%= form.radio_button :schedule_type, "cron",
                        class: "sr-only",
                        data: { action: "change->pipeline-validation#validateField" } %>
                    <div class="flex items-center space-x-3">
                      <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-900">Custom</div>
                        <div class="text-xs text-gray-500">Cron expression</div>
                      </div>
                    </div>
                  </label>
                </div>

                <!-- Custom Schedule Configuration -->
                <div class="hidden mt-4" data-pipeline-wizard-target="cronConfig">
                  <%= form.label :schedule_config, class: "block text-sm font-medium text-gray-700 mb-2" do %>
                    Cron Expression
                    <span class="text-red-500">*</span>
                  <% end %>
                  <div class="relative">
                    <%= form.text_area :schedule_config,
                        rows: 2,
                        class: "block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm font-mono",
                        placeholder: '{"cron": "0 0 * * *"}',
                        data: { validation_rules: "required|json" } %>
                    <div class="mt-2 text-xs text-gray-500">
                      <p>Examples:</p>
                      <ul class="list-disc list-inside space-y-1 mt-1">
                        <li><code class="bg-gray-100 px-1 rounded">{"cron": "0 0 * * *"}</code> - Daily at midnight</li>
                        <li><code class="bg-gray-100 px-1 rounded">{"cron": "0 */6 * * *"}</code> - Every 6 hours</li>
                        <li><code class="bg-gray-100 px-1 rounded">{"cron": "0 0 * * 1"}</code> - Weekly on Monday</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Pipeline Status -->
            <div class="bg-gray-50 rounded-xl p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Initial Status</h3>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Draft -->
                <label class="relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-all duration-200">
                  <%= form.radio_button :status, "draft",
                      class: "sr-only",
                      checked: true,
                      data: { action: "change->pipeline-validation#validateField" } %>
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                      <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                      </svg>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-900">Draft</div>
                      <div class="text-xs text-gray-500">Save for later</div>
                    </div>
                  </div>
                </label>

                <!-- Active -->
                <label class="relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-all duration-200">
                  <%= form.radio_button :status, "active",
                      class: "sr-only",
                      data: { action: "change->pipeline-validation#validateField" } %>
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"/>
                      </svg>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-900">Active</div>
                      <div class="text-xs text-gray-500">Ready to run</div>
                    </div>
                  </div>
                </label>

                <!-- Paused -->
                <label class="relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-all duration-200">
                  <%= form.radio_button :status, "paused",
                      class: "sr-only",
                      data: { action: "change->pipeline-validation#validateField" } %>
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                      <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-900">Paused</div>
                      <div class="text-xs text-gray-500">Inactive</div>
                    </div>
                  </div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

          <!-- Data Sources & Destinations -->
          <div class="border-t border-gray-200 pt-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Data Configuration</h3>

            <div class="grid grid-cols-1 gap-6">
              <!-- Source Configuration -->
              <div>
                <%= form.label :source_config, class: "block text-sm font-medium text-gray-700" do %>
                  Source Configuration
                  <span class="text-red-500">*</span>
                <% end %>
                <%= form.text_area :source_config,
                    rows: 4,
                    class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm font-mono",
                    placeholder: '{"type": "database", "host": "localhost", "database": "source_db"}',
                    required: true %>
                <p class="mt-2 text-sm text-gray-500">
                  JSON configuration for the data source.
                  <%= link_to "View examples", "#", class: "text-indigo-600 hover:text-indigo-500" %>
                </p>
              </div>

              <!-- Destination Configuration -->
              <div>
                <%= form.label :destination_config, class: "block text-sm font-medium text-gray-700" do %>
                  Destination Configuration
                  <span class="text-red-500">*</span>
                <% end %>
                <%= form.text_area :destination_config,
                    rows: 4,
                    class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm font-mono",
                    placeholder: '{"type": "database", "host": "localhost", "database": "dest_db"}',
                    required: true %>
                <p class="mt-2 text-sm text-gray-500">
                  JSON configuration for the data destination.
                </p>
              </div>
            </div>
          </div>

          <!-- Advanced Settings -->
          <div class="border-t border-gray-200 pt-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Advanced Settings</h3>

            <div class="space-y-4">
              <!-- Schedule Config (for cron expressions) -->
              <div id="schedule-config" style="display: none;">
                <%= form.label :schedule_config, class: "block text-sm font-medium text-gray-700" do %>
                  Schedule Configuration
                <% end %>
                <%= form.text_area :schedule_config,
                    rows: 2,
                    class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm font-mono",
                    placeholder: '{"cron": "0 0 * * *"}' %>
                <p class="mt-2 text-sm text-gray-500">
                  JSON configuration for custom scheduling (required when schedule type is 'Custom')
                </p>
              </div>

              <!-- Transformation Rules -->
              <div>
                <%= form.label :transformation_rules, class: "block text-sm font-medium text-gray-700" do %>
                  Transformation Rules
                <% end %>
                <%= form.text_area :transformation_rules,
                    rows: 3,
                    class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm font-mono",
                    placeholder: '{"rules": []}' %>
                <p class="mt-2 text-sm text-gray-500">
                  Optional JSON configuration for data transformations
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
        <div class="text-sm text-gray-500">
          <span class="text-red-500">*</span> Required fields
        </div>
        <div class="flex items-center space-x-3">
          <%= link_to pipelines_path,
              class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            Cancel
          <% end %>
          <%= form.submit "Create Pipeline",
              class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Help Section -->
  <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
        </svg>
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium text-blue-800">Need help getting started?</h3>
        <div class="mt-2 text-sm text-blue-700">
          <p>
            Pipelines help you automate data movement and transformation between your systems. 
            Start by selecting your source and destination connections, then configure how often the pipeline should run.
          </p>
          <div class="mt-3">
            <a href="#" class="font-medium text-blue-800 hover:text-blue-600">
              View pipeline documentation →
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const scheduleTypeSelect = document.getElementById('pipeline_schedule_type');
    const scheduleConfigDiv = document.getElementById('schedule-config');

    if (scheduleTypeSelect && scheduleConfigDiv) {
      scheduleTypeSelect.addEventListener('change', function() {
        if (this.value === 'cron') {
          scheduleConfigDiv.style.display = 'block';
        } else {
          scheduleConfigDiv.style.display = 'none';
        }
      });
    }
  });
</script>
