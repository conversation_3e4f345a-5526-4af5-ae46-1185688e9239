<% content_for :page_title, "Pipelines" %>

<div class="max-w-7xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Data Pipelines</h1>
        <p class="mt-1 text-sm text-gray-500">
          Create and manage your data processing pipelines
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <%= link_to new_pipeline_path,
            class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
          <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
          </svg>
          Create Pipeline
        <% end %>
      </div>
    </div>
  </div>

  <!-- Pipelines List -->
  <div class="bg-white shadow overflow-hidden sm:rounded-md">
    <% if @pipelines&.any? %>
      <ul class="divide-y divide-gray-200">
        <% @pipelines.each do |pipeline| %>
          <li class="hover:bg-gray-50">
            <div class="px-4 py-4 flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="h-10 w-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                    <svg class="h-6 w-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <div class="flex items-center">
                    <div class="text-sm font-medium text-gray-900">
                      <%= pipeline.name %>
                    </div>
                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= pipeline.respond_to?(:status) && pipeline.status == 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %>">
                      <%= pipeline.respond_to?(:status) ? pipeline.status.humanize : 'Active' %>
                    </span>
                  </div>
                  <div class="text-sm text-gray-500">
                    <%= pipeline.description.present? ? truncate(pipeline.description, length: 100) : 'No description' %> • 
                    Last updated <%= time_ago_in_words(pipeline.updated_at) %> ago
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <% if pipeline.respond_to?(:status) && pipeline.status == 'active' %>
                  <%= link_to "#",
                      class: "inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200" do %>
                    <svg class="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                    </svg>
                    Run
                  <% end %>
                <% end %>
                <%= link_to pipeline_path(pipeline),
                    class: "text-indigo-600 hover:text-indigo-900 text-sm font-medium" do %>
                  View
                <% end %>
                <%= link_to edit_pipeline_path(pipeline),
                    class: "text-gray-600 hover:text-gray-900 text-sm font-medium" do %>
                  Edit
                <% end %>
              </div>
            </div>
          </li>
        <% end %>
      </ul>
    <% else %>
      <!-- Empty State -->
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No pipelines</h3>
        <p class="mt-1 text-sm text-gray-500">
          Get started by creating your first data pipeline.
        </p>
        <div class="mt-6">
          <%= link_to new_pipeline_path,
              class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
            </svg>
            Create Pipeline
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>
