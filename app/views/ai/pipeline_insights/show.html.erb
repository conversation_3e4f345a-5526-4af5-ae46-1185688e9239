<%# AI-Powered Pipeline Revenue Assessment %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">💡 AI Revenue Assessment</h1>
          <p class="mt-1 text-sm text-gray-600">
            AI-powered analysis of <strong><%= @pipeline.name %></strong>'s monetization potential
          </p>
        </div>
        <div class="flex space-x-3">
          <%= link_to pipeline_path(@pipeline), class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" do %>
            ← Back to Pipeline
          <% end %>
        </div>
      </div>
    </div>

    <div class="p-6">
      <!-- Overall Assessment Card -->
      <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white mb-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="text-center">
            <div class="text-3xl font-bold"><%= @assessment[:overall_score] %></div>
            <div class="text-sm opacity-90">Revenue Score</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold">$<%= number_with_delimiter(@assessment[:monthly_revenue_estimate]) %></div>
            <div class="text-sm opacity-90">Est. Monthly Revenue</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold capitalize"><%= @assessment[:opportunity_level] %></div>
            <div class="text-sm opacity-90">Opportunity Level</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-bold capitalize"><%= @assessment[:market_category].humanize %></div>
            <div class="text-sm opacity-90">Market Category</div>
          </div>
        </div>
      </div>

      <!-- Monetization Opportunities -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div>
          <h2 class="text-xl font-bold text-gray-900 mb-4">🚀 Monetization Opportunities</h2>
          <div class="space-y-4">
            <% @opportunities.each_with_index do |opportunity, index| %>
              <div class="border border-gray-200 rounded-lg p-4 <%= 'border-green-300 bg-green-50' if index == 0 %>">
                <div class="flex items-center justify-between mb-2">
                  <h3 class="font-semibold text-gray-900 capitalize">
                    <%= opportunity[:type].humanize %>
                    <% if index == 0 %>
                      <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Best Fit
                      </span>
                    <% end %>
                  </h3>
                  <span class="text-sm font-medium text-gray-600">
                    $<%= number_with_delimiter(opportunity[:revenue_potential]) %>/mo
                  </span>
                </div>
                <p class="text-sm text-gray-600 mb-2"><%= opportunity[:description] %></p>
                <div class="flex items-center justify-between">
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                    <%= case opportunity[:implementation_effort]
                        when 'low' then 'bg-green-100 text-green-800'
                        when 'medium' then 'bg-yellow-100 text-yellow-800'  
                        when 'high' then 'bg-red-100 text-red-800'
                        end %>">
                    <%= opportunity[:implementation_effort].capitalize %> effort
                  </span>
                  <button class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                    View Details →
                  </button>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Market Insights -->
        <div>
          <h2 class="text-xl font-bold text-gray-900 mb-4">📊 Market Analysis</h2>
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div>
                <div class="text-sm text-gray-600">Market Demand</div>
                <div class="flex items-center">
                  <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                    <div class="bg-blue-600 h-2 rounded-full" style="width: <%= (@market_insights[:market_demand] * 100).round %>%"></div>
                  </div>
                  <span class="text-sm font-medium"><%= (@market_insights[:market_demand] * 100).round %>%</span>
                </div>
              </div>
              <div>
                <div class="text-sm text-gray-600">Avg Market Price</div>
                <div class="text-lg font-bold">$<%= @market_insights[:average_market_price] %>/1K records</div>
              </div>
            </div>

            <div class="mb-4">
              <div class="text-sm text-gray-600 mb-2">Target Customers</div>
              <div class="flex flex-wrap gap-2">
                <% @market_insights[:target_customers].each do |customer| %>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <%= customer %>
                  </span>
                <% end %>
              </div>
            </div>

            <div class="mb-4">
              <div class="text-sm text-gray-600 mb-2">Value Propositions</div>
              <ul class="space-y-1">
                <% @market_insights[:value_propositions].each do |proposition| %>
                  <li class="text-sm text-gray-700 flex items-center">
                    <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <%= proposition %>
                  </li>
                <% end %>
              </ul>
            </div>

            <div class="border-t pt-3">
              <div class="text-sm text-gray-600">Competition Level</div>
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium capitalize"><%= @market_insights[:competitive_analysis][:level] %></span>
                <span class="text-sm text-gray-500"><%= @market_insights[:growth_trends][:growth_rate] %> growth</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Data Insights -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div>
          <h2 class="text-xl font-bold text-gray-900 mb-4">📈 Data Characteristics</h2>
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <div class="text-sm text-gray-600">Average Volume</div>
                <div class="text-lg font-bold"><%= number_with_delimiter(@assessment[:data_insights][:average_volume]) %> records</div>
              </div>
              <div>
                <div class="text-sm text-gray-600">Success Rate</div>
                <div class="text-lg font-bold"><%= @assessment[:data_insights][:success_rate] %>%</div>
              </div>
              <div>
                <div class="text-sm text-gray-600">Execution Frequency</div>
                <div class="text-lg font-bold capitalize"><%= @assessment[:data_insights][:execution_frequency] %></div>
              </div>
              <div>
                <div class="text-sm text-gray-600">Data Consistency</div>
                <div class="text-lg font-bold"><%= (@assessment[:data_insights][:data_consistency] * 100).round %>%</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Pricing Strategy -->
        <div>
          <h2 class="text-xl font-bold text-gray-900 mb-4">💰 Suggested Pricing</h2>
          <div class="space-y-3">
            <% @assessment[:pricing_strategy].each do |tier, details| %>
              <% if tier != :usage_based %>
                <div class="border border-gray-200 rounded-lg p-4">
                  <div class="flex items-center justify-between mb-2">
                    <h3 class="font-semibold text-gray-900"><%= details[:name] %></h3>
                    <span class="text-lg font-bold text-green-600">$<%= details[:price] %></span>
                  </div>
                  <p class="text-sm text-gray-600"><%= details[:description] %></p>
                </div>
              <% end %>
            <% end %>

            <!-- Usage-based pricing -->
            <% if @assessment[:pricing_strategy][:usage_based] %>
              <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                <h3 class="font-semibold text-gray-900 mb-2">Usage-Based Pricing</h3>
                <div class="grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <span class="text-gray-600">Per API Call:</span>
                    <span class="font-medium ml-1">$<%= @assessment[:pricing_strategy][:usage_based][:per_api_call] %></span>
                  </div>
                  <div>
                    <span class="text-gray-600">Per 1K Records:</span>
                    <span class="font-medium ml-1">$<%= @assessment[:pricing_strategy][:usage_based][:per_1k_records] %></span>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Recommendations -->
      <div class="bg-blue-50 rounded-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">🎯 AI Recommendations</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="font-semibold text-gray-800 mb-2">Next Steps</h3>
            <ul class="space-y-2">
              <% @assessment[:recommendations].each do |recommendation| %>
                <li class="flex items-start">
                  <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                  </svg>
                  <span class="text-sm text-gray-700"><%= recommendation %></span>
                </li>
              <% end %>
            </ul>
          </div>
          
          <div>
            <h3 class="font-semibold text-gray-800 mb-2">Quick Actions</h3>
            <div class="space-y-2">
              <button class="w-full text-left px-4 py-2 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors">
                <div class="font-medium text-gray-900">Create API Endpoint</div>
                <div class="text-sm text-gray-600">Turn this pipeline into a monetizable API</div>
              </button>
              <button class="w-full text-left px-4 py-2 bg-white rounded-lg border border-gray-200 hover:border-green-300 hover:bg-green-50 transition-colors">
                <div class="font-medium text-gray-900">Setup Billing</div>
                <div class="text-sm text-gray-600">Configure pricing and payment processing</div>
              </button>
              <button class="w-full text-left px-4 py-2 bg-white rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-colors">
                <div class="font-medium text-gray-900">Generate Documentation</div>
                <div class="text-sm text-gray-600">Create API docs and usage guides</div>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- JSON Export -->
      <div class="mt-8 pt-6 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium text-gray-900">Export Analysis</h3>
            <p class="text-sm text-gray-600">Download detailed AI analysis as JSON</p>
          </div>
          <%= link_to ai_pipeline_insights_path(@pipeline, format: :json), 
                      class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",
                      target: "_blank" do %>
            📊 Download JSON Report
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Add some interactivity for future enhancements
  document.addEventListener('DOMContentLoaded', function() {
    // Future: Add click handlers for opportunity details
    // Future: Add real-time updates
    // Future: Add interactive charts
  });
</script>