<%# AI Data Product Recommendations Dashboard %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">🚀 Data Product Recommendations</h1>
          <p class="mt-1 text-sm text-gray-600">
            AI-powered recommendations for monetizing <strong><%= @pipeline.name %></strong>
          </p>
        </div>
        <div class="flex space-x-3">
          <%= link_to ai_pipeline_insights_path(@pipeline), class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" do %>
            📊 Revenue Analysis
          <% end %>
          <%= link_to pipeline_path(@pipeline), class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" do %>
            ← Back to Pipeline
          <% end %>
        </div>
      </div>
    </div>

    <div class="p-6">
      <!-- Monetization Readiness Overview -->
      <div class="mb-8">
        <div class="bg-gradient-to-r from-green-500 to-blue-600 rounded-lg p-6 text-white">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="text-center">
              <div class="text-3xl font-bold"><%= @monetization_readiness[:readiness_score] %></div>
              <div class="text-sm opacity-90">Readiness Score</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold capitalize"><%= @monetization_readiness[:readiness_level].humanize %></div>
              <div class="text-sm opacity-90">Readiness Level</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold"><%= @monetization_readiness[:blocking_issues].size %></div>
              <div class="text-sm opacity-90">Blocking Issues</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold"><%= @monetization_readiness[:quick_wins].size %></div>
              <div class="text-sm opacity-90">Quick Wins</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Product Recommendations Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div>
          <h2 class="text-xl font-bold text-gray-900 mb-4">🎯 Recommended Products</h2>
          <div class="space-y-4">
            <% @recommendations.take(4).each_with_index do |recommendation, index| %>
              <div class="border border-gray-200 rounded-lg p-4 <%= 'border-green-300 bg-green-50' if index == 0 %>">
                <div class="flex items-center justify-between mb-3">
                  <div>
                    <h3 class="font-semibold text-gray-900">
                      <%= recommendation[:name] %>
                      <% if index == 0 %>
                        <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Top Pick
                        </span>
                      <% end %>
                    </h3>
                    <p class="text-sm text-gray-600"><%= recommendation[:description] %></p>
                  </div>
                  <div class="text-right">
                    <div class="text-sm font-medium text-gray-900">
                      Fit Score: <%= (recommendation[:fit_score] * 100).round %>%
                    </div>
                    <div class="text-xs text-gray-500">
                      <%= recommendation[:time_to_market] %>
                    </div>
                  </div>
                </div>

                <div class="flex items-center justify-between mb-3">
                  <div class="flex space-x-4">
                    <div>
                      <span class="text-xs text-gray-500">Revenue Potential</span>
                      <div class="font-semibold text-green-600">$<%= number_with_delimiter(recommendation[:revenue_potential]) %>/year</div>
                    </div>
                    <div>
                      <span class="text-xs text-gray-500">Development</span>
                      <div class="font-semibold text-blue-600"><%= recommendation[:development_effort] %> weeks</div>
                    </div>
                  </div>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                    <%= case recommendation[:complexity]
                        when 'low' then 'bg-green-100 text-green-800'
                        when 'medium' then 'bg-yellow-100 text-yellow-800'
                        when 'high' then 'bg-orange-100 text-orange-800'
                        when 'very_high' then 'bg-red-100 text-red-800'
                        end %>">
                    <%= recommendation[:complexity].capitalize %> complexity
                  </span>
                </div>

                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <span class="text-xs text-gray-500 mr-1">Success Probability:</span>
                    <span class="font-medium text-gray-700"><%= (recommendation[:success_probability] * 100).round %>%</span>
                  </div>
                  <div class="flex space-x-2">
                    <%= link_to ai_pipeline_data_product_recommendation_path(@pipeline, recommendation[:product_type]), 
                                class: "text-sm text-blue-600 hover:text-blue-800 font-medium" do %>
                      View Details →
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
          </div>

          <% if @recommendations.size > 4 %>
            <div class="mt-4 text-center">
              <%= link_to compare_ai_pipeline_data_product_recommendations_path(@pipeline, 
                          product_types: @recommendations.map { |r| r[:product_type] }.join(',')), 
                          class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" do %>
                Compare All Options
              <% end %>
            </div>
          <% end %>
        </div>

        <!-- Revenue Opportunities -->
        <div>
          <h2 class="text-xl font-bold text-gray-900 mb-4">💰 Revenue Opportunities</h2>
          <div class="space-y-4">
            <% pipeline_opportunities = @revenue_opportunities.select { |opp| opp[:pipeline_id] == @pipeline.id } %>
            <% if pipeline_opportunities.any? %>
              <% pipeline_opportunities.take(3).each do |opportunity| %>
                <div class="border border-gray-200 rounded-lg p-4">
                  <div class="flex items-center justify-between mb-2">
                    <h3 class="font-semibold text-gray-900 capitalize">
                      <%= opportunity[:title] %>
                    </h3>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                      <%= case opportunity[:urgency]
                          when 'high' then 'bg-red-100 text-red-800'
                          when 'medium' then 'bg-yellow-100 text-yellow-800'
                          else 'bg-green-100 text-green-800'
                          end %>">
                      <%= opportunity[:urgency].capitalize %> Priority
                    </span>
                  </div>
                  <p class="text-sm text-gray-600 mb-2"><%= opportunity[:description] %></p>
                  <div class="flex items-center justify-between">
                    <div class="flex space-x-3">
                      <span class="text-sm font-medium text-green-600">
                        $<%= number_with_delimiter(opportunity[:revenue_impact] || 0) %> impact
                      </span>
                      <span class="text-sm text-gray-500">
                        <%= opportunity[:implementation_effort] %> effort
                      </span>
                    </div>
                    <div class="text-sm text-blue-600">
                      <%= (opportunity[:confidence] * 100).round %>% confidence
                    </div>
                  </div>
                </div>
              <% end %>
            <% else %>
              <div class="border border-gray-200 rounded-lg p-6 text-center">
                <div class="text-gray-400 mb-2">
                  <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <p class="text-sm text-gray-600">
                  No specific revenue opportunities detected for this pipeline yet.
                  <br>
                  Try improving pipeline performance or data quality first.
                </p>
              </div>
            <% end %>
          </div>

          <div class="mt-4">
            <%= link_to market_analysis_ai_pipeline_data_product_recommendations_path(@pipeline), 
                        class: "w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" do %>
              📈 Full Market Analysis
            <% end %>
          </div>
        </div>
      </div>

      <!-- Implementation Blockers & Quick Wins -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div>
          <h2 class="text-xl font-bold text-gray-900 mb-4">🚫 Implementation Blockers</h2>
          <% if @monetization_readiness[:blocking_issues].any? %>
            <div class="space-y-3">
              <% @monetization_readiness[:blocking_issues].each do |issue| %>
                <div class="border border-red-200 rounded-lg p-4 bg-red-50">
                  <div class="flex items-center justify-between mb-2">
                    <h3 class="font-semibold text-red-900 capitalize">
                      <%= issue[:type].humanize %>
                    </h3>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      <%= issue[:severity].capitalize %>
                    </span>
                  </div>
                  <p class="text-sm text-red-800 mb-2"><%= issue[:description] %></p>
                  <div class="text-xs text-red-700">
                    <strong>Impact:</strong> <%= issue[:impact] %>
                  </div>
                  <div class="text-xs text-red-700 mt-1">
                    <strong>Solution:</strong> <%= issue[:solution] %>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="border border-green-200 rounded-lg p-6 bg-green-50 text-center">
              <div class="text-green-500 mb-2">
                <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <p class="text-sm font-medium text-green-800">No major blockers detected!</p>
              <p class="text-xs text-green-700">Your pipeline is ready for monetization.</p>
            </div>
          <% end %>
        </div>

        <div>
          <h2 class="text-xl font-bold text-gray-900 mb-4">⚡ Quick Wins</h2>
          <% if @monetization_readiness[:quick_wins].any? %>
            <div class="space-y-3">
              <% @monetization_readiness[:quick_wins].each do |win| %>
                <div class="border border-green-200 rounded-lg p-4 bg-green-50">
                  <div class="flex items-center justify-between mb-2">
                    <h3 class="font-semibold text-green-900 capitalize">
                      <%= win[:type].humanize %>
                    </h3>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      <%= win[:effort].capitalize %> effort
                    </span>
                  </div>
                  <p class="text-sm text-green-800 mb-2"><%= win[:description] %></p>
                  <div class="flex items-center justify-between">
                    <div class="text-xs text-green-700">
                      <strong>Impact:</strong> <%= win[:impact] %>
                    </div>
                    <div class="text-sm font-medium text-green-600">
                      $<%= number_with_delimiter(win[:estimated_value]) %>/year
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="border border-gray-200 rounded-lg p-6 text-center">
              <div class="text-gray-400 mb-2">
                <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
              <p class="text-sm text-gray-600">
                No immediate quick wins identified.
                <br>
                Focus on resolving blockers first.
              </p>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="bg-blue-50 rounded-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">🎯 Next Steps</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <%= link_to revenue_projections_ai_pipeline_data_product_recommendations_path(@pipeline), 
                      class: "flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors" do %>
            <div class="text-center">
              <div class="font-semibold">Revenue Projections</div>
              <div class="text-sm opacity-90">Detailed financial forecasts</div>
            </div>
          <% end %>

          <% if @recommendations.any? %>
            <%= link_to ai_pipeline_data_product_recommendation_path(@pipeline, @recommendations.first[:product_type]), 
                        class: "flex items-center justify-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors" do %>
              <div class="text-center">
                <div class="font-semibold">Implementation Plan</div>
                <div class="text-sm opacity-90">Start with top recommendation</div>
              </div>
            <% end %>
          <% end %>

          <%= link_to export_recommendations_ai_pipeline_data_product_recommendations_path(@pipeline, format: :json), 
                      class: "flex items-center justify-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",
                      target: "_blank" do %>
            <div class="text-center">
              <div class="font-semibold">Export Report</div>
              <div class="text-sm opacity-90">Download detailed analysis</div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>