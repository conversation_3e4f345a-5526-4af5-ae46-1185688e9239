<% content_for :page_title, "Analytics Dashboard" %>
<% content_for :layout, 'dashboard' %>

<!-- Include Chart.js for visualizations -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

<div data-controller="analytics-dashboard" 
     data-analytics-dashboard-refresh-interval-value="30000"
     class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

  <!-- Header Section -->
  <div class="mb-8">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
        <p class="mt-2 text-gray-600">Real-time insights and performance metrics</p>
      </div>
      
      <!-- Date Range Selector -->
      <div class="mt-4 lg:mt-0 flex items-center space-x-4">
        <div class="flex items-center bg-white rounded-lg shadow-sm border border-gray-200 p-1">
          <button data-action="click->analytics-dashboard#setRange" 
                  data-range="24h"
                  class="px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 text-gray-700 hover:bg-gray-100"
                  data-analytics-dashboard-target="rangeButton">
            24H
          </button>
          <button data-action="click->analytics-dashboard#setRange" 
                  data-range="7d"
                  class="px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 bg-indigo-600 text-white"
                  data-analytics-dashboard-target="rangeButton">
            7D
          </button>
          <button data-action="click->analytics-dashboard#setRange" 
                  data-range="30d"
                  class="px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 text-gray-700 hover:bg-gray-100"
                  data-analytics-dashboard-target="rangeButton">
            30D
          </button>
          <button data-action="click->analytics-dashboard#setRange" 
                  data-range="90d"
                  class="px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 text-gray-700 hover:bg-gray-100"
                  data-analytics-dashboard-target="rangeButton">
            90D
          </button>
        </div>
        
        <!-- Refresh Button -->
        <button data-action="click->analytics-dashboard#refresh"
                class="p-2 bg-white rounded-lg shadow-sm border border-gray-200 hover:bg-gray-50 transition-colors duration-200">
          <svg class="h-5 w-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
        </button>
        
        <!-- Export Button -->
        <button data-action="click->analytics-dashboard#export"
                class="px-4 py-2 bg-white rounded-lg shadow-sm border border-gray-200 hover:bg-gray-50 transition-colors duration-200 flex items-center">
          <svg class="h-5 w-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          <span class="text-sm font-medium text-gray-700">Export</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Key Performance Indicators -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Revenue -->
    <div class="bg-green-600 rounded-xl shadow-lg p-6 text-white transform hover:scale-105 transition-all duration-200">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-green-100 text-sm font-medium">Total Revenue</p>
          <p class="text-3xl font-bold mt-2">$124,567</p>
          <div class="flex items-center mt-2">
            <svg class="h-4 w-4 text-green-200 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="text-green-200 text-sm">+23.5% from last month</span>
          </div>
        </div>
        <div class="bg-white bg-opacity-20 rounded-lg p-3">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- Active Users -->
    <div class="bg-blue-600 rounded-xl shadow-lg p-6 text-white transform hover:scale-105 transition-all duration-200">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-blue-100 text-sm font-medium">Active Users</p>
          <p class="text-3xl font-bold mt-2">8,234</p>
          <div class="flex items-center mt-2">
            <svg class="h-4 w-4 text-blue-200 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="text-blue-200 text-sm">+18.2% from last week</span>
          </div>
        </div>
        <div class="bg-white bg-opacity-20 rounded-lg p-3">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- Conversion Rate -->
    <div class="bg-purple-600 rounded-xl shadow-lg p-6 text-white transform hover:scale-105 transition-all duration-200">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-purple-100 text-sm font-medium">Conversion Rate</p>
          <p class="text-3xl font-bold mt-2">3.24%</p>
          <div class="flex items-center mt-2">
            <svg class="h-4 w-4 text-red-300 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="text-red-300 text-sm">-5.1% from last week</span>
          </div>
        </div>
        <div class="bg-white bg-opacity-20 rounded-lg p-3">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- Data Processed -->
    <div class="bg-orange-600 rounded-xl shadow-lg p-6 text-white transform hover:scale-105 transition-all duration-200">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-orange-100 text-sm font-medium">Data Processed</p>
          <p class="text-3xl font-bold mt-2">2.4TB</p>
          <div class="flex items-center mt-2">
            <svg class="h-4 w-4 text-orange-200 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="text-orange-200 text-sm">+45.3% from yesterday</span>
          </div>
        </div>
        <div class="bg-white bg-opacity-20 rounded-lg p-3">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"/>
          </svg>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts Section -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Revenue Trend Chart -->
    <div class="bg-white rounded-xl shadow-lg p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Revenue Trend</h3>
        <div class="flex items-center space-x-2">
          <button class="text-sm text-gray-600 hover:text-gray-900">Daily</button>
          <span class="text-gray-400">|</span>
          <button class="text-sm text-indigo-600 font-medium">Weekly</button>
          <span class="text-gray-400">|</span>
          <button class="text-sm text-gray-600 hover:text-gray-900">Monthly</button>
        </div>
      </div>
      <canvas id="revenueTrendChart" data-analytics-dashboard-target="revenueTrendChart"></canvas>
    </div>

    <!-- User Activity Overview -->
    <div class="bg-white rounded-xl shadow-lg p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">User Activity</h3>
        <button class="text-sm text-indigo-600 hover:text-indigo-700">View Details</button>
      </div>
      <canvas id="activityHeatmapChart" data-analytics-dashboard-target="activityHeatmapChart" height="200"></canvas>
    </div>
  </div>

  <!-- Performance Metrics -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Pipeline Performance -->
    <div class="bg-white rounded-xl shadow-lg p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Pipeline Performance</h3>
      <canvas id="pipelinePerformanceChart" data-analytics-dashboard-target="pipelinePerformanceChart"></canvas>
      <div class="mt-4 space-y-2">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">Success Rate</span>
          <span class="text-sm font-semibold text-green-600">94.5%</span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">Avg Processing Time</span>
          <span class="text-sm font-semibold text-gray-900">2.3s</span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">Error Rate</span>
          <span class="text-sm font-semibold text-red-600">5.5%</span>
        </div>
      </div>
    </div>

    <!-- Data Source Distribution -->
    <div class="bg-white rounded-xl shadow-lg p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Data Source Distribution</h3>
      <canvas id="dataSourceChart" data-analytics-dashboard-target="dataSourceChart"></canvas>
      <div class="mt-4 grid grid-cols-2 gap-2">
        <div class="flex items-center">
          <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
          <span class="text-xs text-gray-600">PostgreSQL</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
          <span class="text-xs text-gray-600">MySQL</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
          <span class="text-xs text-gray-600">MongoDB</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
          <span class="text-xs text-gray-600">S3</span>
        </div>
      </div>
    </div>

    <!-- System Health -->
    <div class="bg-white rounded-xl shadow-lg p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">System Health</h3>
      <div class="space-y-4">
        <!-- CPU Usage -->
        <div>
          <div class="flex items-center justify-between mb-1">
            <span class="text-sm text-gray-600">CPU Usage</span>
            <span class="text-sm font-semibold text-gray-900">42%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-green-500 h-2 rounded-full" style="width: 42%"></div>
          </div>
        </div>
        
        <!-- Memory Usage -->
        <div>
          <div class="flex items-center justify-between mb-1">
            <span class="text-sm text-gray-600">Memory Usage</span>
            <span class="text-sm font-semibold text-gray-900">68%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-yellow-500 h-2 rounded-full" style="width: 68%"></div>
          </div>
        </div>
        
        <!-- Disk Usage -->
        <div>
          <div class="flex items-center justify-between mb-1">
            <span class="text-sm text-gray-600">Disk Usage</span>
            <span class="text-sm font-semibold text-gray-900">85%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-red-500 h-2 rounded-full" style="width: 85%"></div>
          </div>
        </div>
        
        <!-- Network I/O -->
        <div>
          <div class="flex items-center justify-between mb-1">
            <span class="text-sm text-gray-600">Network I/O</span>
            <span class="text-sm font-semibold text-gray-900">124 MB/s</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-blue-500 h-2 rounded-full" style="width: 35%"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Real-time Activity Feed -->
  <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">Real-time Activity Feed</h3>
      <div class="flex items-center">
        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
        <span class="text-sm text-gray-600">Live</span>
      </div>
    </div>
    
    <div class="space-y-4" data-analytics-dashboard-target="activityFeed">
      <!-- Activity items will be inserted here via JavaScript -->
      <div class="flex items-start space-x-3">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
              <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
            </svg>
          </div>
        </div>
        <div class="flex-1">
          <p class="text-sm text-gray-900">
            <span class="font-medium">Pipeline "Customer ETL"</span> completed successfully
          </p>
          <p class="text-xs text-gray-500">2 minutes ago • 1.2M rows processed</p>
        </div>
      </div>
      
      <div class="flex items-start space-x-3">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
          </div>
        </div>
        <div class="flex-1">
          <p class="text-sm text-gray-900">
            <span class="font-medium">New connection</span> to PostgreSQL database established
          </p>
          <p class="text-xs text-gray-500">5 minutes ago • Production server</p>
        </div>
      </div>
      
      <div class="flex items-start space-x-3">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
          </div>
        </div>
        <div class="flex-1">
          <p class="text-sm text-gray-900">
            <span class="font-medium">Warning:</span> High memory usage detected
          </p>
          <p class="text-xs text-gray-500">8 minutes ago • 85% memory utilization</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Data Quality Metrics -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Data Quality Score -->
    <div class="bg-white rounded-xl shadow-lg p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Data Quality Score</h3>
      <div class="flex items-center justify-center">
        <div class="relative">
          <canvas id="dataQualityGauge" data-analytics-dashboard-target="dataQualityGauge" width="200" height="200"></canvas>
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="text-center">
              <p class="text-4xl font-bold text-gray-900">87</p>
              <p class="text-sm text-gray-600">Quality Score</p>
            </div>
          </div>
        </div>
      </div>
      <div class="mt-6 space-y-3">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">Completeness</span>
          <div class="flex items-center">
            <div class="w-24 bg-gray-200 rounded-full h-2 mr-2">
              <div class="bg-green-500 h-2 rounded-full" style="width: 92%"></div>
            </div>
            <span class="text-sm font-semibold text-gray-900">92%</span>
          </div>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">Accuracy</span>
          <div class="flex items-center">
            <div class="w-24 bg-gray-200 rounded-full h-2 mr-2">
              <div class="bg-green-500 h-2 rounded-full" style="width: 88%"></div>
            </div>
            <span class="text-sm font-semibold text-gray-900">88%</span>
          </div>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">Consistency</span>
          <div class="flex items-center">
            <div class="w-24 bg-gray-200 rounded-full h-2 mr-2">
              <div class="bg-yellow-500 h-2 rounded-full" style="width: 79%"></div>
            </div>
            <span class="text-sm font-semibold text-gray-900">79%</span>
          </div>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">Timeliness</span>
          <div class="flex items-center">
            <div class="w-24 bg-gray-200 rounded-full h-2 mr-2">
              <div class="bg-green-500 h-2 rounded-full" style="width: 90%"></div>
            </div>
            <span class="text-sm font-semibold text-gray-900">90%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Top Pipelines by Volume -->
    <div class="bg-white rounded-xl shadow-lg p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Pipelines by Volume</h3>
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center flex-1">
            <div class="w-2 h-8 bg-indigo-500 rounded-full mr-3"></div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-900">Customer Data Sync</p>
              <p class="text-xs text-gray-500">PostgreSQL → Snowflake</p>
            </div>
          </div>
          <div class="text-right">
            <p class="text-sm font-semibold text-gray-900">2.1M</p>
            <p class="text-xs text-gray-500">rows/hour</p>
          </div>
        </div>
        
        <div class="flex items-center justify-between">
          <div class="flex items-center flex-1">
            <div class="w-2 h-8 bg-green-500 rounded-full mr-3"></div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-900">Sales Analytics ETL</p>
              <p class="text-xs text-gray-500">Salesforce → BigQuery</p>
            </div>
          </div>
          <div class="text-right">
            <p class="text-sm font-semibold text-gray-900">1.8M</p>
            <p class="text-xs text-gray-500">rows/hour</p>
          </div>
        </div>
        
        <div class="flex items-center justify-between">
          <div class="flex items-center flex-1">
            <div class="w-2 h-8 bg-purple-500 rounded-full mr-3"></div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-900">Product Events Stream</p>
              <p class="text-xs text-gray-500">Kafka → Elasticsearch</p>
            </div>
          </div>
          <div class="text-right">
            <p class="text-sm font-semibold text-gray-900">1.5M</p>
            <p class="text-xs text-gray-500">rows/hour</p>
          </div>
        </div>
        
        <div class="flex items-center justify-between">
          <div class="flex items-center flex-1">
            <div class="w-2 h-8 bg-orange-500 rounded-full mr-3"></div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-900">Log Aggregation</p>
              <p class="text-xs text-gray-500">CloudWatch → S3</p>
            </div>
          </div>
          <div class="text-right">
            <p class="text-sm font-semibold text-gray-900">982K</p>
            <p class="text-xs text-gray-500">rows/hour</p>
          </div>
        </div>
        
        <div class="flex items-center justify-between">
          <div class="flex items-center flex-1">
            <div class="w-2 h-8 bg-blue-500 rounded-full mr-3"></div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-900">Marketing Data Sync</p>
              <p class="text-xs text-gray-500">HubSpot → MySQL</p>
            </div>
          </div>
          <div class="text-right">
            <p class="text-sm font-semibold text-gray-900">756K</p>
            <p class="text-xs text-gray-500">rows/hour</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Initialize charts when the page loads
  document.addEventListener('DOMContentLoaded', function() {
    // Revenue Trend Chart
    const revenueTrendCtx = document.getElementById('revenueTrendChart').getContext('2d');
    new Chart(revenueTrendCtx, {
      type: 'line',
      data: {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [{
          label: 'Revenue',
          data: [12000, 19000, 15000, 25000, 22000, 30000, 28000],
          borderColor: 'rgb(99, 102, 241)',
          backgroundColor: 'rgba(99, 102, 241, 0.1)',
          tension: 0.4,
          fill: true
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return '$' + value.toLocaleString();
              }
            }
          }
        }
      }
    });

    // User Activity Chart
    const activityHeatmapCtx = document.getElementById('activityHeatmapChart').getContext('2d');
    new Chart(activityHeatmapCtx, {
      type: 'line',
      data: {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [{
          label: 'Active Users',
          data: [1200, 1900, 1500, 2500, 2200, 1800, 1300],
          borderColor: 'rgb(99, 102, 241)',
          backgroundColor: 'rgba(99, 102, 241, 0.1)',
          tension: 0.4,
          fill: true
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return value.toLocaleString();
              }
            }
          }
        }
      }
    });

    // Pipeline Performance Chart
    const pipelinePerformanceCtx = document.getElementById('pipelinePerformanceChart').getContext('2d');
    new Chart(pipelinePerformanceCtx, {
      type: 'doughnut',
      data: {
        labels: ['Successful', 'Failed', 'Running'],
        datasets: [{
          data: [342, 18, 24],
          backgroundColor: [
            'rgba(34, 197, 94, 0.8)',
            'rgba(239, 68, 68, 0.8)',
            'rgba(251, 191, 36, 0.8)'
          ]
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    });

    // Data Source Distribution Chart
    const dataSourceCtx = document.getElementById('dataSourceChart').getContext('2d');
    new Chart(dataSourceCtx, {
      type: 'pie',
      data: {
        labels: ['PostgreSQL', 'MySQL', 'MongoDB', 'S3'],
        datasets: [{
          data: [35, 25, 20, 20],
          backgroundColor: [
            'rgba(59, 130, 246, 0.8)',
            'rgba(34, 197, 94, 0.8)',
            'rgba(168, 85, 247, 0.8)',
            'rgba(251, 146, 60, 0.8)'
          ]
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        }
      }
    });
  });
</script>