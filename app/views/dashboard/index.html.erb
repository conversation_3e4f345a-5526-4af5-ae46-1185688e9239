<% content_for :page_title, "Dashboard" %>
<% content_for :layout, 'dashboard' %>

<div data-controller="dashboard" data-dashboard-auto-refresh-value="true">

<!-- Welcome Section -->
<div class="mb-8">
  <div class="bg-gradient-to-r from-indigo-600 to-blue-600 rounded-lg shadow-lg overflow-hidden">
    <div class="px-6 py-8 sm:px-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-white mb-2">
            Welcome back, <%= @user.full_name %>!
          </h1>
          <p class="text-indigo-100 text-lg">
            <% if @user.owner? %>
              Manage your team and monitor your data pipelines.
            <% elsif @user.admin? %>
              Monitor pipelines and manage team operations.
            <% elsif @user.member? %>
              Here's what's happening with your data pipelines today.
            <% else %>
              View your team's data pipeline activity.
            <% end %>
          </p>
          <% if @account.subscription&.plan == 'free' %>
            <div class="mt-3">
              <%= link_to subscription_path,
                  class: "inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50 transition-colors duration-200" do %>
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
                Upgrade to unlock more features
              <% end %>
            </div>
          <% end %>
        </div>
        <div class="hidden sm:block">
          <div class="flex items-center space-x-4">
            <div class="text-right">
              <p class="text-sm text-indigo-100">Account</p>
              <p class="text-lg font-semibold text-white"><%= @account.name %></p>
            </div>
            <div class="h-12 w-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Key Metrics -->
<section class="mb-8" aria-labelledby="key-metrics-heading">
  <h2 id="key-metrics-heading" class="text-lg font-semibold text-gray-900 mb-4">Key Metrics</h2>
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
    <!-- Pipelines Metric -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 hover:shadow-md transition-shadow duration-200"
         data-dashboard-target="metricCard"
         data-metric="pipelines"
         role="button"
         tabindex="0"
         aria-label="Pipelines metric: <%= @pipeline_metrics[:total_pipelines] %> total, <%= @pipeline_metrics[:active_pipelines] %> active"
         onkeydown="if(event.key==='Enter'||event.key===' '){this.click()}">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="h-12 w-12 bg-indigo-100 rounded-lg flex items-center justify-center" aria-hidden="true">
            <svg class="h-6 w-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
            </svg>
          </div>
        </div>
        <div class="ml-4 flex-1">
          <p class="text-sm font-medium text-gray-500" id="pipelines-label">Total Pipelines</p>
          <div class="flex items-baseline">
            <p class="text-2xl font-semibold text-gray-900"
               data-controller="metrics"
               data-metrics-current-value-value="<%= @pipeline_metrics[:total_pipelines] %>"
               data-metrics-metric-value="total_pipelines"
               aria-labelledby="pipelines-label"
               aria-describedby="pipelines-description">
              <%= @pipeline_metrics[:total_pipelines] %>
            </p>
            <p class="ml-2 text-sm text-green-600" id="pipelines-description">
              <%= @pipeline_metrics[:active_pipelines] %> active
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Projects Metric -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 hover:shadow-md transition-shadow duration-200"
         data-dashboard-target="metricCard"
         data-metric="projects"
         role="button"
         tabindex="0"
         aria-label="Projects metric: <%= @project_metrics[:total_projects] %> total, <%= @project_metrics[:active_projects] %> active"
         onkeydown="if(event.key==='Enter'||event.key===' '){this.click()}">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center" aria-hidden="true">
            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
            </svg>
          </div>
        </div>
        <div class="ml-4 flex-1">
          <p class="text-sm font-medium text-gray-500" id="projects-label">Total Projects</p>
          <div class="flex items-baseline">
            <p class="text-2xl font-semibold text-gray-900"
               data-controller="metrics"
               data-metrics-current-value-value="<%= @project_metrics[:total_projects] %>"
               data-metrics-metric-value="total_projects"
               aria-labelledby="projects-label"
               aria-describedby="projects-description">
              <%= @project_metrics[:total_projects] %>
            </p>
            <p class="ml-2 text-sm text-blue-600" id="projects-description">
              <%= @project_metrics[:active_projects] %> active
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Rate Metric -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 hover:shadow-md transition-shadow duration-200 cursor-pointer"
         data-dashboard-target="metricCard"
         data-metric="success_rate"
         role="button"
         tabindex="0"
         aria-label="Success rate metric: <%= number_to_percentage(@pipeline_metrics[:avg_success_rate] * 100, precision: 1) %> over last 30 days"
         onkeydown="if(event.key==='Enter'||event.key===' '){this.click()}">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center" aria-hidden="true">
            <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
        </div>
        <div class="ml-4 flex-1">
          <p class="text-sm font-medium text-gray-500" id="success-rate-label">Success Rate</p>
          <div class="flex items-baseline">
            <p class="text-2xl font-semibold text-gray-900"
               data-controller="metrics"
               data-metrics-current-value-value="<%= @pipeline_metrics[:avg_success_rate] * 100 %>"
               data-metrics-metric-value="success_rate"
               data-metrics-format-value="percentage"
               aria-labelledby="success-rate-label"
               aria-describedby="success-rate-description">
              <%= number_to_percentage(@pipeline_metrics[:avg_success_rate] * 100, precision: 1) %>
            </p>
            <p class="ml-2 text-sm text-gray-600" id="success-rate-description">last 30 days</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Connections Metric -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
            </svg>
          </div>
        </div>
        <div class="ml-4 flex-1">
          <p class="text-sm font-medium text-gray-500">Connections</p>
          <div class="flex items-baseline">
            <p class="text-2xl font-semibold text-gray-900"><%= @connector_metrics[:total] %></p>
            <p class="ml-2 text-sm text-green-600">
              <%= @connector_metrics[:healthy] %> healthy
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Data Processed Metric -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
            <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
          </div>
        </div>
        <div class="ml-4 flex-1">
          <p class="text-sm font-medium text-gray-500">Data Processed</p>
          <div class="flex items-baseline">
            <p class="text-2xl font-semibold text-gray-900">
              <%= number_to_human(@usage_metrics[:data_processed_mb], units: { thousand: 'K', million: 'M' }) %>
            </p>
            <p class="ml-2 text-sm text-gray-600">rows</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Plan Usage & Limits (for owners and admins) -->
<% if can_manage_account? %>
  <section class="mb-8" aria-labelledby="plan-usage-heading">
    <h2 id="plan-usage-heading" class="text-lg font-semibold text-gray-900 mb-4">Plan Usage</h2>
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Team Members Usage -->
        <div>
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">Team Members</span>
            <span class="text-sm text-gray-500">
              <%= @account.users.count %> / <%= @account.max_team_members == -1 ? '∞' : @account.max_team_members + 1 %>
            </span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <% team_usage = @account.max_team_members == -1 ? 0 : usage_percentage(@account.users.count, @account.max_team_members + 1) %>
            <div class="h-2 rounded-full bg-green-500"
                 style="width: <%= [team_usage, 100].min %>%"></div>
          </div>
        </div>

        <!-- Pipelines Usage -->
        <div>
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">Pipelines</span>
            <span class="text-sm text-gray-500">
              <%= @pipeline_metrics[:total_pipelines] %> / <%= @account.plan_pipeline_limit == -1 ? '∞' : @account.plan_pipeline_limit %>
            </span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <% pipeline_usage = @account.plan_pipeline_limit == -1 ? 0 : usage_percentage(@pipeline_metrics[:total_pipelines], @account.plan_pipeline_limit) %>
            <div class="h-2 rounded-full bg-blue-500"
                 style="width: <%= [pipeline_usage, 100].min %>%"></div>
          </div>
        </div>

        <!-- Storage Usage -->
        <div>
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">Storage</span>
            <span class="text-sm text-gray-500">
              <%= number_to_human_size(@usage_metrics[:storage_used_mb] * 1024 * 1024) %> /
              <%= @account.subscription&.plan == 'enterprise' ? '∞' : '10 GB' %>
            </span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <% storage_limit_mb = @account.subscription&.plan == 'enterprise' ? -1 : 10240 %>
            <% storage_usage = storage_limit_mb == -1 ? 0 : usage_percentage(@usage_metrics[:storage_used_mb], storage_limit_mb) %>
            <div class="h-2 rounded-full bg-purple-500"
                 style="width: <%= [storage_usage, 100].min %>%"></div>
          </div>
        </div>
      </div>

      <% if @account.subscription&.plan == 'free' %>
        <div class="mt-6 p-4 bg-indigo-50 rounded-lg border border-indigo-200">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-indigo-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-indigo-800">Upgrade for more features</h3>
              <p class="mt-1 text-sm text-indigo-700">
                Get unlimited pipelines, advanced analytics, and priority support.
              </p>
              <div class="mt-3">
                <%= link_to subscription_path,
                    class: "text-sm font-medium text-indigo-600 hover:text-indigo-500" do %>
                  View plans →
                <% end %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </section>
<% end %>

<!-- Quick Actions -->
<section class="mb-8" aria-labelledby="quick-actions-heading">
  <h2 id="quick-actions-heading" class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
  <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4">
    <!-- Create Pipeline -->
    <%= link_to new_pipeline_path,
        class: "group bg-white border-2 border-dashed border-gray-300 rounded-lg p-4 sm:p-6 hover:border-indigo-500 hover:bg-indigo-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2",
        data: {
          dashboard_target: "quickAction",
          action: "create_pipeline"
        },
        'aria-label': "Create a new data pipeline",
        'aria-describedby': "create-pipeline-desc" do %>
      <div class="text-center">
        <div class="mx-auto h-12 w-12 bg-indigo-100 rounded-lg flex items-center justify-center group-hover:bg-indigo-200 transition-colors duration-200" aria-hidden="true">
          <svg class="h-6 w-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
        </div>
        <h3 class="mt-3 text-sm font-medium text-gray-900 group-hover:text-indigo-900">Create Pipeline</h3>
        <p class="mt-1 text-xs text-gray-500" id="create-pipeline-desc">Build a new data pipeline</p>
      </div>
    <% end %>

    <!-- Add Connection -->
    <%
      # Get the first active project or create a default one for the quick action
      first_project = current_account.projects.active.first
      connector_path = first_project ? new_project_data_connector_path(first_project) : projects_path
    %>
    <%= link_to connector_path,
        class: "group bg-white border-2 border-dashed border-gray-300 rounded-lg p-4 sm:p-6 hover:border-green-500 hover:bg-green-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",
        data: {
          dashboard_target: "quickAction",
          action: "add_connection"
        },
        'aria-label': "Add a new data connection",
        'aria-describedby': "add-connection-desc" do %>
      <div class="text-center">
        <div class="mx-auto h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200" aria-hidden="true">
          <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
          </svg>
        </div>
        <h3 class="mt-3 text-sm font-medium text-gray-900 group-hover:text-green-900">Add Connection</h3>
        <p class="mt-1 text-xs text-gray-500" id="add-connection-desc">Connect a data source</p>
      </div>
    <% end %>

    <!-- View Analytics -->
    <%= link_to analytics_path,
        class: "group bg-white border-2 border-dashed border-gray-300 rounded-lg p-4 sm:p-6 hover:border-purple-500 hover:bg-purple-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2",
        data: {
          dashboard_target: "quickAction",
          action: "view_analytics"
        },
        'aria-label': "View analytics and insights",
        'aria-describedby': "view-analytics-desc" do %>
      <div class="text-center">
        <div class="mx-auto h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center group-hover:bg-purple-200 transition-colors duration-200" aria-hidden="true">
          <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
          </svg>
        </div>
        <h3 class="mt-3 text-sm font-medium text-gray-900 group-hover:text-purple-900">View Analytics</h3>
        <p class="mt-1 text-xs text-gray-500" id="view-analytics-desc">Explore your data insights</p>
      </div>
    <% end %>

    <!-- Manage Billing -->
    <%= link_to subscription_path,
        class: "group bg-white border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-orange-500 hover:bg-orange-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500" do %>
      <div class="text-center">
        <div class="mx-auto h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center group-hover:bg-orange-200 transition-colors duration-200">
          <svg class="h-6 w-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
          </svg>
        </div>
        <h3 class="mt-3 text-sm font-medium text-gray-900 group-hover:text-orange-900">Manage Billing</h3>
        <p class="mt-1 text-xs text-gray-500">Update subscription & billing</p>
      </div>
    <% end %>

    <!-- Team Settings -->
    <%= link_to team_members_path,
        class: "group bg-white border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-blue-500 hover:bg-blue-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500" do %>
      <div class="text-center">
        <div class="mx-auto h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors duration-200">
          <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
          </svg>
        </div>
        <h3 class="mt-3 text-sm font-medium text-gray-900 group-hover:text-blue-900">Team Settings</h3>
        <p class="mt-1 text-xs text-gray-500">Manage team members</p>
      </div>
    <% end %>
  </div>
</div>

<!-- Recent Activity & System Health -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
  <!-- Recent Activity -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">Recent Activity</h3>
    </div>
    <div class="p-6">
      <% if @recent_activity.any? %>
        <div class="space-y-4">
          <% @recent_activity.each do |activity| %>
            <div class="flex items-start space-x-3">
              <div class="flex-shrink-0">
                <div class="h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center">
                  <svg class="h-4 w-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                  </svg>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm text-gray-900"><%= activity[:message] %></p>
                <p class="text-xs text-gray-500"><%= activity[:timestamp] %></p>
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No recent activity</h3>
          <p class="mt-1 text-sm text-gray-500">Get started by creating your first pipeline or connection.</p>
        </div>
      <% end %>
    </div>
  </div>

  <!-- System Health -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">System Health</h3>
    </div>
    <div class="p-6">
      <div class="space-y-4">
        <!-- Overall Status -->
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="h-3 w-3 bg-green-400 rounded-full mr-3"></div>
            <span class="text-sm font-medium text-gray-900">Overall Status</span>
          </div>
          <span class="text-sm text-green-600 font-medium">Healthy</span>
        </div>

        <!-- Uptime -->
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">Uptime</span>
          <span class="text-sm font-medium text-gray-900"><%= @system_health[:uptime_percentage] %>%</span>
        </div>

        <!-- Response Time -->
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">Avg Response Time</span>
          <span class="text-sm font-medium text-gray-900"><%= @system_health[:response_time_ms] %>ms</span>
        </div>

        <!-- Last Backup -->
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">Last Backup</span>
          <span class="text-sm font-medium text-gray-900">
            <%= time_ago_in_words(@system_health[:last_backup]) %> ago
          </span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Account Summary -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
  <div class="px-6 py-4 border-b border-gray-200">
    <h3 class="text-lg font-medium text-gray-900">Account Summary</h3>
  </div>
  <div class="p-6">
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <div>
        <dt class="text-sm font-medium text-gray-500">Account Name</dt>
        <dd class="mt-1 text-sm text-gray-900"><%= @account.name %></dd>
      </div>
      <div>
        <dt class="text-sm font-medium text-gray-500">Subdomain</dt>
        <dd class="mt-1 text-sm text-gray-900"><%= @account.subdomain %>.dataReflow.io</dd>
      </div>
      <div>
        <dt class="text-sm font-medium text-gray-500">Current Plan</dt>
        <dd class="mt-1">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
            <%= @account.subscription&.plan&.humanize || 'Free' %>
          </span>
        </dd>
      </div>
      <div>
        <dt class="text-sm font-medium text-gray-500">Team Members</dt>
        <dd class="mt-1 text-sm text-gray-900">
          <%= @account.users.count %> / <%= @account.max_team_members == -1 ? '∞' : @account.max_team_members + 1 %>
        </dd>
      </div>
    </div>
  </div>
</div>

</div> <!-- End dashboard controller -->