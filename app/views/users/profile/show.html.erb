<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="bg-white shadow-xl rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
      <h1 class="text-2xl font-bold text-gray-900">My Profile</h1>
    </div>

    <%= form_with model: @user, url: users_profile_path, method: :patch, local: true, 
                  data: { controller: "form-validation profile-manager" } do |form| %>
      
      <div class="p-6 space-y-6">
        <!-- Personal Information Section -->
        <div>
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <%= form.label :first_name, class: "block text-sm font-medium text-gray-700 mb-1" %>
              <%= form.text_field :first_name, 
                  class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500",
                  placeholder: "Enter your first name",
                  data: { "form-validation-target": "field" } %>
            </div>

            <div>
              <%= form.label :last_name, class: "block text-sm font-medium text-gray-700 mb-1" %>
              <%= form.text_field :last_name, 
                  class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500",
                  placeholder: "Enter your last name",
                  data: { "form-validation-target": "field" } %>
            </div>

            <div>
              <%= form.label :email, class: "block text-sm font-medium text-gray-700 mb-1" %>
              <%= form.email_field :email, 
                  class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500",
                  placeholder: "<EMAIL>",
                  data: { "form-validation-target": "field" } %>
            </div>

            <div>
              <%= form.label :phone, class: "block text-sm font-medium text-gray-700 mb-1" %>
              <%= form.text_field :phone, 
                  class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500",
                  placeholder: "+****************",
                  data: { "form-validation-target": "field" } %>
            </div>
          </div>

          <div class="mt-4">
            <%= form.label :bio, class: "block text-sm font-medium text-gray-700 mb-1" %>
            <%= form.text_area :bio, 
                rows: 4,
                class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500",
                placeholder: "Tell us about yourself...",
                data: { "form-validation-target": "field" } %>
          </div>
        </div>

        <!-- Preferences Section -->
        <div>
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Preferences</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <%= form.label :theme, class: "block text-sm font-medium text-gray-700 mb-1" %>
              <%= form.select :theme, 
                  options_for_select([
                    ['Light', 'light'],
                    ['Dark', 'dark'],
                    ['System', 'system']
                  ], @user.theme || 'light'),
                  {},
                  class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500",
                  data: { "profile-manager-target": "themeSelector" } %>
            </div>

            <div>
              <%= form.label :language, class: "block text-sm font-medium text-gray-700 mb-1" %>
              <%= form.select :language, 
                  options_for_select([
                    ['English', 'en'],
                    ['Spanish', 'es'],
                    ['French', 'fr'],
                    ['German', 'de']
                  ], @user.language || 'en'),
                  {},
                  class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500" %>
            </div>

            <div>
              <%= form.label :timezone, class: "block text-sm font-medium text-gray-700 mb-1" %>
              <%= form.select :timezone, 
                  options_for_select([
                    ['Pacific Time (PT)', 'America/Los_Angeles'],
                    ['Mountain Time (MT)', 'America/Denver'],
                    ['Central Time (CT)', 'America/Chicago'],
                    ['Eastern Time (ET)', 'America/New_York']
                  ], @user.timezone || 'America/Los_Angeles'),
                  {},
                  class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500" %>
            </div>
          </div>
        </div>

        <!-- Account Information Section -->
        <div>
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Account Information</h2>
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span class="text-sm font-medium text-gray-700">Account Name:</span>
                <p class="text-gray-900"><%= @account.name %></p>
              </div>
              <div>
                <span class="text-sm font-medium text-gray-700">Account ID:</span>
                <p class="text-gray-900 font-mono text-sm"><%= @account.id %></p>
              </div>
              <div>
                <span class="text-sm font-medium text-gray-700">Member Since:</span>
                <p class="text-gray-900"><%= @user.created_at.strftime("%B %d, %Y") %></p>
              </div>
              <div>
                <span class="text-sm font-medium text-gray-700">Last Updated:</span>
                <p class="text-gray-900"><%= @user.updated_at.strftime("%B %d, %Y at %I:%M %p") %></p>
              </div>
            </div>
          </div>
        </div>

        <!-- Security Section -->
        <div>
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Security</h2>
          <div class="space-y-4">
            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div>
                <h3 class="font-medium text-gray-900">Change Password</h3>
                <p class="text-sm text-gray-500">Update your password regularly to keep your account secure</p>
              </div>
              <a href="<%= edit_user_registration_path %>" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition">
                Change Password
              </a>
            </div>

            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div>
                <h3 class="font-medium text-gray-900">Two-Factor Authentication</h3>
                <p class="text-sm text-gray-500">Add an extra layer of security to your account</p>
              </div>
              <button type="button" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition">
                Coming Soon
              </button>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
          <button type="button" 
                  onclick="window.location.reload()" 
                  class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition">
            Cancel
          </button>
          
          <div class="flex items-center gap-4">
            <span class="text-sm text-gray-500" data-profile-manager-target="saveStatus"></span>
            <%= form.submit "Save Changes", 
                class: "px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition cursor-pointer",
                data: { "profile-manager-target": "submitButton" } %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>

<% if notice.present? %>
  <div class="fixed bottom-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded shadow-lg" 
       data-controller="notification">
    <span class="block sm:inline"><%= notice %></span>
  </div>
<% end %>

<% if alert.present? %>
  <div class="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded shadow-lg" 
       data-controller="notification">
    <span class="block sm:inline"><%= alert %></span>
  </div>
<% end %>