<% content_for :title, "System Monitoring Dashboard" %>
<% content_for :description, "Real-time performance metrics and capacity monitoring" %>

<div class="monitoring-dashboard">
  <div class="dashboard-header">
    <h1>System Performance Dashboard</h1>
    <div class="status-indicator status-<%= @health_status[:status] %>">
      <%= @health_status[:status].humanize %>
    </div>
  </div>

  <!-- Capacity Overview -->
  <div class="metrics-grid">
    <div class="metric-card capacity-card">
      <h3>System Capacity</h3>
      <div class="capacity-gauge">
        <div class="gauge-value"><%= @capacity_status[:capacity_used] %></div>
        <div class="gauge-label">Capacity Used</div>
      </div>
      <div class="capacity-details">
        <div class="detail">
          <span class="label">Active Users:</span>
          <span class="value"><%= @capacity_status[:active_users] %></span>
        </div>
        <div class="detail">
          <span class="label">Max Recommended:</span>
          <span class="value"><%= @capacity_status[:max_recommended_users] %></span>
        </div>
        <div class="detail">
          <span class="label">Available Headroom:</span>
          <span class="value"><%= @capacity_status[:max_recommended_users] - @capacity_status[:active_users] %> users</span>
        </div>
      </div>
    </div>

    <!-- Performance Metrics -->
    <div class="metric-card">
      <h3>Application Performance</h3>
      <div class="performance-metrics">
        <div class="metric">
          <span class="metric-value"><%= @metrics[:application][:avg_response_time] %>ms</span>
          <span class="metric-label">Avg Response Time</span>
          <span class="trend <%= @performance_trends[:response_time][:trend] %>">
            <%= @performance_trends[:response_time][:change_percent] %>%
          </span>
        </div>
        <div class="metric">
          <span class="metric-value"><%= @metrics[:application][:error_rate] %>%</span>
          <span class="metric-label">Error Rate</span>
          <span class="trend <%= @performance_trends[:error_rate][:trend] %>">
            <%= @performance_trends[:error_rate][:change_percent] %>%
          </span>
        </div>
        <div class="metric">
          <span class="metric-value"><%= time_ago_in_words(Time.current - @metrics[:application][:uptime].seconds) %></span>
          <span class="metric-label">Uptime</span>
        </div>
      </div>
    </div>

    <!-- Database Health -->
    <div class="metric-card">
      <h3>Database Performance</h3>
      <% if @health_status[:checks][:database][:status] == 'healthy' %>
        <div class="db-metrics">
          <div class="metric">
            <span class="metric-value"><%= @health_status[:checks][:database][:response_time] %></span>
            <span class="metric-label">Query Response Time</span>
          </div>
          <div class="metric">
            <span class="metric-value"><%= @health_status[:checks][:database][:connection_pool_usage] %>%</span>
            <span class="metric-label">Connection Pool Usage</span>
          </div>
          <div class="metric">
            <span class="metric-value"><%= @health_status[:checks][:database][:available_connections] %></span>
            <span class="metric-label">Available Connections</span>
          </div>
        </div>
      <% else %>
        <div class="error-state">
          <span class="error-message">Database connection issues detected</span>
        </div>
      <% end %>
    </div>

    <!-- System Resources -->
    <div class="metric-card">
      <h3>System Resources</h3>
      <div class="resource-metrics">
        <div class="resource-bar">
          <span class="resource-label">Memory Usage</span>
          <div class="progress-bar">
            <div class="progress-fill" style="width: <%= @metrics[:system][:memory_usage] / 10 %>%"></div>
          </div>
          <span class="resource-value"><%= @metrics[:system][:memory_usage].round(1) %>MB</span>
        </div>
        <div class="resource-bar">
          <span class="resource-label">Load Average</span>
          <div class="progress-bar">
            <div class="progress-fill" style="width: <%= [@metrics[:system][:load_average] * 20, 100].min %>%"></div>
          </div>
          <span class="resource-value"><%= @metrics[:system][:load_average] %></span>
        </div>
      </div>
    </div>
  </div>

  <!-- Health Checks -->
  <div class="health-checks-section">
    <h2>System Health Checks</h2>
    <div class="health-checks">
      <% @health_status[:checks].each do |component, status| %>
        <div class="health-check <%= status[:status] %>">
          <div class="check-name"><%= component.to_s.humanize %></div>
          <div class="check-status"><%= status[:status].humanize %></div>
          <% if status[:error] %>
            <div class="check-error"><%= status[:error] %></div>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Recent Alerts -->
  <% if @recent_alerts.any? %>
    <div class="alerts-section">
      <h2>Recent Alerts</h2>
      <div class="alerts">
        <% @recent_alerts.each do |alert| %>
          <div class="alert alert-<%= alert[:type] %> <%= alert[:status] %>">
            <div class="alert-time"><%= time_ago_in_words(alert[:timestamp]) %> ago</div>
            <div class="alert-message"><%= alert[:message] %></div>
            <div class="alert-status"><%= alert[:status] %></div>
          </div>
        <% end %>
      </div>
    </div>
  <% end %>

  <!-- Quick Actions -->
  <div class="actions-section">
    <h2>Quick Actions</h2>
    <div class="action-buttons">
      <a href="<%= admin_monitoring_capacity_path %>" class="btn btn-primary">View Detailed Capacity</a>
      <a href="<%= system_metrics_path %>" class="btn btn-secondary" target="_blank">Raw Metrics JSON</a>
      <a href="<%= admin_monitoring_prometheus_path %>" class="btn btn-secondary">Prometheus Metrics</a>
    </div>
  </div>
</div>

<style>
.monitoring-dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.dashboard-header h1 {
  margin: 0;
  color: #333;
}

.status-indicator {
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  text-transform: uppercase;
  font-size: 12px;
}

.status-healthy { background-color: #d4edda; color: #155724; }
.status-degraded { background-color: #fff3cd; color: #856404; }
.status-unhealthy { background-color: #f8d7da; color: #721c24; }

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.metric-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.metric-card h3 {
  margin: 0 0 20px 0;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.capacity-gauge {
  text-align: center;
  margin-bottom: 20px;
}

.gauge-value {
  font-size: 48px;
  font-weight: bold;
  color: #007bff;
}

.gauge-label {
  font-size: 14px;
  color: #666;
}

.capacity-details .detail {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.performance-metrics .metric,
.db-metrics .metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.metric-value {
  font-size: 18px;
  font-weight: bold;
  color: #007bff;
}

.trend.stable { color: #28a745; }
.trend.increasing { color: #dc3545; }
.trend.decreasing { color: #28a745; }

.resource-bar {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.resource-label {
  flex: 0 0 120px;
  font-size: 14px;
}

.progress-bar {
  flex: 1;
  height: 20px;
  background: #e9ecef;
  border-radius: 10px;
  margin: 0 10px;
  position: relative;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #007bff);
  border-radius: 10px;
  transition: width 0.3s ease;
}

.resource-value {
  flex: 0 0 80px;
  text-align: right;
  font-weight: bold;
}

.health-checks {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.health-check {
  padding: 15px;
  border-radius: 6px;
  text-align: center;
}

.health-check.healthy { background: #d4edda; color: #155724; }
.health-check.degraded { background: #fff3cd; color: #856404; }
.health-check.unhealthy { background: #f8d7da; color: #721c24; }

.check-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.alerts {
  space-y: 10px;
}

.alert {
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid;
}

.alert.resolved {
  opacity: 0.7;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: bold;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn:hover {
  opacity: 0.9;
}
</style>

<script>
// Auto-refresh the dashboard every 30 seconds
setTimeout(function() {
  window.location.reload();
}, 30000);
</script>