<div class="min-h-screen bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h1 class="text-3xl font-bold text-gray-900">Team Members</h1>
        <p class="mt-2 text-sm text-gray-700">
          Manage your team members and their roles.
          <% if @max_members > 0 %>
            (<%= @team_members.count %>/<%= @max_members %> members)
          <% else %>
            (<%= @team_members.count %> members)
          <% end %>
        </p>
      </div>
      <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
        <% if @can_invite %>
          <%= link_to new_team_member_path, 
              class: "block rounded-md bg-blue-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600" do %>
            Invite Member
          <% end %>
        <% else %>
          <div class="text-sm text-gray-500">
            <% if @max_members > 0 %>
              Team limit reached. 
              <%= link_to "Upgrade plan", subscription_path, class: "text-blue-600 hover:text-blue-800" %>
              to add more members.
            <% else %>
              <%= link_to "Upgrade plan", subscription_path, class: "text-blue-600 hover:text-blue-800" %>
              to add team members.
            <% end %>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Team Members Table -->
    <div class="mt-8 flow-root">
      <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
          <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
            <table class="min-w-full divide-y divide-gray-300">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-xs font-medium uppercase tracking-wide text-gray-500 sm:pl-6">
                    Member
                  </th>
                  <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium uppercase tracking-wide text-gray-500">
                    Role
                  </th>
                  <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium uppercase tracking-wide text-gray-500">
                    Joined
                  </th>
                  <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium uppercase tracking-wide text-gray-500">
                    Status
                  </th>
                  <% if current_user.owner? || current_user.admin? %>
                    <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                      <span class="sr-only">Actions</span>
                    </th>
                  <% end %>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 bg-white">
                <% @team_members.each do |member| %>
                  <tr>
                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                      <div class="flex items-center">
                        <div class="h-10 w-10 flex-shrink-0">
                          <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                            <span class="text-sm font-medium text-gray-700 uppercase">
                              <%= member.full_name.first %>
                            </span>
                          </div>
                        </div>
                        <div class="ml-4">
                          <div class="font-medium text-gray-900"><%= member.full_name %></div>
                          <div class="text-gray-500"><%= member.email %></div>
                        </div>
                      </div>
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset
                        <%= case member.role
                            when 'owner' then 'bg-purple-50 text-purple-700 ring-purple-600/20'
                            when 'admin' then 'bg-blue-50 text-blue-700 ring-blue-600/20'
                            when 'member' then 'bg-green-50 text-green-700 ring-green-600/20'
                            else 'bg-gray-50 text-gray-700 ring-gray-600/20'
                            end %>">
                        <%= member.role.humanize %>
                      </span>
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      <%= member.created_at.strftime('%b %d, %Y') %>
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      <span class="inline-flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                        Active
                      </span>
                    </td>
                    <% if current_user.owner? || current_user.admin? %>
                      <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                        <% unless member.owner? || member == current_user %>
                          <%= link_to edit_team_member_path(member), 
                              class: "text-blue-600 hover:text-blue-900 mr-4" do %>
                            Edit
                          <% end %>
                          <%= link_to team_member_path(member), 
                              method: :delete,
                              confirm: "Are you sure you want to remove #{member.full_name} from the team?",
                              class: "text-red-600 hover:text-red-900" do %>
                            Remove
                          <% end %>
                        <% end %>
                      </td>
                    <% end %>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Pending Invitations -->
    <% if @pending_invitations.any? %>
      <div class="mt-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Pending Invitations</h2>
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-xs font-medium uppercase tracking-wide text-gray-500 sm:pl-6">
                  Email
                </th>
                <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium uppercase tracking-wide text-gray-500">
                  Role
                </th>
                <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium uppercase tracking-wide text-gray-500">
                  Invited By
                </th>
                <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium uppercase tracking-wide text-gray-500">
                  Expires
                </th>
                <% if current_user.owner? || current_user.admin? %>
                  <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                    <span class="sr-only">Actions</span>
                  </th>
                <% end %>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 bg-white">
              <% @pending_invitations.each do |invitation| %>
                <tr>
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                    <%= invitation.email %>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    <span class="inline-flex items-center rounded-md bg-yellow-50 px-2 py-1 text-xs font-medium text-yellow-700 ring-1 ring-inset ring-yellow-600/20">
                      <%= invitation.role.humanize %>
                    </span>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    <%= invitation.invited_by.full_name %>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    <%= invitation.expires_at.strftime('%b %d, %Y') %>
                  </td>
                  <% if current_user.owner? || current_user.admin? %>
                    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                      <%= link_to "#", 
                          confirm: "Are you sure you want to cancel this invitation?",
                          class: "text-red-600 hover:text-red-900" do %>
                        Cancel
                      <% end %>
                    </td>
                  <% end %>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    <% end %>
  </div>
</div>
