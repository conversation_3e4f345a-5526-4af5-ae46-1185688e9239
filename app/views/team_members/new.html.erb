<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
      Invite Team Member
    </h2>
    <p class="mt-2 text-center text-sm text-gray-600">
      Send an invitation to join your team
    </p>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
      <%= form_with model: [@team_invitation], url: team_members_path, local: true, class: "space-y-6" do |f| %>
        <% if @team_invitation.errors.any? %>
          <div class="rounded-md bg-red-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  There were errors with your invitation:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @team_invitation.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div>
          <%= f.label :email, class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= f.email_field :email, 
                placeholder: "<EMAIL>",
                required: true,
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" %>
          </div>
        </div>

        <div>
          <%= f.label :role, class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= f.select :role, 
                options_for_select([
                  ['Viewer - Can view data and reports', 'viewer'],
                  ['Member - Can create and edit pipelines', 'member'],
                  ['Admin - Can manage team and settings', 'admin']
                ], @team_invitation.role),
                { prompt: 'Select a role' },
                { class: "block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
          </div>
        </div>

        <!-- Role Descriptions -->
        <div class="bg-gray-50 rounded-md p-4">
          <h4 class="text-sm font-medium text-gray-900 mb-2">Role Permissions:</h4>
          <div class="text-xs text-gray-600 space-y-1">
            <div><strong>Viewer:</strong> Read-only access to dashboards and reports</div>
            <div><strong>Member:</strong> Create, edit, and manage data pipelines and connections</div>
            <div><strong>Admin:</strong> Full access including team management and billing</div>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <%= link_to team_members_path, 
              class: "text-sm text-gray-500 hover:text-gray-700" do %>
            ← Back to Team
          <% end %>
          
          <%= f.submit "Send Invitation", 
              class: "group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        </div>
      <% end %>
    </div>
  </div>
</div>
