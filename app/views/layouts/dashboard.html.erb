<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Dashboard - DataReflow" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="bg-gray-50 font-sans antialiased">
    <!-- Skip to main content link for keyboard users -->
    <a href="#main-content"
       class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-indigo-600 text-white px-4 py-2 rounded-md z-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
      Skip to main content
    </a>

    <div class="min-h-screen flex" data-controller="navigation">
      <!-- Sidebar -->
      <div class="hidden lg:flex lg:flex-shrink-0">
        <div class="flex flex-col w-64">
          <%= render 'shared/sidebar' %>
        </div>
      </div>

      <!-- Mobile sidebar overlay -->
      <div class="lg:hidden fixed inset-0 z-40 flex opacity-0 transition-opacity duration-300"
           data-navigation-target="mobileOverlay"
           data-action="click->navigation#closeMobile"
           style="display: none;">
        <div class="fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <div class="relative flex-1 flex flex-col max-w-xs w-full bg-white transform -translate-x-full transition-transform duration-300 ease-in-out">
          <%= render 'shared/sidebar' %>
        </div>
      </div>

      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top navigation -->
        <%= render 'shared/header' %>

        <!-- Page content -->
        <main id="main-content"
              class="flex-1 relative overflow-y-auto focus:outline-none"
              role="main"
              aria-label="Main content area">
          <div class="py-6">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <%= render 'shared/flash_messages' if notice || alert %>
              <%= yield %>
            </div>
          </div>
        </main>
      </div>
    </div>
  </body>
</html>
