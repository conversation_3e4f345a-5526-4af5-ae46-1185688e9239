<% content_for :page_title, "Add New Connection" %>

<!-- Enhanced Styles for the Form -->
<style>
  .wizard-step { display: none; }
  .wizard-step.active { display: block; }
  .step-indicator.completed { background-color: #10b981; color: white; }
  .step-indicator.active { background-color: #3b82f6; color: white; }
  .step-indicator { background-color: #e5e7eb; color: #6b7280; }
  .connection-type-card { transition: all 0.2s ease; }
  .connection-type-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
  .connection-type-card.selected { border-color: #3b82f6; background-color: #eff6ff; }
  .form-field-error { border-color: #ef4444; background-color: #fef2f2; }
  .form-field-success { border-color: #10b981; background-color: #f0fdf4; }
  .loading-spinner { animation: spin 1s linear infinite; }
  @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
  .fade-in { animation: fadeIn 0.3s ease-in; }
  @keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
</style>

<div class="max-w-6xl mx-auto" data-controller="connection-wizard" data-connection-wizard-current-step-value="1">
  <!-- Enhanced Header with Breadcrumbs -->
  <div class="mb-8">
    <!-- Breadcrumb Navigation -->
    <nav class="flex mb-4" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        <% if @project %>
          <li class="inline-flex items-center">
            <%= link_to projects_path, class: "inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600" do %>
              <svg class="mr-2 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L9 5.414V17a1 1 0 102 0V5.414l5.293 5.293a1 1 0 001.414-1.414l-7-7z"/>
              </svg>
              Projects
            <% end %>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <%= link_to @project, class: "ml-1 text-sm font-medium text-gray-700 hover:text-indigo-600 md:ml-2" do %>
                <%= @project.name %>
              <% end %>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <%= link_to project_data_connectors_path(@project), class: "ml-1 text-sm font-medium text-gray-700 hover:text-indigo-600 md:ml-2" do %>
                Connectors
              <% end %>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Add Connection</span>
            </div>
          </li>
        <% else %>
          <li class="inline-flex items-center">
            <%= link_to data_connectors_path, class: "inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600" do %>
              <svg class="mr-2 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L9 5.414V17a1 1 0 102 0V5.414l5.293 5.293a1 1 0 001.414-1.414l-7-7z"/>
              </svg>
              Connections
            <% end %>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Add Connection</span>
            </div>
          </li>
        <% end %>
      </ol>
    </nav>

    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">
          <% if @project %>
            Add Connection to <%= @project.name %>
          <% else %>
            Add New Connection
          <% end %>
        </h1>
        <p class="mt-2 text-lg text-gray-600">
          <% if @project %>
            Connect a new data source or destination to the <%= @project.name %> project
          <% else %>
            Connect to your data sources and destinations with our step-by-step wizard
          <% end %>
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <!-- Auto-save indicator -->
        <div class="hidden" data-connection-wizard-target="autoSaveIndicator">
          <div class="flex items-center text-sm text-green-600">
            <svg class="mr-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
            Auto-saved
          </div>
        </div>
        <% back_path = @project ? project_data_connectors_path(@project) : data_connectors_path %>
        <%= link_to back_path,
            class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200" do %>
          <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          Back to Connections
        <% end %>
      </div>
    </div>
  </div>

  <!-- Progress Indicator -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <div class="flex items-center">
          <div class="step-indicator active flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium transition-colors duration-200" data-connection-wizard-target="stepIndicator" data-step="1">
            1
          </div>
          <span class="ml-2 text-sm font-medium text-gray-900">Connection Type</span>
        </div>
        <div class="flex-1 h-0.5 bg-gray-200 mx-4"></div>
        <div class="flex items-center">
          <div class="step-indicator flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium transition-colors duration-200" data-connection-wizard-target="stepIndicator" data-step="2">
            2
          </div>
          <span class="ml-2 text-sm font-medium text-gray-500">Basic Information</span>
        </div>
        <div class="flex-1 h-0.5 bg-gray-200 mx-4"></div>
        <div class="flex items-center">
          <div class="step-indicator flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium transition-colors duration-200" data-connection-wizard-target="stepIndicator" data-step="3">
            3
          </div>
          <span class="ml-2 text-sm font-medium text-gray-500">Configuration</span>
        </div>
        <div class="flex-1 h-0.5 bg-gray-200 mx-4"></div>
        <div class="flex items-center">
          <div class="step-indicator flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium transition-colors duration-200" data-connection-wizard-target="stepIndicator" data-step="4">
            4
          </div>
          <span class="ml-2 text-sm font-medium text-gray-500">Test & Save</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Connection Creation Form with Wizard -->
  <div class="bg-white shadow-xl rounded-xl border border-gray-200">
    <% form_url = @project ? project_data_connectors_path(@project) : data_connectors_path %>
    <%= form_with model: [@project, @data_connector].compact,
        url: form_url,
        local: true,
        class: "connection-form",
        data: {
          controller: "form-validation auto-save",
          form_validation_target: "form",
          auto_save_url: "#",
          auto_save_interval: 30000
        } do |form| %>

      <!-- Step 1: Connection Type Selection -->
      <div class="wizard-step active fade-in" data-connection-wizard-target="step" data-step="1">
        <div class="p-8">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Choose Connection Type</h2>
            <p class="text-gray-600">Select the type of data source or destination you want to connect to</p>
          </div>

          <!-- Connection Type Cards -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- Database Connections -->
            <div class="connection-type-card border-2 border-gray-200 rounded-xl p-6 cursor-pointer hover:border-indigo-300 transition-all duration-200"
                 data-action="click->connection-wizard#selectConnectionType"
                 data-type="database">
              <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Database</h3>
                <p class="text-sm text-gray-600 mb-4">PostgreSQL, MySQL, MongoDB, Redis</p>
                <div class="flex flex-wrap gap-1 justify-center">
                  <span class="px-2 py-1 bg-gray-100 text-xs rounded">PostgreSQL</span>
                  <span class="px-2 py-1 bg-gray-100 text-xs rounded">MySQL</span>
                  <span class="px-2 py-1 bg-gray-100 text-xs rounded">MongoDB</span>
                </div>
              </div>
            </div>

            <!-- Cloud Storage -->
            <div class="connection-type-card border-2 border-gray-200 rounded-xl p-6 cursor-pointer hover:border-indigo-300 transition-all duration-200"
                 data-action="click->connection-wizard#selectConnectionType"
                 data-type="cloud_storage">
              <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Cloud Storage</h3>
                <p class="text-sm text-gray-600 mb-4">AWS S3, Google Cloud, Azure</p>
                <div class="flex flex-wrap gap-1 justify-center">
                  <span class="px-2 py-1 bg-gray-100 text-xs rounded">AWS S3</span>
                  <span class="px-2 py-1 bg-gray-100 text-xs rounded">GCS</span>
                  <span class="px-2 py-1 bg-gray-100 text-xs rounded">Azure</span>
                </div>
              </div>
            </div>

            <!-- APIs -->
            <div class="connection-type-card border-2 border-gray-200 rounded-xl p-6 cursor-pointer hover:border-indigo-300 transition-all duration-200"
                 data-action="click->connection-wizard#selectConnectionType"
                 data-type="api">
              <div class="text-center">
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">API</h3>
                <p class="text-sm text-gray-600 mb-4">REST, GraphQL, Webhooks</p>
                <div class="flex flex-wrap gap-1 justify-center">
                  <span class="px-2 py-1 bg-gray-100 text-xs rounded">REST API</span>
                  <span class="px-2 py-1 bg-gray-100 text-xs rounded">GraphQL</span>
                  <span class="px-2 py-1 bg-gray-100 text-xs rounded">Webhook</span>
                </div>
              </div>
            </div>

            <!-- File Systems -->
            <div class="connection-type-card border-2 border-gray-200 rounded-xl p-6 cursor-pointer hover:border-indigo-300 transition-all duration-200"
                 data-action="click->connection-wizard#selectConnectionType"
                 data-type="file_system">
              <div class="text-center">
                <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">File System</h3>
                <p class="text-sm text-gray-600 mb-4">FTP, SFTP, Local Files</p>
                <div class="flex flex-wrap gap-1 justify-center">
                  <span class="px-2 py-1 bg-gray-100 text-xs rounded">FTP</span>
                  <span class="px-2 py-1 bg-gray-100 text-xs rounded">SFTP</span>
                  <span class="px-2 py-1 bg-gray-100 text-xs rounded">Local</span>
                </div>
              </div>
            </div>

            <!-- Data Warehouses -->
            <div class="connection-type-card border-2 border-gray-200 rounded-xl p-6 cursor-pointer hover:border-indigo-300 transition-all duration-200"
                 data-action="click->connection-wizard#selectConnectionType"
                 data-type="data_warehouse">
              <div class="text-center">
                <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Data Warehouse</h3>
                <p class="text-sm text-gray-600 mb-4">Snowflake, BigQuery, Redshift</p>
                <div class="flex flex-wrap gap-1 justify-center">
                  <span class="px-2 py-1 bg-gray-100 text-xs rounded">Snowflake</span>
                  <span class="px-2 py-1 bg-gray-100 text-xs rounded">BigQuery</span>
                  <span class="px-2 py-1 bg-gray-100 text-xs rounded">Redshift</span>
                </div>
              </div>
            </div>

            <!-- Custom/Other -->
            <div class="connection-type-card border-2 border-gray-200 rounded-xl p-6 cursor-pointer hover:border-indigo-300 transition-all duration-200"
                 data-action="click->connection-wizard#selectConnectionType"
                 data-type="custom">
              <div class="text-center">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Custom</h3>
                <p class="text-sm text-gray-600 mb-4">Custom protocols and integrations</p>
                <div class="flex flex-wrap gap-1 justify-center">
                  <span class="px-2 py-1 bg-gray-100 text-xs rounded">Custom</span>
                  <span class="px-2 py-1 bg-gray-100 text-xs rounded">Other</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Hidden field to store selected connection type -->
          <%= form.hidden_field :connector_type, data: { connection_wizard_target: "connectorTypeField" } %>
        </div>
      </div>
      <!-- Step 2: Basic Information -->
      <div class="wizard-step fade-in" data-connection-wizard-target="step" data-step="2">
        <div class="p-8">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Basic Information</h2>
            <p class="text-gray-600">Provide basic details about your connection</p>
          </div>

          <div class="max-w-2xl mx-auto space-y-6">
            <!-- Connection Name with Smart Suggestions -->
            <div class="space-y-2">
              <%= form.label :name, class: "block text-sm font-medium text-gray-700" do %>
                Connection Name
                <span class="text-red-500">*</span>
                <button type="button" class="ml-1 text-gray-400 hover:text-gray-600" data-tooltip="Choose a descriptive name that helps identify this connection">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                  </svg>
                </button>
              <% end %>
              <div class="relative">
                <%= form.text_field :name,
                    class: "block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors duration-200",
                    placeholder: "e.g., Production Database",
                    required: true,
                    data: {
                      action: "input->form-validation#validateField blur->auto-save#saveField",
                      form_validation_target: "field",
                      validation_rules: "required|min:3|max:100"
                    } %>
                <!-- Real-time validation feedback -->
                <div class="hidden mt-1 text-sm text-red-600" data-form-validation-target="error" data-field="name">
                  <svg class="inline w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                  </svg>
                  <span data-form-validation-target="errorMessage"></span>
                </div>
                <div class="hidden mt-1 text-sm text-green-600" data-form-validation-target="success" data-field="name">
                  <svg class="inline w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                  </svg>
                  Looks good!
                </div>
              </div>
              <!-- Smart suggestions -->
              <div class="hidden" data-connection-wizard-target="nameSuggestions">
                <p class="text-xs text-gray-500 mb-2">Suggested names:</p>
                <div class="flex flex-wrap gap-2">
                  <button type="button" class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-full transition-colors duration-200" data-action="click->connection-wizard#applySuggestion" data-field="name" data-value="Production Database">
                    Production Database
                  </button>
                  <button type="button" class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-full transition-colors duration-200" data-action="click->connection-wizard#applySuggestion" data-field="name" data-value="Analytics Warehouse">
                    Analytics Warehouse
                  </button>
                  <button type="button" class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-full transition-colors duration-200" data-action="click->connection-wizard#applySuggestion" data-field="name" data-value="Customer Data API">
                    Customer Data API
                  </button>
                </div>
              </div>
            </div>

            <!-- Connection Description with Character Counter -->
            <div class="space-y-2">
              <%= form.label :description, class: "block text-sm font-medium text-gray-700" do %>
                Description
                <span class="text-gray-400">(Optional)</span>
              <% end %>
              <div class="relative">
                <%= form.text_area :description,
                    rows: 4,
                    maxlength: 500,
                    class: "block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors duration-200",
                    placeholder: "Describe this connection and its purpose...",
                    data: {
                      action: "input->form-validation#updateCharacterCount input->auto-save#saveField",
                      form_validation_target: "field"
                    } %>
                <div class="absolute bottom-2 right-2 text-xs text-gray-400" data-form-validation-target="characterCount" data-max="500">
                  0/500
                </div>
              </div>
            </div>

            <!-- Direction Selection with Visual Icons -->
            <div class="space-y-2">
              <%= form.label :direction, class: "block text-sm font-medium text-gray-700" do %>
                Connection Direction
                <span class="text-red-500">*</span>
              <% end %>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <label class="relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-colors duration-200">
                  <%= form.radio_button :direction, "source",
                      class: "sr-only",
                      data: { action: "change->form-validation#validateField" } %>
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"/>
                      </svg>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-900">Source</div>
                      <div class="text-xs text-gray-500">Read data from</div>
                    </div>
                  </div>
                  <div class="absolute inset-0 border-2 border-transparent rounded-lg pointer-events-none transition-colors duration-200"></div>
                </label>

                <label class="relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-colors duration-200">
                  <%= form.radio_button :direction, "destination",
                      class: "sr-only",
                      data: { action: "change->form-validation#validateField" } %>
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                      </svg>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-900">Destination</div>
                      <div class="text-xs text-gray-500">Write data to</div>
                    </div>
                  </div>
                  <div class="absolute inset-0 border-2 border-transparent rounded-lg pointer-events-none transition-colors duration-200"></div>
                </label>

                <label class="relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-colors duration-200">
                  <%= form.radio_button :direction, "both",
                      class: "sr-only",
                      data: { action: "change->form-validation#validateField" } %>
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                      <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"/>
                      </svg>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-900">Both</div>
                      <div class="text-xs text-gray-500">Read & Write</div>
                    </div>
                  </div>
                  <div class="absolute inset-0 border-2 border-transparent rounded-lg pointer-events-none transition-colors duration-200"></div>
                </label>
              </div>
            </div>

            <!-- Tags/Labels -->
            <div class="space-y-2">
              <%= form.label :tags, class: "block text-sm font-medium text-gray-700" do %>
                Tags
                <span class="text-gray-400">(Optional)</span>
              <% end %>
              <div class="relative">
                <%= form.text_field :tags,
                    class: "block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    placeholder: "production, analytics, customer-data",
                    data: { action: "input->auto-save#saveField" } %>
                <p class="mt-1 text-xs text-gray-500">Separate tags with commas to help organize your connections</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 3: Configuration -->
      <div class="wizard-step fade-in" data-connection-wizard-target="step" data-step="3">
        <div class="p-8">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Connection Configuration</h2>
            <p class="text-gray-600">Configure the technical details for your connection</p>
          </div>

          <div class="max-w-4xl mx-auto">
            <!-- Dynamic Configuration Based on Connection Type -->
            <div data-connection-wizard-target="configurationSection">

              <!-- Database Configuration -->
              <div class="hidden" data-connection-wizard-target="databaseConfig">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <!-- Specific Database Type -->
                  <div class="md:col-span-2">
                    <%= form.label :connector_type, class: "block text-sm font-medium text-gray-700 mb-2" do %>
                      Database Type
                      <span class="text-red-500">*</span>
                    <% end %>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                      <label class="relative flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-colors duration-200">
                        <%= form.radio_button :connector_type, "postgresql", class: "sr-only" %>
                        <div class="text-center w-full">
                          <div class="text-sm font-medium text-gray-900">PostgreSQL</div>
                        </div>
                      </label>
                      <label class="relative flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-colors duration-200">
                        <%= form.radio_button :connector_type, "mysql", class: "sr-only" %>
                        <div class="text-center w-full">
                          <div class="text-sm font-medium text-gray-900">MySQL</div>
                        </div>
                      </label>
                      <label class="relative flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-colors duration-200">
                        <%= form.radio_button :connector_type, "mongodb", class: "sr-only" %>
                        <div class="text-center w-full">
                          <div class="text-sm font-medium text-gray-900">MongoDB</div>
                        </div>
                      </label>
                      <label class="relative flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-colors duration-200">
                        <%= form.radio_button :connector_type, "redis", class: "sr-only" %>
                        <div class="text-center w-full">
                          <div class="text-sm font-medium text-gray-900">Redis</div>
                        </div>
                      </label>
                    </div>
                  </div>

                  <!-- Host -->
                  <div>
                    <%= form.label :host, class: "block text-sm font-medium text-gray-700" do %>
                      Host/Server
                      <span class="text-red-500">*</span>
                    <% end %>
                    <%= form.text_field :host,
                        class: "mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                        placeholder: "localhost or server.example.com",
                        data: {
                          action: "input->form-validation#validateField",
                          validation_rules: "required|host"
                        } %>
                  </div>

                  <!-- Port -->
                  <div>
                    <%= form.label :port, class: "block text-sm font-medium text-gray-700" do %>
                      Port
                    <% end %>
                    <%= form.number_field :port,
                        class: "mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                        placeholder: "5432",
                        min: 1,
                        max: 65535 %>
                  </div>

                  <!-- Database Name -->
                  <div>
                    <%= form.label :database, class: "block text-sm font-medium text-gray-700" do %>
                      Database Name
                      <span class="text-red-500">*</span>
                    <% end %>
                    <%= form.text_field :database,
                        class: "mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                        placeholder: "production_db",
                        data: { validation_rules: "required" } %>
                  </div>

                  <!-- Username -->
                  <div>
                    <%= form.label :username, class: "block text-sm font-medium text-gray-700" do %>
                      Username
                      <span class="text-red-500">*</span>
                    <% end %>
                    <%= form.text_field :username,
                        class: "mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                        placeholder: "db_user",
                        data: { validation_rules: "required" } %>
                  </div>

                  <!-- Password -->
                  <div class="md:col-span-2">
                    <%= form.label :password, class: "block text-sm font-medium text-gray-700" do %>
                      Password
                      <span class="text-red-500">*</span>
                    <% end %>
                    <div class="relative">
                      <%= form.password_field :password,
                          class: "mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm pr-10",
                          placeholder: "Enter password",
                          data: { validation_rules: "required" } %>
                      <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" data-action="click->form-validation#togglePassword">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                        </svg>
                      </button>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">
                      <svg class="inline w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                      </svg>
                      Password is encrypted and stored securely
                    </p>
                  </div>
                </div>
              </div>

              <!-- Cloud Storage Configuration -->
              <div class="hidden" data-connection-wizard-target="cloudStorageConfig">
                <div class="space-y-6">
                  <!-- Cloud Provider -->
                  <div>
                    <%= form.label :connector_type, class: "block text-sm font-medium text-gray-700 mb-3" do %>
                      Cloud Provider
                      <span class="text-red-500">*</span>
                    <% end %>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <label class="relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-colors duration-200">
                        <%= form.radio_button :connector_type, "s3", class: "sr-only" %>
                        <div class="flex items-center space-x-3">
                          <div class="w-8 h-8 bg-orange-100 rounded flex items-center justify-center">
                            <span class="text-orange-600 font-bold text-xs">AWS</span>
                          </div>
                          <div>
                            <div class="text-sm font-medium text-gray-900">Amazon S3</div>
                            <div class="text-xs text-gray-500">AWS Storage</div>
                          </div>
                        </div>
                      </label>
                      <label class="relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-colors duration-200">
                        <%= form.radio_button :connector_type, "gcs", class: "sr-only" %>
                        <div class="flex items-center space-x-3">
                          <div class="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                            <span class="text-blue-600 font-bold text-xs">GCP</span>
                          </div>
                          <div>
                            <div class="text-sm font-medium text-gray-900">Google Cloud</div>
                            <div class="text-xs text-gray-500">GCS Storage</div>
                          </div>
                        </div>
                      </label>
                      <label class="relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-colors duration-200">
                        <%= form.radio_button :connector_type, "azure", class: "sr-only" %>
                        <div class="flex items-center space-x-3">
                          <div class="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                            <span class="text-blue-600 font-bold text-xs">AZ</span>
                          </div>
                          <div>
                            <div class="text-sm font-medium text-gray-900">Azure Blob</div>
                            <div class="text-xs text-gray-500">Azure Storage</div>
                          </div>
                        </div>
                      </label>
                    </div>
                  </div>

                  <!-- Configuration Upload -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Configuration
                      <span class="text-red-500">*</span>
                    </label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-indigo-400 transition-colors duration-200"
                         data-controller="file-upload"
                         data-action="dragover->file-upload#dragOver dragleave->file-upload#dragLeave drop->file-upload#drop">
                      <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                      <div class="mt-4">
                        <label class="cursor-pointer">
                          <span class="mt-2 block text-sm font-medium text-gray-900">
                            Upload credentials file or enter manually
                          </span>
                          <input type="file" class="sr-only" accept=".json,.yaml,.yml" data-file-upload-target="input">
                        </label>
                        <p class="mt-1 text-xs text-gray-500">JSON, YAML up to 10MB</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- API Configuration -->
              <div class="hidden" data-connection-wizard-target="apiConfig">
                <div class="space-y-6">
                  <!-- API Type -->
                  <div>
                    <%= form.label :connector_type, class: "block text-sm font-medium text-gray-700 mb-3" do %>
                      API Type
                      <span class="text-red-500">*</span>
                    <% end %>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <label class="relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-colors duration-200">
                        <%= form.radio_button :connector_type, "rest_api", class: "sr-only" %>
                        <div class="text-center w-full">
                          <div class="text-sm font-medium text-gray-900">REST API</div>
                          <div class="text-xs text-gray-500">HTTP/HTTPS</div>
                        </div>
                      </label>
                      <label class="relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-colors duration-200">
                        <%= form.radio_button :connector_type, "graphql", class: "sr-only" %>
                        <div class="text-center w-full">
                          <div class="text-sm font-medium text-gray-900">GraphQL</div>
                          <div class="text-xs text-gray-500">Query Language</div>
                        </div>
                      </label>
                      <label class="relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-colors duration-200">
                        <%= form.radio_button :connector_type, "webhook", class: "sr-only" %>
                        <div class="text-center w-full">
                          <div class="text-sm font-medium text-gray-900">Webhook</div>
                          <div class="text-xs text-gray-500">Event-driven</div>
                        </div>
                      </label>
                    </div>
                  </div>

                  <!-- API Configuration -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Base URL -->
                    <div class="md:col-span-2">
                      <%= form.label :host, class: "block text-sm font-medium text-gray-700" do %>
                        Base URL
                        <span class="text-red-500">*</span>
                      <% end %>
                      <%= form.url_field :host,
                          class: "mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                          placeholder: "https://api.example.com/v1",
                          data: { validation_rules: "required|url" } %>
                    </div>

                    <!-- Authentication Method -->
                    <div>
                      <%= form.label :auth_method, class: "block text-sm font-medium text-gray-700" do %>
                        Authentication
                      <% end %>
                      <%= form.select :auth_method,
                          options_for_select([
                            ['None', 'none'],
                            ['API Key', 'api_key'],
                            ['Bearer Token', 'bearer'],
                            ['Basic Auth', 'basic'],
                            ['OAuth 2.0', 'oauth2']
                          ]),
                          { prompt: 'Select authentication method...' },
                          { class: "mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" } %>
                    </div>

                    <!-- API Key/Token -->
                    <div>
                      <%= form.label :password, class: "block text-sm font-medium text-gray-700" do %>
                        API Key/Token
                      <% end %>
                      <%= form.password_field :password,
                          class: "mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                          placeholder: "Enter API key or token" %>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Default Configuration Message -->
              <div data-connection-wizard-target="defaultConfig">
                <div class="text-center py-12">
                  <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                  </svg>
                  <h3 class="mt-2 text-sm font-medium text-gray-900">Select a connection type</h3>
                  <p class="mt-1 text-sm text-gray-500">
                    Choose a connection type from Step 1 to see configuration options
                  </p>
                </div>
              </div>
            </div>

            <!-- Advanced Settings -->
            <div class="mt-8 border-t border-gray-200 pt-8">
              <h3 class="text-lg font-medium text-gray-900 mb-6">Advanced Settings</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- SSL/TLS -->
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div>
                    <div class="text-sm font-medium text-gray-900">SSL/TLS Encryption</div>
                    <div class="text-xs text-gray-500">Secure connection with encryption</div>
                  </div>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <%= form.check_box :ssl_enabled, class: "sr-only peer", checked: true %>
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                  </label>
                </div>

                <!-- Connection Pooling -->
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div>
                    <div class="text-sm font-medium text-gray-900">Connection Pooling</div>
                    <div class="text-xs text-gray-500">Reuse connections for better performance</div>
                  </div>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <%= form.check_box :pool_enabled, class: "sr-only peer", checked: true %>
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                  </label>
                </div>

                <!-- Timeout -->
                <div>
                  <%= form.label :timeout, class: "block text-sm font-medium text-gray-700" do %>
                    Connection Timeout (seconds)
                  <% end %>
                  <%= form.range_field :timeout,
                      min: 5, max: 300, value: 30,
                      class: "mt-1 block w-full",
                      data: { action: "input->form-validation#updateRangeValue" } %>
                  <div class="flex justify-between text-xs text-gray-500 mt-1">
                    <span>5s</span>
                    <span data-form-validation-target="rangeValue">30s</span>
                    <span>300s</span>
                  </div>
                </div>

                <!-- Retry Attempts -->
                <div>
                  <%= form.label :retry_attempts, class: "block text-sm font-medium text-gray-700" do %>
                    Retry Attempts
                  <% end %>
                  <%= form.number_field :retry_attempts,
                      min: 0, max: 10, value: 3,
                      class: "mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 4: Test & Save -->
      <div class="wizard-step fade-in" data-connection-wizard-target="step" data-step="4">
        <div class="p-8">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Test & Save Connection</h2>
            <p class="text-gray-600">Verify your connection works and save it to your account</p>
          </div>

          <div class="max-w-2xl mx-auto space-y-8">
            <!-- Connection Summary -->
            <div class="bg-gray-50 rounded-xl p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Connection Summary</h3>
              <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <dt class="text-sm font-medium text-gray-500">Name</dt>
                  <dd class="text-sm text-gray-900" data-connection-wizard-target="summaryName">-</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Type</dt>
                  <dd class="text-sm text-gray-900" data-connection-wizard-target="summaryType">-</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Direction</dt>
                  <dd class="text-sm text-gray-900" data-connection-wizard-target="summaryDirection">-</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Host</dt>
                  <dd class="text-sm text-gray-900" data-connection-wizard-target="summaryHost">-</dd>
                </div>
              </dl>
            </div>

            <!-- Connection Test -->
            <div class="border-2 border-gray-200 rounded-xl p-6">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Connection Test</h3>
                <button type="button"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                        data-action="click->connection-wizard#testConnection"
                        data-connection-wizard-target="testButton">
                  <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  Test Connection
                </button>
              </div>

              <!-- Test Results -->
              <div class="hidden" data-connection-wizard-target="testResults">
                <!-- Success State -->
                <div class="hidden p-4 bg-green-50 border border-green-200 rounded-lg" data-connection-wizard-target="testSuccess">
                  <div class="flex items-center">
                    <svg class="h-5 w-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    <div>
                      <h4 class="text-sm font-medium text-green-800">Connection Successful!</h4>
                      <p class="text-sm text-green-700 mt-1">Your connection is working properly and ready to use.</p>
                    </div>
                  </div>
                  <div class="mt-3 text-xs text-green-600" data-connection-wizard-target="testDetails">
                    <!-- Test details will be populated here -->
                  </div>
                </div>

                <!-- Error State -->
                <div class="hidden p-4 bg-red-50 border border-red-200 rounded-lg" data-connection-wizard-target="testError">
                  <div class="flex items-center">
                    <svg class="h-5 w-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                    </svg>
                    <div>
                      <h4 class="text-sm font-medium text-red-800">Connection Failed</h4>
                      <p class="text-sm text-red-700 mt-1" data-connection-wizard-target="testErrorMessage">
                        Unable to establish connection. Please check your configuration.
                      </p>
                    </div>
                  </div>
                  <div class="mt-3">
                    <button type="button"
                            class="text-sm text-red-600 hover:text-red-500 font-medium"
                            data-action="click->connection-wizard#showTestDetails">
                      View detailed error →
                    </button>
                  </div>
                </div>

                <!-- Loading State -->
                <div class="hidden p-4 bg-blue-50 border border-blue-200 rounded-lg" data-connection-wizard-target="testLoading">
                  <div class="flex items-center">
                    <svg class="animate-spin h-5 w-5 text-blue-400 mr-2" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <div>
                      <h4 class="text-sm font-medium text-blue-800">Testing Connection...</h4>
                      <p class="text-sm text-blue-700 mt-1">Please wait while we verify your connection settings.</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Test Placeholder -->
              <div class="text-center py-8 text-gray-500" data-connection-wizard-target="testPlaceholder">
                <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
                <p class="text-sm">Click "Test Connection" to verify your settings</p>
              </div>
            </div>

            <!-- Save Options -->
            <div class="border-2 border-gray-200 rounded-xl p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Save Options</h3>
              <div class="space-y-4">
                <!-- Save and Activate -->
                <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200">
                  <%= form.radio_button :save_option, "save_and_activate", class: "sr-only", checked: true %>
                  <div class="flex items-center space-x-3">
                    <div class="w-4 h-4 border-2 border-indigo-600 rounded-full flex items-center justify-center">
                      <div class="w-2 h-2 bg-indigo-600 rounded-full"></div>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-900">Save and Activate</div>
                      <div class="text-xs text-gray-500">Connection will be ready to use immediately</div>
                    </div>
                  </div>
                </label>

                <!-- Save as Draft -->
                <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200">
                  <%= form.radio_button :save_option, "save_as_draft", class: "sr-only" %>
                  <div class="flex items-center space-x-3">
                    <div class="w-4 h-4 border-2 border-gray-300 rounded-full"></div>
                    <div>
                      <div class="text-sm font-medium text-gray-900">Save as Draft</div>
                      <div class="text-xs text-gray-500">Save configuration but don't activate yet</div>
                    </div>
                  </div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Wizard Navigation -->
      <div class="px-8 py-6 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
        <button type="button"
                class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                data-action="click->connection-wizard#previousStep"
                data-connection-wizard-target="prevButton"
                disabled>
          <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          Previous
        </button>

        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-500">Step</span>
          <span class="text-sm font-medium text-gray-900" data-connection-wizard-target="currentStepText">1</span>
          <span class="text-sm text-gray-500">of 4</span>
        </div>

        <div class="flex items-center space-x-3">
          <button type="button"
                  class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                  data-action="click->connection-wizard#nextStep"
                  data-connection-wizard-target="nextButton">
            Next
            <svg class="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"/>
            </svg>
          </button>

          <%= form.submit "Create Connection",
              class: "hidden inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200",
              data: { connection_wizard_target: "submitButton" } %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Enhanced Help & Resources Section -->
  <div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Quick Help -->
    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <svg class="h-6 w-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-lg font-medium text-blue-900 mb-3">Quick Setup Tips</h3>
          <div class="space-y-2 text-sm text-blue-800">
            <div class="flex items-start">
              <svg class="mt-0.5 mr-2 h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              </svg>
              <span>Test connections before saving to avoid issues</span>
            </div>
            <div class="flex items-start">
              <svg class="mt-0.5 mr-2 h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              </svg>
              <span>Use descriptive names for easy identification</span>
            </div>
            <div class="flex items-start">
              <svg class="mt-0.5 mr-2 h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              </svg>
              <span>Enable SSL/TLS for production systems</span>
            </div>
            <div class="flex items-start">
              <svg class="mt-0.5 mr-2 h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              </svg>
              <span>Tag connections for better organization</span>
            </div>
          </div>
          <div class="mt-4">
            <a href="#" class="inline-flex items-center text-sm font-medium text-blue-700 hover:text-blue-600">
              View detailed documentation
              <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Popular Templates -->
    <div class="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"/>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-lg font-medium text-green-900 mb-3">Popular Templates</h3>
          <div class="space-y-2">
            <button type="button"
                    class="w-full text-left p-3 bg-white border border-green-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors duration-200"
                    data-action="click->connection-wizard#loadTemplate"
                    data-template="postgresql-production">
              <div class="text-sm font-medium text-green-900">PostgreSQL Production</div>
              <div class="text-xs text-green-700">Standard production database setup</div>
            </button>
            <button type="button"
                    class="w-full text-left p-3 bg-white border border-green-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors duration-200"
                    data-action="click->connection-wizard#loadTemplate"
                    data-template="aws-s3">
              <div class="text-sm font-medium text-green-900">AWS S3 Bucket</div>
              <div class="text-xs text-green-700">Amazon S3 storage connection</div>
            </button>
            <button type="button"
                    class="w-full text-left p-3 bg-white border border-green-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors duration-200"
                    data-action="click->connection-wizard#loadTemplate"
                    data-template="rest-api">
              <div class="text-sm font-medium text-green-900">REST API</div>
              <div class="text-xs text-green-700">Standard REST API with authentication</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Keyboard Shortcuts Help -->
  <div class="mt-6 bg-gray-50 border border-gray-200 rounded-lg p-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <svg class="h-5 w-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
        </svg>
        <span class="text-sm text-gray-600">Keyboard shortcuts:</span>
      </div>
      <div class="flex items-center space-x-4 text-xs text-gray-500">
        <span><kbd class="px-2 py-1 bg-gray-200 rounded">Ctrl+S</kbd> Auto-save</span>
        <span><kbd class="px-2 py-1 bg-gray-200 rounded">Tab</kbd> Next field</span>
        <span><kbd class="px-2 py-1 bg-gray-200 rounded">Ctrl+T</kbd> Test connection</span>
        <span><kbd class="px-2 py-1 bg-gray-200 rounded">Ctrl+Enter</kbd> Save</span>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced JavaScript for Wizard Functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Initialize wizard functionality
  const wizard = {
    currentStep: 1,
    totalSteps: 4,

    init() {
      this.bindEvents();
      this.updateStepIndicators();
      this.setupKeyboardShortcuts();
      this.initializeAutoSave();
    },

    bindEvents() {
      // Step navigation
      document.addEventListener('click', (e) => {
        if (e.target.matches('[data-action*="nextStep"]')) {
          this.nextStep();
        }
        if (e.target.matches('[data-action*="previousStep"]')) {
          this.previousStep();
        }
        if (e.target.matches('[data-action*="selectConnectionType"]')) {
          this.selectConnectionType(e.target.dataset.type);
        }
        if (e.target.matches('[data-action*="testConnection"]')) {
          this.testConnection();
        }
      });

      // Form validation
      document.addEventListener('input', (e) => {
        if (e.target.matches('[data-validation-rules]')) {
          this.validateField(e.target);
        }
      });
    },

    nextStep() {
      if (this.currentStep < this.totalSteps && this.validateCurrentStep()) {
        this.currentStep++;
        this.updateStepDisplay();
        this.updateStepIndicators();
        this.updateSummary();
      }
    },

    previousStep() {
      if (this.currentStep > 1) {
        this.currentStep--;
        this.updateStepDisplay();
        this.updateStepIndicators();
      }
    },

    updateStepDisplay() {
      // Hide all steps
      document.querySelectorAll('.wizard-step').forEach(step => {
        step.classList.remove('active');
      });

      // Show current step
      const currentStepEl = document.querySelector(`[data-step="${this.currentStep}"]`);
      if (currentStepEl) {
        currentStepEl.classList.add('active');
      }

      // Update navigation buttons
      const prevBtn = document.querySelector('[data-connection-wizard-target="prevButton"]');
      const nextBtn = document.querySelector('[data-connection-wizard-target="nextButton"]');
      const submitBtn = document.querySelector('[data-connection-wizard-target="submitButton"]');

      if (prevBtn) prevBtn.disabled = this.currentStep === 1;

      if (this.currentStep === this.totalSteps) {
        if (nextBtn) nextBtn.style.display = 'none';
        if (submitBtn) submitBtn.style.display = 'inline-flex';
      } else {
        if (nextBtn) nextBtn.style.display = 'inline-flex';
        if (submitBtn) submitBtn.style.display = 'none';
      }

      // Update step counter
      const stepText = document.querySelector('[data-connection-wizard-target="currentStepText"]');
      if (stepText) stepText.textContent = this.currentStep;
    },

    updateStepIndicators() {
      document.querySelectorAll('[data-connection-wizard-target="stepIndicator"]').forEach(indicator => {
        const step = parseInt(indicator.dataset.step);
        indicator.className = indicator.className.replace(/\b(active|completed)\b/g, '');

        if (step < this.currentStep) {
          indicator.classList.add('completed');
          indicator.innerHTML = '✓';
        } else if (step === this.currentStep) {
          indicator.classList.add('active');
          indicator.innerHTML = step;
        } else {
          indicator.innerHTML = step;
        }
      });
    },

    selectConnectionType(type) {
      // Update visual selection
      document.querySelectorAll('.connection-type-card').forEach(card => {
        card.classList.remove('selected');
      });
      event.target.closest('.connection-type-card').classList.add('selected');

      // Update hidden field
      const typeField = document.querySelector('[data-connection-wizard-target="connectorTypeField"]');
      if (typeField) typeField.value = type;

      // Show relevant configuration section
      this.showConfigurationSection(type);

      // Enable next button
      const nextBtn = document.querySelector('[data-connection-wizard-target="nextButton"]');
      if (nextBtn) nextBtn.disabled = false;
    },

    showConfigurationSection(type) {
      // Hide all config sections
      document.querySelectorAll('[data-connection-wizard-target$="Config"]').forEach(section => {
        section.classList.add('hidden');
      });

      // Show relevant section
      const sectionMap = {
        'database': 'databaseConfig',
        'cloud_storage': 'cloudStorageConfig',
        'api': 'apiConfig',
        'file_system': 'fileSystemConfig',
        'data_warehouse': 'databaseConfig', // Use database config for warehouses
        'custom': 'defaultConfig'
      };

      const sectionName = sectionMap[type] || 'defaultConfig';
      const section = document.querySelector(`[data-connection-wizard-target="${sectionName}"]`);
      if (section) {
        section.classList.remove('hidden');
      }
    },

    validateCurrentStep() {
      const currentStepEl = document.querySelector(`[data-step="${this.currentStep}"].active`);
      if (!currentStepEl) return true;

      const requiredFields = currentStepEl.querySelectorAll('[required]');
      let isValid = true;

      requiredFields.forEach(field => {
        if (!field.value.trim()) {
          this.showFieldError(field, 'This field is required');
          isValid = false;
        } else {
          this.clearFieldError(field);
        }
      });

      return isValid;
    },

    validateField(field) {
      const rules = field.dataset.validationRules;
      if (!rules) return;

      const value = field.value.trim();
      const ruleList = rules.split('|');

      for (const rule of ruleList) {
        if (rule === 'required' && !value) {
          this.showFieldError(field, 'This field is required');
          return false;
        }

        if (rule.startsWith('min:')) {
          const min = parseInt(rule.split(':')[1]);
          if (value.length < min) {
            this.showFieldError(field, `Minimum ${min} characters required`);
            return false;
          }
        }

        if (rule.startsWith('max:')) {
          const max = parseInt(rule.split(':')[1]);
          if (value.length > max) {
            this.showFieldError(field, `Maximum ${max} characters allowed`);
            return false;
          }
        }

        if (rule === 'url' && value && !this.isValidUrl(value)) {
          this.showFieldError(field, 'Please enter a valid URL');
          return false;
        }

        if (rule === 'host' && value && !this.isValidHost(value)) {
          this.showFieldError(field, 'Please enter a valid hostname or IP address');
          return false;
        }
      }

      this.showFieldSuccess(field);
      return true;
    },

    showFieldError(field, message) {
      field.classList.add('form-field-error');
      field.classList.remove('form-field-success');

      const errorEl = field.parentNode.querySelector('[data-form-validation-target="error"]');
      const errorMsgEl = field.parentNode.querySelector('[data-form-validation-target="errorMessage"]');

      if (errorEl) {
        errorEl.classList.remove('hidden');
        if (errorMsgEl) errorMsgEl.textContent = message;
      }

      const successEl = field.parentNode.querySelector('[data-form-validation-target="success"]');
      if (successEl) successEl.classList.add('hidden');
    },

    showFieldSuccess(field) {
      field.classList.add('form-field-success');
      field.classList.remove('form-field-error');

      const successEl = field.parentNode.querySelector('[data-form-validation-target="success"]');
      if (successEl) successEl.classList.remove('hidden');

      const errorEl = field.parentNode.querySelector('[data-form-validation-target="error"]');
      if (errorEl) errorEl.classList.add('hidden');
    },

    clearFieldError(field) {
      field.classList.remove('form-field-error');
      const errorEl = field.parentNode.querySelector('[data-form-validation-target="error"]');
      if (errorEl) errorEl.classList.add('hidden');
    },

    testConnection() {
      const testButton = document.querySelector('[data-connection-wizard-target="testButton"]');
      const testResults = document.querySelector('[data-connection-wizard-target="testResults"]');
      const testPlaceholder = document.querySelector('[data-connection-wizard-target="testPlaceholder"]');
      const testLoading = document.querySelector('[data-connection-wizard-target="testLoading"]');
      const testSuccess = document.querySelector('[data-connection-wizard-target="testSuccess"]');
      const testError = document.querySelector('[data-connection-wizard-target="testError"]');

      // Show loading state
      if (testPlaceholder) testPlaceholder.style.display = 'none';
      if (testResults) testResults.classList.remove('hidden');
      if (testLoading) testLoading.classList.remove('hidden');
      if (testSuccess) testSuccess.classList.add('hidden');
      if (testError) testError.classList.add('hidden');

      // Simulate connection test (replace with actual API call)
      setTimeout(() => {
        const isSuccess = Math.random() > 0.3; // 70% success rate for demo

        if (testLoading) testLoading.classList.add('hidden');

        if (isSuccess) {
          if (testSuccess) testSuccess.classList.remove('hidden');
        } else {
          if (testError) testError.classList.remove('hidden');
        }
      }, 2000);
    },

    updateSummary() {
      if (this.currentStep === 4) {
        const summaryName = document.querySelector('[data-connection-wizard-target="summaryName"]');
        const summaryType = document.querySelector('[data-connection-wizard-target="summaryType"]');
        const summaryDirection = document.querySelector('[data-connection-wizard-target="summaryDirection"]');
        const summaryHost = document.querySelector('[data-connection-wizard-target="summaryHost"]');

        const nameField = document.querySelector('#data_connector_name');
        const typeField = document.querySelector('[data-connection-wizard-target="connectorTypeField"]');
        const directionField = document.querySelector('input[name="data_connector[direction]"]:checked');
        const hostField = document.querySelector('#data_connector_host');

        if (summaryName && nameField) summaryName.textContent = nameField.value || '-';
        if (summaryType && typeField) summaryType.textContent = typeField.value || '-';
        if (summaryDirection && directionField) summaryDirection.textContent = directionField.value || '-';
        if (summaryHost && hostField) summaryHost.textContent = hostField.value || '-';
      }
    },

    setupKeyboardShortcuts() {
      document.addEventListener('keydown', (e) => {
        if (e.ctrlKey || e.metaKey) {
          switch(e.key) {
            case 's':
              e.preventDefault();
              this.autoSave();
              break;
            case 't':
              e.preventDefault();
              if (this.currentStep === 4) this.testConnection();
              break;
            case 'Enter':
              e.preventDefault();
              if (this.currentStep === this.totalSteps) {
                document.querySelector('[data-connection-wizard-target="submitButton"]')?.click();
              }
              break;
          }
        }

        if (e.key === 'Tab' && !e.shiftKey) {
          // Enhanced tab navigation
        }
      });
    },

    initializeAutoSave() {
      setInterval(() => {
        this.autoSave();
      }, 30000); // Auto-save every 30 seconds
    },

    autoSave() {
      const indicator = document.querySelector('[data-connection-wizard-target="autoSaveIndicator"]');
      if (indicator) {
        indicator.classList.remove('hidden');
        setTimeout(() => indicator.classList.add('hidden'), 2000);
      }

      // Implement actual auto-save logic here
      console.log('Auto-saving form data...');
    },

    isValidUrl(string) {
      try {
        new URL(string);
        return true;
      } catch (_) {
        return false;
      }
    },

    isValidHost(string) {
      const hostRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;
      return hostRegex.test(string);
    }
  };

  wizard.init();
});
</script>
