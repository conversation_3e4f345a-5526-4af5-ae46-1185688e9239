<% content_for :page_title, @data_connector.display_name %>

<div class="max-w-7xl mx-auto">
  <!-- Breadcrumb Navigation -->
  <nav class="flex mb-6" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-3">
      <li class="inline-flex items-center">
        <%= link_to projects_path, class: "inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-150" do %>
          <svg class="mr-2 w-4 h-4" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L9 5.414V17a1 1 0 102 0V5.414l5.293 5.293a1 1 0 001.414-1.414l-7-7z"/>
          </svg>
          Projects
        <% end %>
      </li>
      <li>
        <div class="flex items-center">
          <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
          </svg>
          <%= link_to @project, class: "ml-1 text-sm font-medium text-gray-700 hover:text-indigo-600 md:ml-2 transition-colors duration-150" do %>
            <%= @project.name %>
          <% end %>
        </div>
      </li>
      <li>
        <div class="flex items-center">
          <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
          </svg>
          <%= link_to project_data_connectors_path(@project), class: "ml-1 text-sm font-medium text-gray-700 hover:text-indigo-600 md:ml-2 transition-colors duration-150" do %>
            Connectors
          <% end %>
        </div>
      </li>
      <li>
        <div class="flex items-center">
          <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
          </svg>
          <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2"><%= @data_connector.name %></span>
        </div>
      </li>
    </ol>
  </nav>

  <!-- Header Section -->
  <div class="bg-white shadow-lg rounded-lg border border-gray-200 mb-8 overflow-hidden card-sophisticated">
    <div class="bg-gradient-to-r from-indigo-50 to-blue-50 px-6 py-6 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <!-- Connector Icon -->
          <div class="h-16 w-16 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-lg flex items-center justify-center shadow-lg">
            <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.79 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.79 4 8 4s8-1.79 8-4M4 7c0-2.21 3.79-4 8-4s8 1.79 8 4"/>
            </svg>
          </div>
          
          <div>
            <h1 class="text-3xl font-bold text-gray-900"><%= @data_connector.name %></h1>
            <div class="flex items-center space-x-3 mt-2">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800">
                <%= @data_connector.connector_type.humanize %>
              </span>
              
              <!-- Status Badge -->
              <% case @data_connector.status %>
              <% when 'active' %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                  </svg>
                  Active
                </span>
              <% when 'error' %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                  </svg>
                  Error
                </span>
              <% when 'testing' %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  <svg class="animate-spin w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Testing
                </span>
              <% else %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  Inactive
                </span>
              <% end %>
              
              <!-- Health Status -->
              <% if @data_connector.connection_healthy? %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  Healthy
                </span>
              <% elsif @data_connector.needs_testing? %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                  </svg>
                  Needs Testing
                </span>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex space-x-3">
          <%= link_to "Test Connection", 
              test_connection_project_data_connector_path(@project, @data_connector), 
              method: :post,
              class: "inline-flex items-center px-4 py-2 border border-indigo-300 rounded-md shadow-sm text-sm font-medium text-indigo-700 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-150 btn-sophisticated",
              data: { 
                confirm: "This will test the connection to your data source. Continue?",
                disable_with: "Testing..."
              } %>
          
          <%= link_to "Edit", 
              edit_project_data_connector_path(@project, @data_connector), 
              class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-150 btn-sophisticated" %>
          
          <%= link_to "Delete", 
              project_data_connector_path(@project, @data_connector), 
              method: :delete,
              class: "inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-150 btn-sophisticated",
              data: { 
                confirm: "Are you sure you want to delete this connector? This action cannot be undone.",
                disable_with: "Deleting..."
              } %>
        </div>
      </div>
    </div>

    <!-- Connection Summary -->
    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
      <div class="flex items-center space-x-2">
        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
        </svg>
        <span class="text-sm font-medium text-gray-700">Connection:</span>
        <span class="text-sm text-gray-900 font-mono bg-white px-2 py-1 rounded border">
          <%= @data_connector.connection_summary %>
        </span>
      </div>
    </div>
  </div>

  <!-- Main Content Grid -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    
    <!-- Left Column - Main Details -->
    <div class="lg:col-span-2 space-y-8">
      
      <!-- Connection Details -->
      <div class="bg-white shadow-lg rounded-lg border border-gray-200 overflow-hidden card-sophisticated">
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900 flex items-center">
            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
            Connection Configuration
          </h2>
        </div>
        
        <div class="p-6">
          <div class="space-y-4">
            <% @data_connector.masked_config.each do |key, value| %>
              <div class="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                <span class="text-sm font-medium text-gray-700 capitalize">
                  <%= key.humanize %>
                </span>
                <span class="text-sm text-gray-900 font-mono bg-gray-50 px-2 py-1 rounded">
                  <%= value.present? ? value : '(not set)' %>
                </span>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Test Results -->
      <% if @data_connector.test_result.present? %>
        <div class="bg-white shadow-lg rounded-lg border border-gray-200 overflow-hidden card-sophisticated">
          <div class="bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
              <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              Latest Test Results
            </h2>
          </div>
          
          <div class="p-6">
            <div class="bg-gray-50 rounded-lg p-4">
              <pre class="text-sm text-gray-800 whitespace-pre-wrap font-mono"><%= @data_connector.test_result %></pre>
            </div>
          </div>
        </div>
      <% end %>

    </div>

    <!-- Right Column - Sidebar -->
    <div class="space-y-6">
      
      <!-- Quick Stats -->
      <div class="bg-white shadow-lg rounded-lg border border-gray-200 overflow-hidden card-sophisticated">
        <div class="bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Quick Stats</h3>
        </div>
        
        <div class="p-6 space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-700">Created</span>
            <span class="text-sm text-gray-900">
              <%= time_ago_in_words(@data_connector.created_at) %> ago
            </span>
          </div>
          
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-700">Last Updated</span>
            <span class="text-sm text-gray-900">
              <%= time_ago_in_words(@data_connector.updated_at) %> ago
            </span>
          </div>
          
          <% if @data_connector.last_tested_at.present? %>
            <div class="flex justify-between items-center">
              <span class="text-sm font-medium text-gray-700">Last Tested</span>
              <span class="text-sm text-gray-900">
                <%= time_ago_in_words(@data_connector.last_tested_at) %> ago
              </span>
            </div>
          <% end %>
          
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-700">Created By</span>
            <span class="text-sm text-gray-900">
              <%= @data_connector.created_by.first_name %> <%= @data_connector.created_by.last_name %>
            </span>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="bg-white shadow-lg rounded-lg border border-gray-200 overflow-hidden card-sophisticated">
        <div class="bg-gradient-to-r from-orange-50 to-yellow-50 px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Actions</h3>
        </div>
        
        <div class="p-6 space-y-3">
          <%= link_to test_connection_project_data_connector_path(@project, @data_connector), 
              method: :post,
              class: "w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-150 btn-sophisticated" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
            </svg>
            Test Connection
          <% end %>
          
          <%= link_to edit_project_data_connector_path(@project, @data_connector), 
              class: "w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-150 btn-sophisticated" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
            </svg>
            Edit Configuration
          <% end %>
          
          <%= link_to project_data_connectors_path(@project), 
              class: "w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-150 btn-sophisticated" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            Back to Connectors
          <% end %>
        </div>
      </div>

    </div>
  </div>
</div>
