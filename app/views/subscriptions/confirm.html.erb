<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
      Confirm Your Subscription
    </h2>
    <p class="mt-2 text-center text-sm text-gray-600">
      Complete your payment to activate your subscription
    </p>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
      <div id="payment-element">
        <!-- Stripe Elements will create form elements here -->
      </div>
      
      <div class="mt-6">
        <button id="submit-payment" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
          <span id="button-text">Complete Payment</span>
        </button>
      </div>

      <div id="payment-message" class="mt-4 text-sm text-red-600 hidden"></div>
    </div>
  </div>
</div>

<script src="https://js.stripe.com/v3/"></script>
<script>
  const stripe = Stripe('<%= @publishable_key %>');
  
  const elements = stripe.elements({
    clientSecret: '<%= @client_secret %>'
  });

  const paymentElement = elements.create('payment');
  paymentElement.mount('#payment-element');

  const form = document.getElementById('payment-form');
  const submitButton = document.getElementById('submit-payment');
  const buttonText = document.getElementById('button-text');
  const messageContainer = document.getElementById('payment-message');

  submitButton.addEventListener('click', async (event) => {
    event.preventDefault();
    
    setLoading(true);

    const {error} = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: '<%= subscription_url %>'
      }
    });

    if (error) {
      showMessage(error.message);
    }
    
    setLoading(false);
  });

  function showMessage(messageText) {
    messageContainer.textContent = messageText;
    messageContainer.classList.remove('hidden');
  }

  function setLoading(isLoading) {
    if (isLoading) {
      submitButton.disabled = true;
      buttonText.textContent = 'Processing...';
    } else {
      submitButton.disabled = false;
      buttonText.textContent = 'Complete Payment';
    }
  }
</script>