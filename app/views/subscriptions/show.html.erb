<div class="min-h-screen bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Subscription Management</h1>
      <p class="mt-2 text-gray-600">Manage your billing and subscription plan</p>
    </div>

    <!-- Current Subscription Status -->
    <div class="bg-white overflow-hidden shadow rounded-lg mb-8">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-lg font-medium text-gray-900">Current Plan</h2>
            <p class="mt-1 text-sm text-gray-500">
              <% if @current_plan %>
                <%= @current_plan.name %> - $<%= @current_plan.price_in_dollars %>/month
              <% else %>
                Free Plan
              <% end %>
            </p>
          </div>
          <div>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
              <%= case @subscription.status
                  when 'active' then 'bg-green-100 text-green-800'
                  when 'trialing' then 'bg-blue-100 text-blue-800'
                  when 'past_due' then 'bg-yellow-100 text-yellow-800'
                  when 'canceled' then 'bg-red-100 text-red-800'
                  else 'bg-gray-100 text-gray-800'
                  end %>">
              <%= @subscription.status.humanize %>
            </span>
          </div>
        </div>

        <% if @subscription.trial_end && @subscription.trialing? %>
          <div class="mt-4 p-4 bg-blue-50 rounded-md">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Trial Period</h3>
                <div class="mt-2 text-sm text-blue-700">
                  <p>Your trial ends on <%= @subscription.trial_end.strftime('%B %d, %Y') %></p>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <% if @subscription.current_period_end %>
          <div class="mt-4 text-sm text-gray-500">
            <p>
              <% if @subscription.canceled? %>
                Your subscription was canceled and will end on <%= @subscription.current_period_end.strftime('%B %d, %Y') %>
              <% else %>
                Next billing date: <%= @subscription.current_period_end.strftime('%B %d, %Y') %>
              <% end %>
            </p>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Available Plans -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-6">Available Plans</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- Free Plan -->
          <div class="border rounded-lg p-6 <%= 'ring-2 ring-blue-500' if @subscription.free? %>">
            <div class="text-center">
              <h3 class="text-lg font-semibold text-gray-900">Free</h3>
              <div class="mt-4">
                <span class="text-4xl font-bold text-gray-900">$0</span>
                <span class="text-gray-500">/month</span>
              </div>
              <ul class="mt-6 space-y-4 text-sm text-gray-600">
                <li>2 pipelines</li>
                <li>1,000 executions/month</li>
                <li>Basic connectors</li>
                <li>Community support</li>
              </ul>
              <% if @subscription.free? %>
                <div class="mt-6">
                  <span class="w-full bg-gray-100 text-gray-500 py-2 px-4 rounded-md text-sm font-medium">
                    Current Plan
                  </span>
                </div>
              <% else %>
                <%= form_with url: cancel_subscription_path, method: :patch, local: true, class: "mt-6" do |f| %>
                  <%= f.submit "Downgrade to Free", 
                      class: "w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md text-sm font-medium",
                      confirm: "Are you sure you want to cancel your subscription?" %>
                <% end %>
              <% end %>
            </div>
          </div>

          <% @subscription_plans.each do |plan| %>
            <div class="border rounded-lg p-6 <%= 'ring-2 ring-blue-500' if @current_plan == plan %>">
              <div class="text-center">
                <h3 class="text-lg font-semibold text-gray-900"><%= plan.name %></h3>
                <div class="mt-4">
                  <span class="text-4xl font-bold text-gray-900">$<%= plan.price_in_dollars %></span>
                  <span class="text-gray-500">/<%= plan.billing_cycle %></span>
                </div>
                <ul class="mt-6 space-y-4 text-sm text-gray-600">
                  <% plan.features_list.each do |feature| %>
                    <li><%= feature %></li>
                  <% end %>
                </ul>
                <div class="mt-6">
                  <% if @current_plan == plan %>
                    <span class="w-full bg-blue-100 text-blue-800 py-2 px-4 rounded-md text-sm font-medium">
                      Current Plan
                    </span>
                  <% elsif @subscription.free? %>
                    <%= form_with url: subscription_path, method: :post, local: true do |f| %>
                      <%= f.hidden_field :subscription_plan_id, value: plan.id %>
                      <%= f.submit "Upgrade to #{plan.name}", 
                          class: "w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md text-sm font-medium" %>
                    <% end %>
                  <% else %>
                    <%= form_with url: subscription_path, method: :patch, local: true do |f| %>
                      <%= f.hidden_field :subscription_plan_id, value: plan.id %>
                      <%= f.submit "Switch to #{plan.name}", 
                          class: "w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md text-sm font-medium" %>
                    <% end %>
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
        </div>

        <% unless @subscription.free? %>
          <div class="mt-8 pt-6 border-t border-gray-200">
            <div class="flex justify-between items-center">
              <div>
                <h3 class="text-lg font-medium text-gray-900">Danger Zone</h3>
                <p class="text-sm text-gray-500">Cancel your subscription</p>
              </div>
              <%= form_with url: cancel_subscription_path, method: :patch, local: true do |f| %>
                <%= f.submit "Cancel Subscription", 
                    class: "bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md text-sm font-medium",
                    confirm: "Are you sure you want to cancel your subscription? It will remain active until the end of your current billing period." %>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>