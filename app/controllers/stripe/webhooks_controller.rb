# frozen_string_literal: true

module Stripe
  class WebhooksController < ApplicationController
    skip_before_action :verify_authenticity_token
    skip_before_action :authenticate_user!

    def create
      webhook_service = WebhookService.new(request.body.read, request.env["HTTP_STRIPE_SIGNATURE"])
      webhook_service.process!

      head :ok
    rescue WebhookService::InvalidSignatureError => e
      Rails.logger.error "Invalid Stripe webhook signature: #{e.message}"
      head :bad_request
    rescue WebhookService::Error => e
      Rails.logger.error "Stripe webhook processing error: #{e.message}"
      head :unprocessable_entity
    rescue StandardError => e
      Rails.logger.error "Unexpected error processing Stripe webhook: #{e.message}"
      head :internal_server_error
    end
  end
end
