class Api::AuthController < Api::BaseController
  skip_before_action :authenticate_user_from_token!, only: [ :login, :register ]

  def login
    account = Account.find_by(subdomain: params[:subdomain])
    return render_error("Account not found", :not_found) unless account

    # Temporarily disable <PERSON><PERSON> for authentication
    ActiveRecord::Base.connection.execute("SET LOCAL rls.bypass = true")

    user = account.users.find_for_authentication(email: params[:email])

    if user&.valid_password?(params[:password])
      token = generate_jwt_token(user)

      render json: {
        token: token,
        user: user_data(user),
        account: account_data(user.account)
      }
    else
      render_error("Invalid credentials", :unauthorized)
    end
  ensure
    # Re-enable RLS
    ActiveRecord::Base.connection.execute("SET LOCAL rls.bypass = false")
  end

  def register
    account_params = params.require(:account).permit(:name, :subdomain)
    user_params = params.require(:user).permit(:email, :password, :password_confirmation, :first_name, :last_name)

    ActiveRecord::Base.transaction do
      # Create account
      account = Account.new(account_params)
      account.status = "active"

      if account.save
        # Create owner user
        user = account.users.build(user_params)
        user.role = "owner"
        user.skip_confirmation! # Auto-confirm for now

        if user.save
          token = generate_jwt_token(user)

          render json: {
            token: token,
            user: user_data(user),
            account: account_data(account)
          }, status: :created
        else
          render_error(user.errors.full_messages, :unprocessable_entity)
        end
      else
        render_error(account.errors.full_messages, :unprocessable_entity)
      end
    end
  end

  def logout
    # Add current token to denylist
    if request.headers["Authorization"]
      token = request.headers["Authorization"].split(" ").last

      begin
        jwt_payload = JWT.decode(token, Rails.application.credentials.devise_jwt_secret_key!).first

        JwtDenylist.create!(
          jti: jwt_payload["jti"] || SecureRandom.uuid,
          exp: Time.at(jwt_payload["exp"] || 1.day.from_now.to_i)
        )

        render json: { message: "Logged out successfully" }
      rescue JWT::DecodeError
        render_error("Invalid token", :bad_request)
      end
    else
      render json: { message: "No active session" }
    end
  end

  def me
    render json: {
      user: user_data(current_user),
      account: account_data(current_account)
    }
  end

  private

  def generate_jwt_token(user)
    payload = {
      sub: user.id,
      jti: SecureRandom.uuid,
      scp: "user",
      account_id: user.account_id,
      iat: Time.now.to_i,
      exp: 1.day.from_now.to_i
    }

    JWT.encode(payload, Rails.application.credentials.devise_jwt_secret_key!)
  end

  def user_data(user)
    {
      id: user.id,
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      full_name: user.full_name,
      role: user.role,
      time_zone: user.time_zone,
      created_at: user.created_at
    }
  end

  def account_data(account)
    {
      id: account.id,
      name: account.name,
      subdomain: account.subdomain,
      status: account.status,
      onboarded: account.onboarded?,
      plan: account.subscription&.plan,
      created_at: account.created_at
    }
  end

  def render_error(message, status)
    render json: { error: message }, status: status
  end
end
