class Api::BaseController < ActionController::API
  before_action :authenticate_user_from_token!
  before_action :set_current_account

  rescue_from StandardError, with: :render_internal_error
  rescue_from ActiveRecord::RecordNotFound, with: :render_not_found
  rescue_from ActionController::ParameterMissing, with: :render_bad_request

  private

  def authenticate_user_from_token!
    token = request.headers["Authorization"]&.split(" ")&.last
    return render_unauthorized unless token

    begin
      jwt_payload = JWT.decode(token, Rails.application.credentials.devise_jwt_secret_key!).first
      @current_user = User.find(jwt_payload["sub"])
      @current_account = @current_user.account
      Current.user = @current_user
      Current.account = @current_account
    rescue JWT::ExpiredSignature
      render_unauthorized("Token has expired")
    rescue JWT::VerificationError, JWT::DecodeError
      render_unauthorized("Invalid token")
    rescue ActiveRecord::RecordNotFound
      render_unauthorized("User not found")
    end
  end

  def authenticate_api_key!
    api_key = request.headers["X-API-Key"] || params[:api_key]
    return render_unauthorized("API key required") unless api_key

    @api_token = ApiToken.active.find_by(token: api_key)

    if @api_token.nil?
      render_unauthorized("Invalid API key")
    elsif !@api_token.active?
      render_unauthorized("API key expired or revoked")
    else
      @current_account = @api_token.account
      Current.account = @current_account
      @api_token.update_last_used!

      # Track API usage
      track_api_usage
    end
  end

  def set_current_account
    Current.account = @current_account
    Current.user = @current_user
  end

  def current_user
    @current_user
  end

  def current_account
    @current_account
  end

  def track_api_usage
    # TODO: Implement usage tracking when UsageRecord model exists
  end

  def render_unauthorized(message = "Unauthorized")
    render json: { error: message }, status: :unauthorized
  end

  def render_not_found(exception)
    render json: { error: "Not found" }, status: :not_found
  end

  def render_bad_request(exception)
    render json: { error: exception.message }, status: :bad_request
  end

  def render_internal_error(exception)
    Rails.logger.error "API Error: #{exception.class} - #{exception.message}"
    Rails.logger.error exception.backtrace.join("\n")

    render json: { error: "Internal server error" }, status: :internal_server_error
  end
end
