class Api::AnalyticsController < ApplicationController
  before_action :authenticate_user!
  
  def index
    range = params[:range] || '7d'
    
    analytics_data = {
      revenue: generate_revenue_data(range),
      users: generate_user_data(range),
      performance: generate_performance_data(range),
      system_health: generate_system_health_data,
      activity_feed: generate_activity_feed_data
    }
    
    render json: {
      data: analytics_data,
      meta: {
        range: range,
        generated_at: Time.current,
        timezone: Time.zone.name
      }
    }
  end
  
  def revenue
    range = params[:range] || '7d'
    
    render json: {
      data: generate_revenue_data(range)
    }
  end
  
  def users
    range = params[:range] || '7d'
    
    render json: {
      data: generate_user_data(range)
    }
  end
  
  def performance
    range = params[:range] || '7d'
    
    render json: {
      data: generate_performance_data(range)
    }
  end
  
  private
  
  def generate_revenue_data(range)
    # Generate realistic revenue data based on range
    case range
    when '24h'
      {
        total: 5_240,
        growth: 12.3,
        trend: generate_hourly_data(24),
        period: 'Last 24 hours'
      }
    when '7d'
      {
        total: 124_567,
        growth: 23.5,
        trend: generate_daily_data(7),
        period: 'Last 7 days'
      }
    when '30d'
      {
        total: 485_920,
        growth: 18.7,
        trend: generate_daily_data(30),
        period: 'Last 30 days'
      }
    when '90d'
      {
        total: 1_456_780,
        growth: 34.2,
        trend: generate_weekly_data(13),
        period: 'Last 90 days'
      }
    end
  end
  
  def generate_user_data(range)
    case range
    when '24h'
      {
        total: 234,
        growth: 8.4,
        trend: generate_hourly_data(24, base: 10, variance: 5),
        period: 'Last 24 hours'
      }
    when '7d'
      {
        total: 8_234,
        growth: 18.2,
        trend: generate_daily_data(7, base: 1000, variance: 200),
        period: 'Last 7 days'
      }
    when '30d'
      {
        total: 32_540,
        growth: 25.8,
        trend: generate_daily_data(30, base: 1000, variance: 300),
        period: 'Last 30 days'
      }
    when '90d'
      {
        total: 89_760,
        growth: 42.1,
        trend: generate_weekly_data(13, base: 6000, variance: 1000),
        period: 'Last 90 days'
      }
    end
  end
  
  def generate_performance_data(range)
    {
      success_rate: rand(90.0..98.0).round(1),
      avg_processing_time: rand(1.5..3.5).round(1),
      error_rate: rand(2.0..8.0).round(1),
      throughput: rand(850..1250),
      distribution: {
        successful: rand(300..400),
        failed: rand(10..30),
        running: rand(15..35)
      }
    }
  end
  
  def generate_system_health_data
    {
      cpu_usage: rand(25..65),
      memory_usage: rand(45..85),
      disk_usage: rand(60..90),
      network_io: rand(20..60),
      uptime: rand(99.5..99.9).round(2)
    }
  end
  
  def generate_activity_feed_data
    activities = []
    
    5.times do |i|
      activity_types = [
        {
          type: 'pipeline_completed',
          title: 'Pipeline completed successfully',
          detail: ["Customer ETL", "Sales Sync", "Log Aggregation", "Data Warehouse Sync"].sample,
          icon: 'check',
          color: 'green'
        },
        {
          type: 'connection_established',
          title: 'New connection established',
          detail: ["PostgreSQL", "MongoDB", "S3 Bucket", "Snowflake"].sample,
          icon: 'database',
          color: 'blue'
        },
        {
          type: 'warning_detected',
          title: 'Performance warning',
          detail: 'High latency detected',
          icon: 'alert',
          color: 'yellow'
        },
        {
          type: 'error_occurred',
          title: 'Pipeline execution failed',
          detail: 'Connection timeout error',
          icon: 'x',
          color: 'red'
        }
      ]
      
      activity = activity_types.sample
      activities << {
        id: SecureRandom.uuid,
        type: activity[:type],
        title: activity[:title],
        detail: activity[:detail],
        icon: activity[:icon],
        color: activity[:color],
        timestamp: (i + 1).minutes.ago,
        meta: "#{rand(100..5000)}K rows processed"
      }
    end
    
    activities
  end
  
  def generate_hourly_data(hours, base: 1000, variance: 200)
    (0...hours).map do |hour|
      {
        x: hour,
        y: base + rand(-variance..variance),
        label: "#{hour}:00"
      }
    end
  end
  
  def generate_daily_data(days, base: 15000, variance: 3000)
    (0...days).map do |day|
      date = day.days.ago
      weekend_factor = date.saturday? || date.sunday? ? 0.7 : 1.0
      
      {
        x: day,
        y: (base + rand(-variance..variance)) * weekend_factor,
        label: date.strftime("%m/%d")
      }
    end.reverse
  end
  
  def generate_weekly_data(weeks, base: 100000, variance: 20000)
    (0...weeks).map do |week|
      date = week.weeks.ago
      
      {
        x: week,
        y: base + rand(-variance..variance),
        label: "Week of #{date.strftime('%m/%d')}"
      }
    end.reverse
  end
end