class TeamMembersController < ApplicationController
  layout "dashboard"

  before_action :authenticate_user!
  before_action :ensure_owner_or_admin, except: [ :index, :show ]
  before_action :set_team_member, only: [ :show, :edit, :update, :destroy ]
  before_action :set_sidebar_metrics

  def index
    @team_members = current_account.users.includes(:account).order(:created_at)
    @pending_invitations = current_account.pending_invitations.includes(:invited_by)
    @can_invite = current_account.can_invite_members?
    @max_members = current_account.max_team_members
  end

  def show
    # Show team member details
  end

  def new
    @team_invitation = current_account.team_invitations.build
  end

  def create
    @team_invitation = current_account.team_invitations.build(invitation_params)
    @team_invitation.invited_by = current_user

    if @team_invitation.save
      # TODO: Send invitation email
      redirect_to team_members_path, notice: "Invitation sent to #{@team_invitation.email}"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    # Edit team member role
  end

  def update
    if @team_member.update(team_member_params)
      redirect_to team_members_path, notice: "Team member updated successfully."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    if @team_member == current_user
      redirect_to team_members_path, alert: "You cannot remove yourself from the team."
      return
    end

    if @team_member.owner?
      redirect_to team_members_path, alert: "Cannot remove the account owner."
      return
    end

    @team_member.destroy
    redirect_to team_members_path, notice: "Team member removed successfully."
  end

  def accept_invitation
    @invitation = TeamInvitation.find_by!(token: params[:token])

    unless @invitation.valid_for_acceptance?
      redirect_to root_path, alert: "This invitation is invalid or has expired."
      return
    end

    if @invitation.accept!(current_user)
      redirect_to subdomain_root_path, notice: "Welcome to the team!"
    else
      redirect_to root_path, alert: "Unable to accept invitation."
    end
  end

  def reject_invitation
    @invitation = TeamInvitation.find_by!(token: params[:token])

    if @invitation.reject!
      redirect_to root_path, notice: "Invitation declined."
    else
      redirect_to root_path, alert: "Unable to decline invitation."
    end
  end

  private

  def set_team_member
    @team_member = current_account.users.find(params[:id])
  end

  def ensure_owner_or_admin
    unless current_user.owner? || current_user.admin?
      redirect_to team_members_path, alert: "Access denied. Only owners and admins can manage team members."
    end
  end

  def invitation_params
    # Only allow specific roles to prevent privilege escalation
    permitted_params = params.require(:team_invitation).permit(:email)
    # Explicitly validate and set role to prevent mass assignment vulnerability
    if %w[member admin].include?(params[:team_invitation][:role])
      permitted_params[:role] = params[:team_invitation][:role]
    else
      permitted_params[:role] = "member" # Default to safest role
    end
    permitted_params
  end

  def team_member_params
    # Only allow specific roles to prevent privilege escalation
    permitted_params = params.require(:user).permit()
    # Explicitly validate and set role to prevent mass assignment vulnerability
    if %w[member admin].include?(params[:user][:role])
      permitted_params[:role] = params[:user][:role]
    else
      permitted_params[:role] = "member" # Default to safest role
    end
    permitted_params
  end
end
