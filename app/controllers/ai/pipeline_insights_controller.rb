# frozen_string_literal: true

module Ai
  class PipelineInsightsController < ApplicationController
    before_action :authenticate_user!
    before_action :set_pipeline

    def show
      @assessment = Ai::DataValueAssessor.new(@pipeline).assess_revenue_potential
      @opportunities = Ai::DataValueAssessor.new(@pipeline).identify_monetization_opportunities
      @market_insights = Ai::DataValueAssessor.new(@pipeline).generate_market_insights

      respond_to do |format|
        format.html
        format.json do
          render json: {
            pipeline: {
              id: @pipeline.id,
              name: @pipeline.name,
              status: @pipeline.status
            },
            assessment: @assessment,
            opportunities: @opportunities,
            market_insights: @market_insights,
            generated_at: Time.current
          }
        end
      end
    end

    def opportunities
      @opportunities = Ai::DataValueAssessor.new(@pipeline).identify_monetization_opportunities

      respond_to do |format|
        format.json { render json: @opportunities }
        format.html { redirect_to ai_pipeline_insights_path(@pipeline) }
      end
    end

    def market_analysis
      @market_insights = Ai::DataValueAssessor.new(@pipeline).generate_market_insights

      respond_to do |format|
        format.json { render json: @market_insights }
        format.html { redirect_to ai_pipeline_insights_path(@pipeline) }
      end
    end

    def pricing_recommendations
      assessor = Ai::DataValueAssessor.new(@pipeline)
      @pricing = assessor.assess_revenue_potential[:pricing_strategy]
      @opportunities = assessor.identify_monetization_opportunities

      respond_to do |format|
        format.json do
          render json: {
            pipeline_id: @pipeline.id,
            pricing_strategy: @pricing,
            opportunities: @opportunities.map { |opp| opp[:suggested_pricing] },
            updated_at: Time.current
          }
        end
        format.html { redirect_to ai_pipeline_insights_path(@pipeline) }
      end
    end

    # Batch analysis for multiple pipelines
    def bulk_analysis
      pipelines = current_account.pipelines.active.includes(:pipeline_executions)

      @bulk_results = pipelines.map do |pipeline|
        assessor = Ai::DataValueAssessor.new(pipeline)

        {
          pipeline: {
            id: pipeline.id,
            name: pipeline.name,
            status: pipeline.status
          },
          revenue_score: assessor.assess_revenue_potential[:overall_score],
          monthly_estimate: assessor.assess_revenue_potential[:monthly_revenue_estimate],
          opportunity_level: assessor.assess_revenue_potential[:opportunity_level],
          top_opportunity: assessor.identify_monetization_opportunities.first,
          market_category: assessor.assess_revenue_potential[:market_category]
        }
      end

      # Sort by revenue potential
      @bulk_results.sort_by! { |result| -result[:revenue_score] }

      respond_to do |format|
        format.json { render json: @bulk_results }
        format.html
      end
    end

    private

    def set_pipeline
      @pipeline = current_account.pipelines.find(params[:pipeline_id] || params[:id])
    rescue ActiveRecord::RecordNotFound
      respond_to do |format|
        format.html { redirect_to pipelines_path, alert: "Pipeline not found." }
        format.json { render json: { error: "Pipeline not found" }, status: :not_found }
      end
    end
  end
end
