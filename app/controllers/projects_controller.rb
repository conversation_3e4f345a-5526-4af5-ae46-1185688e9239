class ProjectsController < ApplicationController
  layout "dashboard"

  before_action :authenticate_user!
  before_action :set_project, only: [:show, :edit, :update, :destroy]
  before_action :ensure_account_access
  before_action :set_sidebar_metrics

  def index
    @projects = current_account.projects
                              .includes(:created_by, :data_connectors)
                              .recent

    @project_stats = {
      total_projects: @projects.count,
      active_projects: @projects.active.count,
      total_connectors: current_account.data_connectors.count,
      active_connectors: current_account.data_connectors.active.count
    }

    respond_to do |format|
      format.html
      format.json { render json: projects_index_json }
    end
  end

  def show
    @data_connectors = @project.data_connectors
                              .includes(:created_by)
                              .recent
                              .limit(10)

    @health_summary = @project.health_summary
    @recent_activity = recent_project_activity

    respond_to do |format|
      format.html
      format.json { render json: project_json(@project) }
    end
  end

  def new
    @project = current_account.projects.build
  end

  def create
    @project = current_account.projects.build(project_params)
    @project.created_by = current_user

    if @project.save
      redirect_to @project, notice: 'Project was successfully created.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @project.update(project_params)
      redirect_to @project, notice: 'Project was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    if @project.can_be_deleted?
      @project.destroy
      redirect_to projects_path, notice: 'Project was successfully deleted.'
    else
      redirect_to @project, alert: 'Cannot delete project with existing data connectors or pipelines.'
    end
  end

  private

  def ensure_account_access
    redirect_to root_path unless current_account
  end

  def set_project
    @project = current_account.projects.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    redirect_to projects_path, alert: 'Project not found.'
  end

  def project_params
    params.require(:project).permit(:name, :description, :status)
  end

  def projects_index_json
    {
      projects: @projects.map { |project| project_summary(project) },
      stats: @project_stats,
      generated_at: Time.current
    }
  end

  def project_json(project)
    {
      project: project_summary(project),
      health_summary: @health_summary,
      data_connectors: @data_connectors.map { |connector| connector_summary(connector) },
      recent_activity: @recent_activity,
      generated_at: Time.current
    }
  end

  def project_summary(project)
    {
      id: project.id,
      name: project.name,
      description: project.description,
      status: project.status,
      connector_count: project.connector_count,
      active_connector_count: project.active_connector_count,
      created_by: project.created_by.full_name,
      created_at: project.created_at,
      updated_at: project.updated_at
    }
  end

  def connector_summary(connector)
    {
      id: connector.id,
      name: connector.name,
      connector_type: connector.connector_type,
      status: connector.status,
      test_status: connector.test_status,
      last_tested_at: connector.last_tested_at,
      created_at: connector.created_at
    }
  end

  def recent_project_activity
    # Get recent activity for this project
    activities = []
    
    # Recent connectors
    @project.data_connectors.recent.limit(5).each do |connector|
      activities << {
        type: 'connector_created',
        description: "Data connector '#{connector.name}' was created",
        timestamp: connector.created_at,
        user: connector.created_by.full_name
      }
    end

    # Sort by timestamp and return latest 10
    activities.sort_by { |a| a[:timestamp] }.reverse.first(10)
  end
end
