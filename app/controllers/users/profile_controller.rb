class Users::ProfileController < ApplicationController
  before_action :authenticate_user!
  
  layout "dashboard"

  # GET /profile
  def show
    @user = current_user
    @account = current_user.account
  end

  # PATCH /profile
  def update
    @user = current_user
    
    if @user.update(user_params)
      redirect_to users_profile_path, notice: 'Profile updated successfully.'
    else
      render :show
    end
  end

  private

  def user_params
    params.require(:user).permit(:first_name, :last_name, :email, :phone, :bio, :theme, :language, :timezone)
  end
end
