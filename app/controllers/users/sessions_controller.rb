class Users::SessionsController < Devise::SessionsController
  layout "application"

  # Override new to handle redirects for already signed in users
  def new
    if user_signed_in?
      target_url = after_sign_in_path_for(current_user)
      Rails.logger.info "Redirecting signed-in user to: #{target_url}"
      redirect_to target_url, allow_other_host: true
      return
    end
    super
  end

  # Override create to handle authentication and cross-subdomain redirects
  def create
    self.resource = warden.authenticate!(auth_options)
    set_flash_message!(:notice, :signed_in)
    sign_in(resource_name, resource)
    yield resource if block_given?

    target_url = after_sign_in_path_for(resource)
    respond_with resource, location: target_url
  rescue Warden::NotAuthenticated
    flash.now[:alert] = "Invalid email or password."
    self.resource = resource_class.new(email: params.dig(:user, :email))
    clean_up_passwords(resource)
    render :new, status: :unprocessable_entity
  end

  # After sign in, send to the account subdomain
  def after_sign_in_path_for(resource)
    # Always redirect to the user's account subdomain
    subdomain_root_url(subdomain: resource.account.subdomain)
  end

  protected

  # Override respond_with to handle cross-subdomain redirects
  def respond_with(resource, opts = {})
    # Only handle redirect logic if we have a location (successful authentication)
    if opts[:location] && resource.persisted?
      target_url = opts[:location]
      respond_to do |format|
        format.html { redirect_to target_url, allow_other_host: true }
        format.turbo_stream { redirect_to target_url, allow_other_host: true }
        format.any { redirect_to target_url, allow_other_host: true }
      end
    else
      # For all other cases (new action, failed auth), use default Devise behavior
      super
    end
  end
end
