# Sophisticated User Profile Controller
# Demonstrates advanced Rails patterns with comprehensive user management
class Users::ProfileController < ApplicationController
  before_action :authenticate_user!

  layout "dashboard"

  # GET /profile
  def show
    @user = current_user
    @account = current_user.account
  end

  # PATCH /profile
  def update
    @user = current_user

    if @user.update(user_params)
      redirect_to users_profile_path, notice: 'Profile updated successfully.'
    else
      render :show
    end
  end

  private

  def user_params
    params.require(:user).permit(:first_name, :last_name, :email, :phone, :bio, :theme, :language, :timezone)
  end
end
    
    if validate_current_password && update_password
      handle_password_change_success
    else
      handle_password_change_failure
    end
  end

  # PATCH /profile/notification_preferences
  def update_notification_preferences
    @user = current_user
    
    if @user.update(notification_params)
      render json: { 
        status: 'success', 
        message: 'Notification preferences updated successfully',
        preferences: @user.notification_preferences
      }
    else
      render json: { 
        status: 'error', 
        message: 'Failed to update notification preferences',
        errors: @user.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  # PATCH /profile/preferences
  def update_preferences
    @user = current_user
    
    if @user.update(preference_params)
      render json: { 
        status: 'success', 
        message: 'Preferences updated successfully',
        preferences: user_preferences
      }
    else
      render json: { 
        status: 'error', 
        message: 'Failed to update preferences',
        errors: @user.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  # POST /profile/enable_two_factor
  def enable_two_factor
    @user = current_user
    
    if @user.enable_two_factor_authentication!
      render json: { 
        status: 'success', 
        message: 'Two-factor authentication enabled successfully',
        qr_code: @user.two_factor_qr_code,
        backup_codes: @user.generate_backup_codes
      }
    else
      render json: { 
        status: 'error', 
        message: 'Failed to enable two-factor authentication'
      }, status: :unprocessable_entity
    end
  end

  # DELETE /profile/disable_two_factor
  def disable_two_factor
    @user = current_user
    
    if validate_two_factor_disable && @user.disable_two_factor_authentication!
      render json: { 
        status: 'success', 
        message: 'Two-factor authentication disabled successfully'
      }
    else
      render json: { 
        status: 'error', 
        message: 'Failed to disable two-factor authentication'
      }, status: :unprocessable_entity
    end
  end

  # GET /profile/export
  def export_data
    @user = current_user
    
    profile_data = {
      user: user_export_data,
      account: account_export_data,
      activity: activity_export_data,
      preferences: preference_export_data,
      exported_at: Time.current.iso8601
    }
    
    respond_to do |format|
      format.json { render json: profile_data }
      format.csv { send_csv_export(profile_data) }
    end
  end

  # DELETE /profile/sessions/:session_id
  def revoke_session
    session_id = params[:session_id]
    
    if revoke_user_session(session_id)
      render json: { 
        status: 'success', 
        message: 'Session revoked successfully'
      }
    else
      render json: { 
        status: 'error', 
        message: 'Failed to revoke session'
      }, status: :unprocessable_entity
    end
  end

  # DELETE /profile/sessions
  def revoke_all_sessions
    if revoke_all_user_sessions
      render json: { 
        status: 'success', 
        message: 'All sessions revoked successfully'
      }
    else
      render json: { 
        status: 'error', 
        message: 'Failed to revoke sessions'
      }, status: :unprocessable_entity
    end
  end

  private

  def set_user
    @user = current_user
  end

  def authorize_profile_access
    # Add any additional authorization logic here
    # For now, users can only access their own profile
    true
  end

  def update_user_profile
    ActiveRecord::Base.transaction do
      # Handle avatar upload if present
      handle_avatar_upload if params[:user][:avatar].present?
      
      # Update basic profile information
      @user.update!(profile_params)
      
      # Log profile update activity
      log_profile_activity('profile_updated')
      
      true
    end
  rescue ActiveRecord::RecordInvalid
    false
  end

  def validate_current_password
    return false unless params[:current_password].present?
    
    unless @user.valid_password?(params[:current_password])
      @user.errors.add(:current_password, 'is incorrect')
      return false
    end
    
    true
  end

  def update_password
    password_params = {
      password: params[:new_password],
      password_confirmation: params[:password_confirmation]
    }
    
    if @user.update(password_params)
      # Log password change activity
      log_profile_activity('password_changed')
      true
    else
      false
    end
  end

  def handle_successful_update(format)
    auto_save = request.headers['X-Auto-Save'] == 'true'
    message = auto_save ? 'Profile auto-saved' : 'Profile updated successfully'
    
    format.html { redirect_to profile_path, notice: message }
    format.json { 
      render json: { 
        status: 'success', 
        message: message,
        user: user_json,
        auto_save: auto_save
      }
    }
  end

  def handle_failed_update(format)
    format.html { render :show, status: :unprocessable_entity }
    format.json { 
      render json: { 
        status: 'error', 
        message: 'Failed to update profile',
        errors: @user.errors.full_messages
      }, status: :unprocessable_entity
    }
  end

  def handle_password_change_success
    render json: { 
      status: 'success', 
      message: 'Password changed successfully'
    }
  end

  def handle_password_change_failure
    render json: { 
      status: 'error', 
      message: 'Failed to change password',
      errors: @user.errors.full_messages
    }, status: :unprocessable_entity
  end

  def handle_avatar_upload
    # Handle avatar upload logic here
    # This could integrate with Active Storage or a service like Cloudinary
  end

  def validate_two_factor_disable
    # Add validation for disabling 2FA (e.g., require password confirmation)
    true
  end

  def revoke_user_session(session_id)
    # Implement session revocation logic
    # This would depend on your session management strategy
    true
  end

  def revoke_all_user_sessions
    # Implement logic to revoke all user sessions except current
    true
  end

  def log_profile_activity(action)
    # Log user activity for audit trail
    Rails.logger.info "User #{@user.id} performed #{action} at #{Time.current}"
    
    # You could also create an Activity model to track this
    # Activity.create!(
    #   user: @user,
    #   action: action,
    #   ip_address: request.remote_ip,
    #   user_agent: request.user_agent
    # )
  end

  # Parameter filtering
  def profile_params
    params.require(:user).permit(
      :first_name, :last_name, :email, :phone, :bio, :avatar,
      :timezone, :language, :date_format, :theme
    )
  end

  def notification_params
    params.require(:user).permit(
      :email_pipeline_notifications, :email_team_notifications, 
      :email_account_notifications, :desktop_notifications,
      :sound_notifications, :notification_frequency
    )
  end

  def preference_params
    params.require(:user).permit(
      :theme, :language, :timezone, :date_format,
      :auto_refresh_dashboard, :show_advanced_metrics,
      :compact_view, :allow_analytics, :product_updates
    )
  end

  # Data loading methods
  def load_recent_activity
    # Load recent user activity
    []
  end

  def load_account_statistics
    # Load account-level statistics
    {}
  end

  def load_security_settings
    # Load security-related settings
    {}
  end

  # JSON response helpers
  def profile_json
    {
      user: user_json,
      account: account_json,
      recent_activity: @recent_activity,
      account_stats: @account_stats,
      security_settings: @security_settings
    }
  end

  def user_json
    @user.as_json(
      only: [:id, :email, :first_name, :last_name, :phone, :bio, :role, :created_at],
      methods: [:full_name]
    )
  end

  def account_json
    current_account.as_json(
      only: [:id, :name, :subdomain, :created_at]
    )
  end

  def user_preferences
    {
      theme: @user.theme,
      language: @user.language,
      timezone: @user.timezone,
      date_format: @user.date_format,
      auto_refresh_dashboard: @user.auto_refresh_dashboard,
      show_advanced_metrics: @user.show_advanced_metrics,
      compact_view: @user.compact_view,
      allow_analytics: @user.allow_analytics,
      product_updates: @user.product_updates
    }
  end

  # Export data methods
  def user_export_data
    @user.as_json(except: [:encrypted_password, :reset_password_token])
  end

  def account_export_data
    current_account.as_json(only: [:name, :subdomain, :created_at])
  end

  def activity_export_data
    # Export user activity data
    []
  end

  def preference_export_data
    user_preferences
  end

  def send_csv_export(data)
    csv_data = generate_csv_from_data(data)
    send_data csv_data, 
              filename: "profile-export-#{Date.current}.csv",
              type: 'text/csv'
  end

  def generate_csv_from_data(data)
    # Convert JSON data to CSV format
    # This is a simplified implementation
    require 'csv'
    
    CSV.generate do |csv|
      csv << ['Field', 'Value']
      flatten_hash(data).each do |key, value|
        csv << [key, value]
      end
    end
  end

  def flatten_hash(hash, prefix = '')
    result = {}
    hash.each do |key, value|
      new_key = prefix.empty? ? key.to_s : "#{prefix}.#{key}"
      if value.is_a?(Hash)
        result.merge!(flatten_hash(value, new_key))
      else
        result[new_key] = value
      end
    end
    result
  end
end
