class OnboardingController < ApplicationController
  before_action :authenticate_user!
  before_action :set_account
  before_action :check_onboarding_status

  def index
    # Main onboarding dashboard
    @current_step = determine_current_step
    @progress = calculate_progress
  end

  def welcome
    # First step - welcome and account setup
    @account = current_account
  end

  def setup_profile
    # Step 2 - Complete user profile
  end

  def create_first_connection
    # Step 3 - Guide through creating first data connection
    @data_connector = current_account.data_connectors.build
    @existing_connectors = current_account.data_connectors.order(created_at: :desc).limit(5)

    @connection_types = [
      { value: "postgresql", name: "PostgreSQL", icon: "🐘", description: "Connect to PostgreSQL database" },
      { value: "mysql", name: "MySQL", icon: "🐬", description: "Connect to MySQL database" },
      { value: "rest_api", name: "REST API", icon: "🌐", description: "Connect to REST API endpoint" },
      { value: "csv_file", name: "CSV Upload", icon: "📊", description: "Upload CSV files" },
      { value: "webhook", name: "Webhook", icon: "🔗", description: "Receive data via webhooks" }
    ]
  end

  def create_first_pipeline
    # Step 4 - Guide through creating first pipeline
    @pipeline = current_account.pipelines.build
    @data_connectors = current_account.data_connectors.active.order(:name)

    # Pre-populate if we have connectors
    if @data_connectors.any?
      @suggested_source = @data_connectors.first
      @pipeline.source_config = {
        "type" => @suggested_source.connector_type,
        "connector_id" => @suggested_source.id
      }
    end

    @schedule_types = [
      { value: "manual", name: "Manual", description: "Run pipeline manually when needed" },
      { value: "real_time", name: "Real-time", description: "Process data as it arrives" },
      { value: "hourly", name: "Hourly", description: "Run every hour" },
      { value: "daily", name: "Daily", description: "Run once a day" }
    ]
  end

  def invite_team
    # Step 5 - Optional team invitation
  end

  def complete
    # Mark onboarding as complete
    @account.update!(
      onboarding_completed: true,
      onboarding_completed_at: Time.current
    )

    redirect_to root_path, notice: "Welcome to DataReflow! Your account is ready to use."
  end

  # Create connector during onboarding
  def create_connector
    @data_connector = current_account.data_connectors.build(connector_params)
    @data_connector.created_by = current_user

    if @data_connector.save
      # Mark connection step as complete
      @account.update!(onboarding_connection_completed: true)

      # Queue connection test
      DataConnectorTestJob.perform_later(@data_connector.id)

      render json: {
        success: true,
        connector: {
          id: @data_connector.id,
          name: @data_connector.name,
          type: @data_connector.connector_type,
          status: @data_connector.status
        },
        next_step: "pipeline",
        message: "Connection created successfully! Testing connection..."
      }
    else
      render json: {
        success: false,
        errors: @data_connector.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  # Create pipeline during onboarding
  def create_pipeline
    @pipeline = current_account.pipelines.build(pipeline_params)
    @pipeline.created_by = current_user

    if @pipeline.save
      # Mark pipeline step as complete
      @account.update!(onboarding_pipeline_completed: true)

      render json: {
        success: true,
        pipeline: {
          id: @pipeline.id,
          name: @pipeline.name,
          schedule: @pipeline.schedule_description,
          status: @pipeline.status
        },
        next_step: "team",
        message: "Pipeline created successfully!"
      }
    else
      render json: {
        success: false,
        errors: @pipeline.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  # AJAX endpoints for onboarding steps
  def mark_step_complete
    step = params[:step]

    case step
    when "welcome"
      @account.update!(onboarding_welcome_completed: true)
    when "profile"
      update_profile if params[:user].present?
      @account.update!(onboarding_profile_completed: true)
    when "connection"
      # Connection completion handled by create_connector
      @account.update!(onboarding_connection_completed: true) if params[:skip]
    when "pipeline"
      # Pipeline completion handled by create_pipeline
      @account.update!(onboarding_pipeline_completed: true) if params[:skip]
    when "team"
      @account.update!(onboarding_team_completed: true)
    end

    # Check if all steps are complete
    if all_steps_complete?
      @account.update!(
        onboarding_completed: true,
        onboarding_completed_at: Time.current
      )
    end

    render json: {
      success: true,
      next_step: determine_next_step(step),
      onboarding_completed: @account.onboarding_completed?
    }
  end

  private

  def set_account
    @account = current_account
  end

  def check_onboarding_status
    # If onboarding is already completed, redirect to dashboard
    if @account.onboarding_completed?
      redirect_to root_path and return unless action_name == "index"
    end
  end

  def determine_current_step
    return "welcome" unless @account.onboarding_welcome_completed?
    return "profile" unless @account.onboarding_profile_completed?
    return "connection" unless @account.onboarding_connection_completed?
    return "pipeline" unless @account.onboarding_pipeline_completed?
    return "team" unless @account.onboarding_team_completed?
    "complete"
  end

  def determine_next_step(current_step)
    steps = %w[welcome profile connection pipeline team complete]
    current_index = steps.index(current_step)
    return nil if current_index.nil? || current_index >= steps.length - 1

    steps[current_index + 1]
  end

  def calculate_progress
    total_steps = 5
    completed_steps = 0

    completed_steps += 1 if @account.onboarding_welcome_completed?
    completed_steps += 1 if @account.onboarding_profile_completed?
    completed_steps += 1 if @account.onboarding_connection_completed?
    completed_steps += 1 if @account.onboarding_pipeline_completed?
    completed_steps += 1 if @account.onboarding_team_completed?

    (completed_steps.to_f / total_steps * 100).round
  end

  def all_steps_complete?
    @account.onboarding_welcome_completed? &&
    @account.onboarding_profile_completed? &&
    @account.onboarding_connection_completed? &&
    @account.onboarding_pipeline_completed? &&
    @account.onboarding_team_completed?
  end

  def update_profile
    current_user.update(user_params)
  end

  def connector_params
    params.require(:data_connector).permit(
      :name, :connector_type, connection_config: {}
    )
  end

  def pipeline_params
    params.require(:pipeline).permit(
      :name, :description, :schedule_type,
      source_config: {},
      destination_config: {},
      transformation_rules: {},
      schedule_config: {}
    )
  end

  def user_params
    params.require(:user).permit(:first_name, :last_name, :time_zone)
  end
end
