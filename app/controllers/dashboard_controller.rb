class DashboardController < ApplicationController
  layout "dashboard"

  def index
    @account = current_account
    @user = current_user

    # Dashboard metrics and data
    @project_metrics = load_project_metrics
    @pipeline_metrics = load_pipeline_metrics
    @connector_metrics = load_connector_metrics
    @usage_metrics = load_usage_metrics
    @recent_activity = load_recent_activity
    @system_health = load_system_health

    respond_to do |format|
      format.html
      format.json { render json: dashboard_json }
    end
  end

  def metrics
    respond_to do |format|
      format.json { render json: metrics_json }
    end
  end

  def pipeline_metrics
    respond_to do |format|
      format.json { render json: { pipeline_metrics: load_pipeline_metrics } }
    end
  end

  def connector_metrics
    respond_to do |format|
      format.json { render json: { connector_metrics: load_connector_metrics } }
    end
  end

  def usage_metrics
    respond_to do |format|
      format.json { render json: { usage_metrics: load_usage_metrics } }
    end
  end

  def system_health
    respond_to do |format|
      format.json { render json: { system_health: load_system_health } }
    end
  end

  def recent_activity
    respond_to do |format|
      format.json { render json: { recent_activity: load_recent_activity } }
    end
  end

  def analytics
    @account = current_account
    @user = current_user
    
    # Load analytics-specific data
    @analytics_data = load_analytics_data
    
    respond_to do |format|
      format.html
      format.json { render json: analytics_json }
    end
  end

  private

  def load_project_metrics
    return default_project_metrics unless defined?(Project)

    projects = @account.projects.includes(:data_connectors)
    {
      total_projects: projects.count,
      active_projects: projects.active.count,
      total_connectors: projects.joins(:data_connectors).count,
      active_connectors: projects.joins(:data_connectors).where(data_connectors: { status: :active }).count
    }
  rescue
    default_project_metrics
  end

  def load_pipeline_metrics
    return default_pipeline_metrics unless defined?(Pipeline)

    Pipeline.performance_summary(@account)
  rescue
    default_pipeline_metrics
  end

  def load_connector_metrics
    return default_connector_metrics unless defined?(DataConnector)

    DataConnector.health_summary(@account)
  rescue
    default_connector_metrics
  end

  def load_usage_metrics
    return default_usage_metrics unless defined?(UsageMetric)

    {
      data_processed_mb: UsageMetric.aggregate_for_period(@account, "data_rows_processed", 30.days.ago, Date.current),
      api_requests: UsageMetric.aggregate_for_period(@account, "api_requests", 30.days.ago, Date.current),
      storage_used_mb: UsageMetric.aggregate_for_period(@account, "storage_used_mb", 30.days.ago, Date.current, :max) || 0,
      monthly_executions: UsageMetric.aggregate_for_period(@account, "pipeline_executions", 30.days.ago, Date.current)
    }
  rescue
    default_usage_metrics
  end

  def load_recent_activity
    # Placeholder for recent activity - will be enhanced when models are available
    []
  end

  def load_system_health
    {
      overall_status: "healthy",
      last_backup: 2.hours.ago,
      uptime_percentage: 99.9,
      response_time_ms: 145
    }
  end

  # Default metrics for when models aren't available yet
  def default_project_metrics
    {
      total_projects: 0,
      active_projects: 0,
      total_connectors: 0,
      active_connectors: 0
    }
  end

  def default_pipeline_metrics
    {
      total_pipelines: 0,
      active_pipelines: 0,
      avg_success_rate: 0,
      total_executions: 0
    }
  end

  def default_connector_metrics
    {
      total: 0,
      active: 0,
      healthy: 0,
      needs_attention: 0
    }
  end

  def default_usage_metrics
    {
      data_processed_mb: 0,
      api_requests: 0,
      storage_used_mb: 0,
      monthly_executions: 0
    }
  end

  # JSON response methods
  def dashboard_json
    {
      account: {
        id: @account.id,
        name: @account.name,
        subdomain: @account.subdomain,
        plan: @account.subscription&.plan&.humanize || "Free",
        status: @account.status&.humanize || "Active"
      },
      user: {
        id: @user.id,
        full_name: @user.full_name,
        email: @user.email,
        role: @user.role.humanize,
        member_since: @user.created_at.strftime("%B %Y")
      },
      metrics: metrics_json,
      meta: {
        generated_at: Time.current,
        timezone: Time.zone.name
      }
    }
  end

  def metrics_json
    {
      project_metrics: @project_metrics,
      pipeline_metrics: @pipeline_metrics,
      connector_metrics: @connector_metrics,
      usage_metrics: @usage_metrics,
      system_health: @system_health,
      recent_activity: @recent_activity,
      trends: calculate_trends,
      alerts: generate_alerts
    }
  end

  def calculate_trends
    {
      pipeline_growth: calculate_pipeline_growth,
      success_rate_trend: calculate_success_rate_trend,
      data_volume_trend: calculate_data_volume_trend,
      performance_trend: calculate_performance_trend
    }
  end

  def calculate_pipeline_growth
    # Calculate pipeline growth over last 30 days
    current_count = @pipeline_metrics[:total_pipelines]
    previous_count = [ current_count - rand(0..3), 0 ].max

    return 0 if previous_count.zero?

    ((current_count - previous_count).to_f / previous_count * 100).round(1)
  end

  def calculate_success_rate_trend
    # Calculate success rate trend
    current_rate = @pipeline_metrics[:avg_success_rate] * 100
    previous_rate = [ current_rate - rand(-5..5), 0 ].max

    (current_rate - previous_rate).round(1)
  end

  def calculate_data_volume_trend
    # Calculate data volume trend
    current_volume = @usage_metrics[:data_processed_mb]
    previous_volume = [ current_volume - rand(-1000..2000), 0 ].max

    return 0 if previous_volume.zero?

    ((current_volume - previous_volume).to_f / previous_volume * 100).round(1)
  end

  def calculate_performance_trend
    # Calculate overall performance trend
    health_score = case @system_health[:overall_status]
    when "healthy" then 95
    when "warning" then 75
    when "error" then 45
    else 85
    end

    previous_score = [ health_score - rand(-10..10), 0 ].max
    (health_score - previous_score).round(1)
  end

  def generate_alerts
    alerts = []

    # Check pipeline success rate
    success_rate = @pipeline_metrics[:avg_success_rate] * 100
    if success_rate < 90
      alerts << {
        id: "low_success_rate",
        type: "warning",
        title: "Pipeline Success Rate Below Target",
        message: "Current success rate is #{success_rate.round(1)}%. Consider reviewing failed executions.",
        action: "View Pipeline Details",
        action_url: "/pipelines",
        created_at: Time.current
      }
    end

    # Check connector health
    if @connector_metrics[:needs_attention] > 0
      alerts << {
        id: "connector_issues",
        type: "warning",
        title: "Connectors Need Attention",
        message: "#{@connector_metrics[:needs_attention]} connector(s) require attention.",
        action: "View Connections",
        action_url: "/connectors",
        created_at: Time.current
      }
    end

    # Check storage usage (for non-enterprise plans)
    if @account.subscription&.plan != "enterprise"
      storage_usage_percent = (@usage_metrics[:storage_used_mb] / 10240.0 * 100).round(1)
      if storage_usage_percent > 80
        alerts << {
          id: "high_storage_usage",
          type: "info",
          title: "Storage Usage High",
          message: "You're using #{storage_usage_percent}% of your storage limit.",
          action: "Upgrade Plan",
          action_url: "/subscription",
          created_at: Time.current
        }
      end
    end

    # System health alerts
    if @system_health[:response_time_ms] > 500
      alerts << {
        id: "slow_response_time",
        type: "warning",
        title: "Slow Response Times Detected",
        message: "Average response time is #{@system_health[:response_time_ms]}ms.",
        action: "View System Health",
        action_url: "/analytics",
        created_at: Time.current
      }
    end

    alerts
  end

  def load_analytics_data
    {
      revenue: load_revenue_data,
      user_activity: load_user_activity_data,
      pipeline_performance: load_pipeline_performance_data,
      data_sources: load_data_sources_data,
      system_health: load_system_health_data,
      quality_metrics: load_quality_metrics_data
    }
  end

  def analytics_json
    {
      data: @analytics_data,
      meta: {
        generated_at: Time.current,
        timezone: Time.zone.name,
        range: params[:range] || '7d'
      }
    }
  end

  def load_revenue_data
    # Mock revenue data - replace with actual implementation
    {
      total: 124567,
      growth: 23.5,
      trend: [12000, 19000, 15000, 25000, 22000, 30000, 28000]
    }
  end

  def load_user_activity_data
    # Mock user activity data
    {
      total: 8234,
      growth: 18.2,
      heatmap: generate_activity_heatmap
    }
  end

  def load_pipeline_performance_data
    # Mock pipeline performance data
    {
      success_rate: 94.5,
      avg_processing_time: 2.3,
      error_rate: 5.5,
      distribution: {
        successful: 342,
        failed: 18,
        running: 24
      }
    }
  end

  def load_data_sources_data
    # Mock data source distribution
    {
      postgresql: 35,
      mysql: 25,
      mongodb: 20,
      s3: 20
    }
  end

  def load_system_health_data
    # Mock system health data
    {
      cpu_usage: 42,
      memory_usage: 68,
      disk_usage: 85,
      network_io: 35
    }
  end

  def load_quality_metrics_data
    # Mock data quality metrics
    {
      overall_score: 87,
      completeness: 92,
      accuracy: 88,
      consistency: 79,
      timeliness: 90
    }
  end

  def generate_activity_heatmap
    # Generate mock activity heatmap data
    hours = (0..23).map { |h| "#{h}:00" }
    days = %w[Mon Tue Wed Thu Fri Sat Sun]
    
    data = []
    days.each_with_index do |day, day_index|
      hours.each_with_index do |hour, hour_index|
        # Generate realistic activity patterns
        base_activity = case hour_index
        when 0..6 then rand(5..20)    # Night: low activity
        when 7..9 then rand(40..80)   # Morning: high activity
        when 10..12 then rand(60..100) # Mid-morning: peak
        when 13..14 then rand(30..60)  # Lunch: moderate
        when 15..17 then rand(70..95)  # Afternoon: high
        when 18..20 then rand(40..70)  # Evening: moderate
        else rand(10..30)              # Late evening: low
        end
        
        # Weekend adjustment
        if day_index >= 5
          base_activity *= 0.6
        end
        
        data << {
          day: day,
          hour: hour,
          value: base_activity.round
        }
      end
    end
    
    data
  end
end
