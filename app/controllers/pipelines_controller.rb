class PipelinesController < ApplicationController
  layout "dashboard"

  before_action :authenticate_user!
  before_action :set_pipeline, only: [ :show, :edit, :update, :destroy, :execute, :toggle_status ]
  before_action :ensure_account_access
  before_action :set_sidebar_metrics

  def index
    @pipelines = current_account.pipelines
                                .includes(:created_by, :pipeline_executions)
                                .recent

    respond_to do |format|
      format.html
      format.json { render json: pipeline_index_json }
    end
  end

  def show
    @recent_executions = @pipeline.pipeline_executions
                                 .includes(:pipeline)
                                 .order(started_at: :desc)
                                 .limit(10)

    respond_to do |format|
      format.html
      format.json { render json: pipeline_show_json }
    end
  end

  def new
    @pipeline = current_account.pipelines.build
    @data_connectors = current_account.data_connectors.active.order(:name)
    @pipeline_templates = pipeline_templates

    # Pre-populate from onboarding if coming from there
    if params[:source_type].present?
      @pipeline.source_config = { "type" => params[:source_type] }
    end

    if params[:destination_type].present?
      @pipeline.destination_config = { "type" => params[:destination_type] }
    end
  end

  def validate
    @pipeline = current_account.pipelines.build(pipeline_params)
    @pipeline.created_by = current_user

    # Parse JSON config fields if they come in as strings
    parse_json_config_fields(@pipeline)

    errors = []

    # Validate pipeline configuration
    if @pipeline.valid?
      # Additional custom validations
      errors.concat(validate_source_config)
      errors.concat(validate_destination_config)
      errors.concat(validate_transformation_rules)
      errors.concat(validate_schedule_config)
    else
      errors.concat(@pipeline.errors.full_messages)
    end

    render json: {
      valid: errors.empty?,
      errors: errors
    }
  end

  def create
    @pipeline = current_account.pipelines.build(pipeline_params)
    @pipeline.created_by = current_user

    # Parse JSON config fields if they come in as strings
    parse_json_config_fields(@pipeline)

    # Handle auto-save requests
    if params[:auto_save] == 'true'
      if @pipeline.save(validate: false)
        render json: { success: true, message: 'Draft saved' }
      else
        render json: { success: false, errors: @pipeline.errors.full_messages }
      end
      return
    end

    if @pipeline.save
      # Update onboarding progress
      if !current_account.onboarding_pipeline_completed?
        current_account.update!(
          onboarding_pipeline_completed: true,
          onboarding_completed: all_onboarding_completed?,
          onboarding_completed_at: all_onboarding_completed? ? Time.current : nil
        )
      end

      respond_to do |format|
        format.html { redirect_to pipeline_path(@pipeline), notice: "Pipeline created successfully!" }
        format.json { render json: { pipeline: pipeline_json(@pipeline), redirect_url: pipeline_path(@pipeline) } }
      end
    else
      @data_connectors = current_account.data_connectors.active.order(:name)

      respond_to do |format|
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: { errors: @pipeline.errors }, status: :unprocessable_entity }
      end
    end
  end

  def edit
    @data_connectors = current_account.data_connectors.active.order(:name)
    @pipeline_templates = pipeline_templates
  end

  def update
    # Get the parameters and parse JSON config fields before assignment
    params_hash = pipeline_params.to_h

    # Parse JSON strings in the parameters
    [:source_config, :destination_config, :transformation_rules, :schedule_config].each do |field|
      if params_hash[field].is_a?(String) && params_hash[field].present?
        begin
          params_hash[field] = JSON.parse(params_hash[field])
        rescue JSON::ParserError => e
          Rails.logger.warn "Invalid JSON for #{field}: #{params_hash[field]}. Error: #{e.message}"
          params_hash[field] = {}
        end
      elsif params_hash[field].nil?
        params_hash[field] = {}
      end
    end

    if @pipeline.update(params_hash)
      respond_to do |format|
        format.html { redirect_to pipeline_path(@pipeline), notice: "Pipeline updated successfully!" }
        format.json { render json: { pipeline: pipeline_json(@pipeline) } }
      end
    else
      @data_connectors = current_account.data_connectors.active.order(:name)
      @pipeline_templates = pipeline_templates

      respond_to do |format|
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: { errors: @pipeline.errors }, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @pipeline.destroy!

    respond_to do |format|
      format.html { redirect_to pipelines_path, notice: "Pipeline deleted successfully." }
      format.json { render json: { message: "Pipeline deleted successfully" } }
    end
  end

  def execute
    if @pipeline.can_execute?
      # Queue pipeline execution job
      PipelineExecutionJob.perform_later(@pipeline)

      respond_to do |format|
        format.html { redirect_to pipeline_path(@pipeline), notice: "Pipeline execution started!" }
        format.json { render json: { message: "Pipeline execution started", status: "queued" } }
      end
    else
      respond_to do |format|
        format.html { redirect_to pipeline_path(@pipeline), alert: "Pipeline cannot be executed in current state." }
        format.json { render json: { error: "Pipeline cannot be executed" }, status: :unprocessable_entity }
      end
    end
  end

  def toggle_status
    new_status = @pipeline.active? ? :paused : :active

    if @pipeline.update(status: new_status)
      status_text = new_status == :active ? "activated" : "paused"

      respond_to do |format|
        format.html { redirect_to pipeline_path(@pipeline), notice: "Pipeline #{status_text} successfully!" }
        format.json { render json: { pipeline: pipeline_json(@pipeline), message: "Pipeline #{status_text}" } }
      end
    else
      respond_to do |format|
        format.html { redirect_to pipeline_path(@pipeline), alert: "Failed to update pipeline status." }
        format.json { render json: { errors: @pipeline.errors }, status: :unprocessable_entity }
      end
    end
  end

  private

  def set_pipeline
    @pipeline = current_account.pipelines.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    respond_to do |format|
      format.html { redirect_to pipelines_path, alert: "Pipeline not found." }
      format.json { render json: { error: "Pipeline not found" }, status: :not_found }
    end
  end

  def ensure_account_access
    redirect_to root_path unless current_account
  end

  def pipeline_params
    params.require(:pipeline).permit(
      :name, :description, :status, :schedule_type, :template_type,
      :source_config, :destination_config, :transformation_rules, :schedule_config
    )
  end

  def all_onboarding_completed?
    current_account.onboarding_welcome_completed? &&
    current_account.onboarding_profile_completed? &&
    current_account.onboarding_connection_completed? &&
    current_account.onboarding_pipeline_completed? &&
    current_account.onboarding_team_completed?
  end

  def pipeline_index_json
    {
      pipelines: @pipelines.map { |pipeline| pipeline_json(pipeline) },
      meta: {
        total: @pipelines.count,
        active: @pipelines.active.count,
        performance: Pipeline.performance_summary(current_account)
      }
    }
  end

  def pipeline_show_json
    {
      pipeline: pipeline_json(@pipeline),
      recent_executions: @recent_executions.map { |execution| execution_json(execution) },
      performance: {
        success_rate: @pipeline.success_rate,
        avg_execution_time: @pipeline.average_execution_time
      }
    }
  end

  def pipeline_json(pipeline)
    {
      id: pipeline.id,
      name: pipeline.name,
      description: pipeline.description,
      status: pipeline.status,
      source_type: pipeline.source_type,
      destination_type: pipeline.destination_type,
      schedule_description: pipeline.schedule_description,
      transformation_summary: pipeline.transformation_summary,
      success_rate: pipeline.success_rate,
      avg_execution_time: pipeline.average_execution_time,
      execution_count: pipeline.execution_count,
      last_executed_at: pipeline.last_executed_at,
      last_execution_status: pipeline.last_execution_status,
      can_execute: pipeline.can_execute?,
      created_at: pipeline.created_at,
      updated_at: pipeline.updated_at,
      created_by: {
        id: pipeline.created_by.id,
        name: pipeline.created_by.first_name + " " + pipeline.created_by.last_name
      }
    }
  end

  def execution_json(execution)
    {
      id: execution.id,
      status: execution.status,
      started_at: execution.started_at,
      completed_at: execution.completed_at,
      execution_time: execution.execution_time,
      records_processed: execution.records_processed,
      records_success: execution.records_success,
      records_failed: execution.records_failed,
      error_message: execution.error_message
    }
  end

  # Validation helper methods
  def validate_source_config
    errors = []
    return errors unless @pipeline.source_config.present?

    begin
      config = JSON.parse(@pipeline.source_config) if @pipeline.source_config.is_a?(String)
      config = @pipeline.source_config if @pipeline.source_config.is_a?(Hash)

      if config.blank?
        errors << "Source configuration cannot be empty"
      elsif !config.is_a?(Hash)
        errors << "Source configuration must be a valid JSON object"
      else
        # Validate required fields based on source type
        case config['type']
        when 'database'
          errors << "Database host is required" unless config['host'].present?
          errors << "Database name is required" unless config['database'].present?
        when 'api'
          errors << "API URL is required" unless config['url'].present?
        when 'file'
          errors << "File path is required" unless config['path'].present?
        end
      end
    rescue JSON::ParserError
      errors << "Source configuration must be valid JSON"
    end

    errors
  end

  def validate_destination_config
    errors = []
    return errors unless @pipeline.destination_config.present?

    begin
      config = JSON.parse(@pipeline.destination_config) if @pipeline.destination_config.is_a?(String)
      config = @pipeline.destination_config if @pipeline.destination_config.is_a?(Hash)

      if config.blank?
        errors << "Destination configuration cannot be empty"
      elsif !config.is_a?(Hash)
        errors << "Destination configuration must be a valid JSON object"
      else
        # Validate required fields based on destination type
        case config['type']
        when 'database'
          errors << "Database host is required" unless config['host'].present?
          errors << "Database name is required" unless config['database'].present?
        when 'api'
          errors << "API URL is required" unless config['url'].present?
        when 'file'
          errors << "File path is required" unless config['path'].present?
        end
      end
    rescue JSON::ParserError
      errors << "Destination configuration must be valid JSON"
    end

    errors
  end

  def validate_transformation_rules
    errors = []
    return errors unless @pipeline.transformation_rules.present?

    begin
      rules = JSON.parse(@pipeline.transformation_rules) if @pipeline.transformation_rules.is_a?(String)
      rules = @pipeline.transformation_rules if @pipeline.transformation_rules.is_a?(Hash)

      if rules.present? && !rules.is_a?(Hash)
        errors << "Transformation rules must be a valid JSON object"
      end
    rescue JSON::ParserError
      errors << "Transformation rules must be valid JSON"
    end

    errors
  end

  def validate_schedule_config
    errors = []
    return errors unless @pipeline.schedule_config.present?

    begin
      config = JSON.parse(@pipeline.schedule_config) if @pipeline.schedule_config.is_a?(String)
      config = @pipeline.schedule_config if @pipeline.schedule_config.is_a?(Hash)

      if config.present? && !config.is_a?(Hash)
        errors << "Schedule configuration must be a valid JSON object"
      elsif config.present? && config['cron'].present?
        # Basic cron validation
        cron_parts = config['cron'].split(' ')
        if cron_parts.length != 5
          errors << "Cron expression must have 5 parts (minute hour day month weekday)"
        end
      end
    rescue JSON::ParserError
      errors << "Schedule configuration must be valid JSON"
    end

    errors
  end

  def parse_json_config_fields(pipeline)
    # Parse JSON strings to hashes for config fields
    [:source_config, :destination_config, :transformation_rules, :schedule_config].each do |field|
      value = pipeline.send(field)
      if value.is_a?(String) && value.present?
        begin
          parsed_value = JSON.parse(value)
          pipeline.send("#{field}=", parsed_value)
        rescue JSON::ParserError => e
          # Log the error and leave as empty hash for invalid JSON
          Rails.logger.warn "Invalid JSON for #{field}: #{value}. Error: #{e.message}"
          pipeline.send("#{field}=", {})
        end
      elsif value.nil?
        # Set empty hash for nil values
        pipeline.send("#{field}=", {})
      end
    end
  end

  def pipeline_templates
    [
      {
        id: 'etl',
        name: 'ETL Pipeline',
        description: 'Extract, Transform, Load data between systems',
        icon: 'refresh',
        tags: ['Extract', 'Transform', 'Load'],
        default_schedule: 'hourly',
        suggested_config: {
          source: { type: 'database', query: 'SELECT * FROM source_table' },
          destination: { type: 'database', table: 'destination_table' },
          transformations: { rules: [{ type: 'mapping', fields: {} }] }
        }
      },
      {
        id: 'sync',
        name: 'Data Sync',
        description: 'Keep data synchronized between systems',
        icon: 'sync',
        tags: ['Real-time', 'Bi-directional', 'Incremental'],
        default_schedule: 'real-time',
        suggested_config: {
          source: { type: 'api', endpoint: '/api/data' },
          destination: { type: 'database', table: 'synced_data' },
          transformations: { rules: [] }
        }
      },
      {
        id: 'stream',
        name: 'Stream Processing',
        description: 'Process data streams in real-time',
        icon: 'lightning',
        tags: ['Real-time', 'Event-driven', 'Scalable'],
        default_schedule: 'continuous',
        suggested_config: {
          source: { type: 'stream', topic: 'data-stream' },
          destination: { type: 'database', table: 'processed_stream' },
          transformations: { rules: [{ type: 'filter', condition: 'value > 0' }] }
        }
      },
      {
        id: 'batch',
        name: 'Batch Processing',
        description: 'Process large datasets in scheduled batches',
        icon: 'database',
        tags: ['Scheduled', 'High-volume', 'Efficient'],
        default_schedule: 'daily',
        suggested_config: {
          source: { type: 'file', path: '/data/batch/*.csv' },
          destination: { type: 'database', table: 'batch_processed' },
          transformations: { rules: [{ type: 'aggregation', function: 'sum' }] }
        }
      },
      {
        id: 'migration',
        name: 'Data Migration',
        description: 'Migrate data between different systems',
        icon: 'upload',
        tags: ['One-time', 'Validation', 'Mapping'],
        default_schedule: 'manual',
        suggested_config: {
          source: { type: 'database', legacy: true },
          destination: { type: 'database', modern: true },
          transformations: { rules: [{ type: 'schema_mapping', mappings: {} }] }
        }
      },
      {
        id: 'custom',
        name: 'Custom Pipeline',
        description: 'Build a custom pipeline from scratch',
        icon: 'settings',
        tags: ['Flexible', 'Custom', 'Advanced'],
        default_schedule: 'manual',
        suggested_config: {
          source: { type: 'custom' },
          destination: { type: 'custom' },
          transformations: { rules: [] }
        }
      }
    ]
  end
end
