# frozen_string_literal: true

class SubscriptionsController < ApplicationController
  layout "dashboard"

  before_action :authenticate_user!
  before_action :ensure_owner_or_admin
  before_action :set_sidebar_metrics

  def show
    @subscription = current_account.subscription
    @subscription_plans = SubscriptionPlan.active.by_price
    @current_plan = @subscription.subscription_plan
    @stripe_subscription = subscription_service.retrieve_subscription if @subscription.stripe_subscription_id
  end

  def create
    subscription_plan = SubscriptionPlan.find(params[:subscription_plan_id])

    begin
      stripe_subscription = subscription_service.create_subscription(
        subscription_plan.stripe_price_id,
        trial_days: 14
      )

      if stripe_subscription.latest_invoice.payment_intent&.status == "requires_action"
        redirect_to confirm_subscription_path(
          client_secret: stripe_subscription.latest_invoice.payment_intent.client_secret
        )
      else
        redirect_to subscription_path, notice: "Subscription created successfully!"
      end
    rescue Stripe::SubscriptionService::Error => e
      redirect_to subscription_path, alert: "Failed to create subscription: #{e.message}"
    end
  end

  def update
    subscription_plan = SubscriptionPlan.find(params[:subscription_plan_id])

    begin
      subscription_service.update_subscription(subscription_plan.stripe_price_id)
      redirect_to subscription_path, notice: "Subscription updated successfully!"
    rescue Stripe::SubscriptionService::Error => e
      redirect_to subscription_path, alert: "Failed to update subscription: #{e.message}"
    end
  end

  def cancel
    begin
      subscription_service.cancel_subscription
      redirect_to subscription_path, notice: "Subscription will be canceled at the end of the billing period."
    rescue Stripe::SubscriptionService::Error => e
      redirect_to subscription_path, alert: "Failed to cancel subscription: #{e.message}"
    end
  end

  def confirm
    @client_secret = params[:client_secret]
    @publishable_key = Rails.configuration.stripe[:publishable_key]
  end

  private

  def ensure_owner_or_admin
    redirect_to root_path, alert: "Access denied" unless current_user.owner? || current_user.admin?
  end

  def subscription_service
    @subscription_service ||= Stripe::SubscriptionService.new(current_account)
  end
end
