class DataConnectorsController < ApplicationController
  layout "dashboard"

  before_action :authenticate_user!
  before_action :set_project, except: [:index]
  before_action :set_data_connector, only: [ :show, :edit, :update, :destroy, :test_connection ]
  before_action :ensure_account_access
  before_action :set_sidebar_metrics

  def index
    if @project
      # Project-scoped view
      @data_connectors = @project.data_connectors
                                .includes(:created_by)
                                .recent
      @health_summary = @project.health_summary
    else
      # Legacy route - redirect to first project or show all
      first_project = current_account.projects.active.first
      if first_project
        redirect_to project_data_connectors_path(first_project)
        return
      else
        # No projects exist, show all connectors (backward compatibility)
        @data_connectors = current_account.data_connectors
                                         .includes(:created_by, :project)
                                         .recent
        @health_summary = DataConnector.health_summary(current_account)
      end
    end

    respond_to do |format|
      format.html
      format.json { render json: connector_index_json }
    end
  end

  def show
    respond_to do |format|
      format.html
      format.json { render json: connector_json(@data_connector) }
    end
  end

  def new
    @data_connector = @project.data_connectors.build
    @data_connector.created_by = current_user
    @connector_types = DataConnector.available_types
  end

  def create
    @data_connector = @project.data_connectors.build(data_connector_params)
    @data_connector.created_by = current_user

    # Handle auto-save requests
    if params[:auto_save] == 'true'
      if @data_connector.save(validate: false)
        render json: { success: true, message: 'Draft saved' }
      else
        render json: { success: false, errors: @data_connector.errors.full_messages }
      end
      return
    end

    if @data_connector.save
      # Update onboarding if this is the first connector
      if !current_account.onboarding_connection_completed?
        current_account.update!(onboarding_connection_completed: true)
      end

      respond_to do |format|
        format.html { redirect_to project_data_connector_path(@project, @data_connector), notice: "Data connector created successfully!" }
        format.json { render json: { connector: connector_json(@data_connector) } }
      end
    else
      @connector_types = DataConnector.available_types

      respond_to do |format|
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: { errors: @data_connector.errors }, status: :unprocessable_entity }
      end
    end
  end

  def edit
    @connector_types = DataConnector.available_types
  end

  def update
    # Don't allow changing connector type on existing connectors
    update_params = data_connector_params.except(:connector_type)

    if @data_connector.update(update_params)
      respond_to do |format|
        format.html {
          redirect_path = @project ? project_data_connector_path(@project, @data_connector) : data_connector_path(@data_connector)
          redirect_to redirect_path, notice: "Data connector updated successfully!"
        }
        format.json { render json: { connector: connector_json(@data_connector) } }
      end
    else
      @connector_types = DataConnector.available_types

      respond_to do |format|
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: { errors: @data_connector.errors }, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    # Check if connector is used in any pipelines
    pipelines_using_connector = current_account.pipelines.where(
      "source_config->>'connector_id' = ? OR destination_config->>'connector_id' = ?",
      @data_connector.id.to_s, @data_connector.id.to_s
    )

    if pipelines_using_connector.any?
      error_message = "Cannot delete connector - it's used by #{pipelines_using_connector.count} pipeline(s)"

      respond_to do |format|
        format.html {
          redirect_path = @project ? project_data_connectors_path(@project) : data_connectors_path
          redirect_to redirect_path, alert: error_message
        }
        format.json { render json: { error: error_message }, status: :unprocessable_entity }
      end
    else
      @data_connector.destroy!

      respond_to do |format|
        format.html {
          redirect_path = @project ? project_data_connectors_path(@project) : data_connectors_path
          redirect_to redirect_path, notice: "Data connector deleted successfully."
        }
        format.json { render json: { message: "Data connector deleted successfully" } }
      end
    end
  end

  def test_connection
    # Queue connection test job
    DataConnectorTestJob.perform_later(@data_connector.id)

    # Update status to testing
    @data_connector.update!(test_status: :test_in_progress)

    respond_to do |format|
      format.html {
        redirect_path = @project ? project_data_connector_path(@project, @data_connector) : data_connector_path(@data_connector)
        redirect_to redirect_path, notice: "Connection test started!"
      }
      format.json {
        render json: {
          message: "Connection test started",
          connector: connector_json(@data_connector)
        }
      }
    end
  end

  private

  def set_project
    @project = current_account.projects.find(params[:project_id])
  rescue ActiveRecord::RecordNotFound
    redirect_to projects_path, alert: "Project not found."
  end

  def set_data_connector
    if @project
      @data_connector = @project.data_connectors.find(params[:id])
    else
      @data_connector = current_account.data_connectors.find(params[:id])
    end
  rescue ActiveRecord::RecordNotFound
    respond_to do |format|
      format.html {
        redirect_path = @project ? project_data_connectors_path(@project) : data_connectors_path
        redirect_to redirect_path, alert: "Data connector not found."
      }
      format.json { render json: { error: "Data connector not found" }, status: :not_found }
    end
  end

  def ensure_account_access
    redirect_to root_path unless current_account
  end

  def data_connector_params
    params.require(:data_connector).permit(
      :name, :connector_type, connection_config: {}
    )
  end

  def connector_index_json
    {
      connectors: @data_connectors.map { |connector| connector_json(connector) },
      health_summary: @health_summary,
      available_types: DataConnector.available_types
    }
  end

  def connector_json(connector)
    {
      id: connector.id,
      name: connector.name,
      display_name: connector.display_name,
      connector_type: connector.connector_type,
      status: connector.status,
      test_status: connector.test_status,
      connection_summary: connector.connection_summary,
      connection_healthy: connector.connection_healthy?,
      needs_testing: connector.needs_testing?,
      last_tested_at: connector.last_tested_at,
      test_result: connector.test_result,
      created_at: connector.created_at,
      updated_at: connector.updated_at,
      created_by: {
        id: connector.created_by.id,
        name: "#{connector.created_by.first_name} #{connector.created_by.last_name}"
      }
    }
  end
end
