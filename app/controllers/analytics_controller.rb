class AnalyticsController < ApplicationController
  layout "dashboard"

  before_action :authenticate_user!
  before_action :ensure_account_access
  before_action :check_analytics_access
  before_action :set_sidebar_metrics

  def index
    @analytics_data = build_analytics_data
    @date_range = params[:date_range] || "30_days"
    @chart_data = build_chart_data(@date_range)

    respond_to do |format|
      format.html
      format.json { render json: analytics_json }
    end
  end

  def pipeline_performance
    @pipeline_analytics = build_pipeline_analytics

    respond_to do |format|
      format.html { redirect_to analytics_path, notice: "Pipeline performance details coming soon!" }
      format.json { render json: @pipeline_analytics }
    end
  end

  def data_quality
    @quality_metrics = build_data_quality_metrics

    respond_to do |format|
      format.html { redirect_to analytics_path, notice: "Data quality metrics coming soon!" }
      format.json { render json: @quality_metrics }
    end
  end

  def usage_trends
    @usage_data = build_usage_trends

    respond_to do |format|
      format.html { redirect_to analytics_path, notice: "Usage trends coming soon!" }
      format.json { render json: @usage_data }
    end
  end

  private

  def ensure_account_access
    redirect_to root_path unless current_account
  end

  def check_analytics_access
    unless feature_available?(:advanced_analytics)
      redirect_to subscription_path,
                  alert: "Advanced analytics requires a Professional or Enterprise plan. Please upgrade to access this feature."
    end
  end

  def feature_available?(feature)
    return true if Rails.env.development? # Allow in development

    case feature
    when :advanced_analytics
      %w[professional enterprise].include?(current_account.subscription&.plan)
    else
      true
    end
  end

  def build_analytics_data
    return default_analytics_data unless current_account

    end_date = Date.current
    start_date = case params[:date_range]
    when "7_days" then 7.days.ago.to_date
    when "30_days" then 30.days.ago.to_date
    when "90_days" then 90.days.ago.to_date
    else 30.days.ago.to_date
    end

    {
      overview: {
        total_pipelines: current_account.pipelines.count,
        active_pipelines: current_account.pipelines.where(status: "active").count,
        total_executions: pipeline_executions_in_range(start_date, end_date).count,
        success_rate: calculate_success_rate(start_date, end_date),
        avg_execution_time: calculate_avg_execution_time(start_date, end_date),
        data_processed: calculate_data_processed(start_date, end_date)
      },
      trends: {
        daily_executions: daily_execution_counts(start_date, end_date),
        success_rates: daily_success_rates(start_date, end_date),
        data_volume: daily_data_volume(start_date, end_date)
      },
      top_performers: {
        pipelines: top_performing_pipelines(5),
        connectors: top_performing_connectors(5)
      },
      alerts: build_analytics_alerts
    }
  end

  def default_analytics_data
    {
      overview: {
        total_pipelines: 0,
        active_pipelines: 0,
        total_executions: 0,
        success_rate: 0,
        avg_execution_time: 0,
        data_processed: 0
      },
      trends: {
        daily_executions: [],
        success_rates: [],
        data_volume: []
      },
      top_performers: {
        pipelines: [],
        connectors: []
      },
      alerts: []
    }
  end

  def build_chart_data(date_range)
    end_date = Date.current
    start_date = case date_range
    when "7_days" then 7.days.ago.to_date
    when "30_days" then 30.days.ago.to_date
    when "90_days" then 90.days.ago.to_date
    else 30.days.ago.to_date
    end

    {
      execution_trends: {
        labels: (start_date..end_date).map { |d| d.strftime("%m/%d") },
        datasets: [
          {
            label: "Successful Executions",
            data: daily_successful_executions(start_date, end_date),
            borderColor: "#10B981",
            backgroundColor: "rgba(16, 185, 129, 0.1)"
          },
          {
            label: "Failed Executions",
            data: daily_failed_executions(start_date, end_date),
            borderColor: "#EF4444",
            backgroundColor: "rgba(239, 68, 68, 0.1)"
          }
        ]
      },
      data_volume: {
        labels: (start_date..end_date).map { |d| d.strftime("%m/%d") },
        datasets: [
          {
            label: "Records Processed",
            data: daily_records_processed(start_date, end_date),
            borderColor: "#3B82F6",
            backgroundColor: "rgba(59, 130, 246, 0.1)"
          }
        ]
      }
    }
  end

  def build_pipeline_analytics
    return { performance_by_pipeline: [], execution_patterns: {}, bottlenecks: [] } unless current_account

    {
      performance_by_pipeline: current_account.pipelines.limit(10).map do |pipeline|
        {
          id: pipeline.id,
          name: pipeline.name,
          success_rate: pipeline.respond_to?(:success_rate) ? pipeline.success_rate : 0.95,
          avg_execution_time: pipeline.respond_to?(:average_execution_time) ? pipeline.average_execution_time : rand(30..300),
          total_executions: pipeline.respond_to?(:pipeline_executions) ? pipeline.pipeline_executions.count : rand(10..100),
          last_execution: pipeline.respond_to?(:last_executed_at) ? pipeline.last_executed_at : rand(1..24).hours.ago
        }
      end,
      execution_patterns: build_execution_patterns,
      bottlenecks: identify_bottlenecks
    }
  rescue => e
    Rails.logger.error "Error building pipeline analytics: #{e.message}"
    { performance_by_pipeline: [], execution_patterns: {}, bottlenecks: [] }
  end

  def build_data_quality_metrics
    {
      overall_score: calculate_overall_quality_score,
      quality_trends: build_quality_trends,
      issue_breakdown: build_quality_issue_breakdown,
      recommendations: build_quality_recommendations
    }
  end

  def build_usage_trends
    {
      storage_usage: build_storage_trends,
      api_usage: build_api_usage_trends,
      team_activity: build_team_activity_trends,
      feature_adoption: build_feature_adoption_metrics
    }
  end

  # Helper methods for data calculation
  def pipeline_executions_in_range(start_date, end_date)
    # This would need to be implemented based on your PipelineExecution model
    # For now, return empty relation
    current_account.pipelines.joins(:pipeline_executions)
                   .where(pipeline_executions: { started_at: start_date..end_date })
                   .distinct
  rescue
    current_account.pipelines.none
  end

  def calculate_success_rate(start_date, end_date)
    # Placeholder implementation
    85.5
  end

  def calculate_avg_execution_time(start_date, end_date)
    # Placeholder implementation
    145.2
  end

  def calculate_data_processed(start_date, end_date)
    # Placeholder implementation
    1_250_000
  end

  def daily_execution_counts(start_date, end_date)
    # Placeholder implementation
    (start_date..end_date).map { rand(10..50) }
  end

  def daily_success_rates(start_date, end_date)
    # Placeholder implementation
    (start_date..end_date).map { rand(80..95) }
  end

  def daily_data_volume(start_date, end_date)
    # Placeholder implementation
    (start_date..end_date).map { rand(1000..10000) }
  end

  def daily_successful_executions(start_date, end_date)
    (start_date..end_date).map { rand(8..45) }
  end

  def daily_failed_executions(start_date, end_date)
    (start_date..end_date).map { rand(0..5) }
  end

  def daily_records_processed(start_date, end_date)
    (start_date..end_date).map { rand(500..5000) }
  end

  def top_performing_pipelines(limit)
    return [] unless current_account

    current_account.pipelines.limit(limit).map do |pipeline|
      {
        id: pipeline.id,
        name: pipeline.name,
        success_rate: pipeline.respond_to?(:success_rate) ? pipeline.success_rate : 0.95,
        executions: pipeline.respond_to?(:execution_count) ? (pipeline.execution_count || 0) : rand(10..100)
      }
    end
  rescue => e
    Rails.logger.error "Error loading top performing pipelines: #{e.message}"
    []
  end

  def top_performing_connectors(limit)
    return [] unless current_account

    current_account.data_connectors.limit(limit).map do |connector|
      {
        id: connector.id,
        name: connector.name,
        type: connector.respond_to?(:connector_type) ? connector.connector_type : "database",
        status: connector.respond_to?(:status) ? connector.status : "active",
        health_score: rand(85..99)
      }
    end
  rescue => e
    Rails.logger.error "Error loading top performing connectors: #{e.message}"
    []
  end

  def build_analytics_alerts
    alerts = []

    return alerts unless current_account

    # Check for performance issues
    begin
      success_rate = calculate_success_rate(7.days.ago, Date.current)
      if success_rate < 90
        alerts << {
          type: "warning",
          title: "Pipeline Success Rate Below Target",
          message: "Your pipeline success rate has dropped below 90% in the last 7 days.",
          action: "Review failed executions",
          action_url: "/pipelines"
        }
      end
    rescue => e
      Rails.logger.error "Error calculating success rate: #{e.message}"
    end

    # Check for storage usage
    begin
      if current_account.subscription&.plan != "enterprise"
        storage_usage = rand(60..95)
        if storage_usage > 80
          alerts << {
            type: "info",
            title: "Storage Usage High",
            message: "You're using #{storage_usage}% of your storage limit.",
            action: "Consider upgrading your plan",
            action_url: "/subscription"
          }
        end
      end
    rescue => e
      Rails.logger.error "Error checking storage usage: #{e.message}"
    end

    alerts
  end

  def build_execution_patterns
    # Placeholder for execution pattern analysis
    {
      peak_hours: [ 9, 10, 14, 15 ],
      busiest_days: [ "Monday", "Tuesday", "Wednesday" ],
      seasonal_trends: "Stable"
    }
  end

  def identify_bottlenecks
    # Placeholder for bottleneck identification
    [
      {
        type: "slow_connector",
        name: "Database Connection Pool",
        impact: "High",
        recommendation: "Increase connection pool size"
      }
    ]
  end

  def calculate_overall_quality_score
    rand(85..95)
  end

  def build_quality_trends
    # Placeholder for quality trends
    []
  end

  def build_quality_issue_breakdown
    # Placeholder for quality issues
    {}
  end

  def build_quality_recommendations
    # Placeholder for quality recommendations
    []
  end

  def build_storage_trends
    # Placeholder for storage trends
    {}
  end

  def build_api_usage_trends
    # Placeholder for API usage
    {}
  end

  def build_team_activity_trends
    # Placeholder for team activity
    {}
  end

  def build_feature_adoption_metrics
    # Placeholder for feature adoption
    {}
  end

  def analytics_json
    {
      analytics: @analytics_data,
      charts: @chart_data,
      meta: {
        date_range: @date_range,
        generated_at: Time.current,
        account: {
          id: current_account.id,
          name: current_account.name,
          plan: current_account.subscription&.plan
        }
      }
    }
  end
end
