class Agent::RecommendationsController < Agent::BaseController
  before_action :set_recommendation, only: [ :show, :accept, :reject, :implement ]
  before_action :check_agent_limits, only: [ :generate, :refresh ]

  def index
    @recommendations = @current_account.agent_recommendations
                                      .includes(:pipeline)
                                      .order(created_at: :desc)
                                      .page(params[:page])
                                      .per(20)

    @revenue_summary = calculate_revenue_summary
    @adoption_rate = @current_account.agent_adoption_rate
    @total_value_generated = calculate_total_value_generated

    # Filter by status if requested
    if params[:status].present?
      @recommendations = @recommendations.where(status: params[:status])
    end

    # Filter by type if requested
    if params[:type].present?
      @recommendations = @recommendations.where(recommendation_type: params[:type])
    end

    respond_to do |format|
      format.html
      format.json {
        render json: {
          recommendations: @recommendations.as_json(include: :pipeline),
          summary: {
            revenue_summary: @revenue_summary,
            adoption_rate: @adoption_rate,
            total_value: @total_value_generated
          }
        }
      }
    end
  end

  def show
    @pipeline = @recommendation.pipeline
    @implementation_steps = @recommendation.implementation_steps
    @similar_recommendations = find_similar_recommendations

    respond_to do |format|
      format.html
      format.json { render json: @recommendation.as_json(include: :pipeline) }
    end
  end

  def accept
    if @recommendation.update(status: :accepted)
      # Track the acceptance
      track_recommendation_event(:accepted)

      # Queue implementation job if auto-implementation is available
      if auto_implementable?(@recommendation)
        ImplementRecommendationJob.perform_later(@recommendation.id)
        flash[:notice] = "Recommendation accepted and implementation started automatically."
      else
        flash[:notice] = "Recommendation accepted. Please follow the implementation steps."
      end

      # Generate revenue for accepted recommendations
      generate_acceptance_revenue
    else
      flash[:alert] = "Failed to accept recommendation."
    end

    respond_to do |format|
      format.html { redirect_to agent_recommendations_path }
      format.json { render json: { status: "accepted", message: "Recommendation accepted successfully" } }
    end
  end

  def reject
    reason = params[:rejection_reason] || "Not specified"

    if @recommendation.update(
      status: :rejected,
      ai_analysis: @recommendation.ai_analysis.merge({
        rejection_reason: reason,
        rejected_at: Time.current
      })
    )
      track_recommendation_event(:rejected, { reason: reason })
      flash[:notice] = "Recommendation rejected."
    else
      flash[:alert] = "Failed to reject recommendation."
    end

    respond_to do |format|
      format.html { redirect_to agent_recommendations_path }
      format.json { render json: { status: "rejected", message: "Recommendation rejected" } }
    end
  end

  def implement
    # Manual implementation tracking
    if @recommendation.pending? || @recommendation.accepted?
      @recommendation.update!(
        status: :implemented,
        implemented_at: Time.current,
        ai_analysis: @recommendation.ai_analysis.merge({
          implementation_method: "manual",
          implemented_by_user_id: current_user.id
        })
      )

      # Generate revenue for implemented recommendations
      generate_implementation_revenue

      track_recommendation_event(:implemented)

      flash[:success] = "Recommendation marked as implemented. Thank you for the feedback!"
    else
      flash[:alert] = "This recommendation cannot be implemented."
    end

    respond_to do |format|
      format.html { redirect_to agent_recommendations_path }
      format.json { render json: { status: "implemented", message: "Implementation recorded successfully" } }
    end
  end

  def generate
    # Generate new recommendations for the account
    generated_count = 0

    @current_account.pipelines.active.each do |pipeline|
      # Pipeline Intelligence recommendations
      intelligence_service = PipelineIntelligenceService.new(pipeline: pipeline, account: @current_account)
      intelligence_recommendations = intelligence_service.analyze_pipeline_performance
      generated_count += intelligence_recommendations.count if intelligence_recommendations.respond_to?(:count)

      # Data Quality recommendations
      quality_service = DataQualityGuardService.new(pipeline: pipeline, account: @current_account)
      quality_recommendations = quality_service.analyze_data_quality
      generated_count += quality_recommendations.count if quality_recommendations.respond_to?(:count)
    end

    # Template recommendations
    template_service = TemplateMarketplaceService.new(account: @current_account)
    template_recommendations = template_service.recommend_templates_for_account(5)
    generated_count += template_recommendations.count if template_recommendations.respond_to?(:count)

    flash[:success] = "Generated #{generated_count} new recommendations."
    redirect_to agent_recommendations_path
  end

  def refresh
    # Refresh specific recommendation or all pending ones
    if params[:recommendation_id]
      recommendation = @current_account.agent_recommendations.find(params[:recommendation_id])
      # Refresh logic for specific recommendation
      flash[:notice] = "Recommendation refreshed."
    else
      # Refresh all pending recommendations
      GenerateRecommendationsJob.perform_later(@current_account.id)
      flash[:notice] = "All recommendations are being refreshed in the background."
    end

    redirect_to agent_recommendations_path
  end

  def analytics
    @period = params[:period]&.to_i&.days || 30.days

    @analytics_data = {
      recommendations_by_type: recommendations_by_type_data,
      acceptance_rate: calculate_acceptance_rate,
      implementation_rate: calculate_implementation_rate,
      value_generated: calculate_period_value(@period),
      trends: calculate_recommendation_trends(@period),
      top_performing_recommendations: top_performing_recommendations,
      pipeline_performance: pipeline_performance_data
    }

    respond_to do |format|
      format.html
      format.json { render json: @analytics_data }
    end
  end

  private

  def set_recommendation
    @recommendation = @current_account.agent_recommendations.find(params[:id])
  end

  def calculate_revenue_summary
    {
      this_month: @current_account.agent_revenue_this_month,
      total: @current_account.agent_revenues.sum(:amount_cents) / 100.0,
      average_per_recommendation: calculate_average_revenue_per_recommendation
    }
  end

  def calculate_average_revenue_per_recommendation
    implemented_count = @current_account.agent_recommendations.implemented.count
    return 0 if implemented_count.zero?

    total_revenue = @current_account.agent_revenues.sum(:amount_cents) / 100.0
    (total_revenue / implemented_count).round(2)
  end

  def calculate_total_value_generated
    @current_account.agent_recommendations
                   .where.not(revenue_generated_cents: 0)
                   .sum(:revenue_generated_cents) / 100.0
  end

  def find_similar_recommendations
    @current_account.agent_recommendations
                   .where(recommendation_type: @recommendation.recommendation_type)
                   .where.not(id: @recommendation.id)
                   .order(confidence_score: :desc)
                   .limit(3)
  end

  def auto_implementable?(recommendation)
    # Simple optimization recommendations can be auto-implemented
    recommendation.optimization? && recommendation.confidence_score > 80
  end

  def track_recommendation_event(event, metadata = {})
    UsageMetric.create!(
      account: @current_account,
      metric_type: "recommendation_#{event}",
      value: 1,
      recorded_at: Time.current,
      metadata: {
        recommendation_id: @recommendation.id,
        recommendation_type: @recommendation.recommendation_type,
        confidence_score: @recommendation.confidence_score
      }.merge(metadata)
    )
  end

  def generate_acceptance_revenue
    # Generate small revenue for accepted recommendations
    AgentRevenue.create!(
      account: @current_account,
      agent_recommendation: @recommendation,
      revenue_source: :optimization_fee,
      amount_cents: 50, # $0.50 per accepted recommendation
      description: "Recommendation acceptance: #{@recommendation.title}",
      performance_metrics: {
        recommendation_id: @recommendation.id,
        event_type: "acceptance",
        confidence_score: @recommendation.confidence_score
      }
    )
  end

  def generate_implementation_revenue
    # Generate larger revenue for implemented recommendations
    base_revenue = (@recommendation.estimated_value || 10.0) * 0.1 # 10% of estimated value
    revenue_amount = [ base_revenue, 50.0 ].min # Cap at $50

    AgentRevenue.create!(
      account: @current_account,
      agent_recommendation: @recommendation,
      revenue_source: :optimization_fee,
      amount_cents: (revenue_amount * 100).to_i,
      description: "Recommendation implementation: #{@recommendation.title}",
      performance_metrics: {
        recommendation_id: @recommendation.id,
        event_type: "implementation",
        estimated_value: @recommendation.estimated_value,
        actual_revenue: revenue_amount
      }
    )
  end

  def recommendations_by_type_data
    @current_account.agent_recommendations
                   .group(:recommendation_type)
                   .group(:status)
                   .count
  end

  def calculate_acceptance_rate
    total = @current_account.agent_recommendations.count
    return 0 if total.zero?

    accepted = @current_account.agent_recommendations.where(status: [ :accepted, :implemented ]).count
    ((accepted.to_f / total) * 100).round(1)
  end

  def calculate_implementation_rate
    accepted = @current_account.agent_recommendations.where(status: [ :accepted, :implemented ]).count
    return 0 if accepted.zero?

    implemented = @current_account.agent_recommendations.implemented.count
    ((implemented.to_f / accepted) * 100).round(1)
  end

  def calculate_period_value(period)
    @current_account.agent_recommendations
                   .where(created_at: period.ago..Time.current)
                   .where.not(revenue_generated_cents: 0)
                   .sum(:revenue_generated_cents) / 100.0
  end

  def calculate_recommendation_trends(period)
    # Group recommendations by week to show trends
    recommendations_by_week = @current_account.agent_recommendations
                                             .where(created_at: period.ago..Time.current)
                                             .group_by_week(:created_at, time_zone: Time.zone)
                                             .count

    acceptance_by_week = @current_account.agent_recommendations
                                        .where(status: [ :accepted, :implemented ])
                                        .where(created_at: period.ago..Time.current)
                                        .group_by_week(:created_at, time_zone: Time.zone)
                                        .count

    {
      recommendations: recommendations_by_week,
      acceptances: acceptance_by_week
    }
  end

  def top_performing_recommendations
    @current_account.agent_recommendations
                   .implemented
                   .order(revenue_generated_cents: :desc)
                   .limit(5)
                   .pluck(:title, :revenue_generated_cents)
                   .map { |title, revenue| { title: title, revenue: revenue / 100.0 } }
  end

  def pipeline_performance_data
    @current_account.pipelines.active.map do |pipeline|
      recommendations = @current_account.agent_recommendations.where(pipeline: pipeline)
      {
        pipeline_id: pipeline.id,
        pipeline_name: pipeline.name,
        recommendations_count: recommendations.count,
        implemented_count: recommendations.implemented.count,
        estimated_value: recommendations.sum(:estimated_value) || 0
      }
    end
  end
end
