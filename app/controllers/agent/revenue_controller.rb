class Agent::RevenueController < Agent::BaseController
  def index
    @revenue_summary = calculate_comprehensive_revenue_summary
    @monthly_trend = calculate_monthly_trend
    @revenue_sources = calculate_revenue_by_source
    @top_recommendations = get_top_performing_recommendations
    @growth_metrics = calculate_growth_metrics

    respond_to do |format|
      format.html
      format.json {
        render json: {
          summary: @revenue_summary,
          monthly_trend: @monthly_trend,
          revenue_sources: @revenue_sources,
          top_recommendations: @top_recommendations,
          growth_metrics: @growth_metrics
        }
      }
    end
  end

  def dashboard
    @period = params[:period]&.to_i&.days || 30.days

    @dashboard_data = {
      overview: generate_revenue_overview(@period),
      charts: generate_chart_data(@period),
      insights: generate_revenue_insights(@period),
      forecasts: generate_revenue_forecasts(@period)
    }

    respond_to do |format|
      format.html
      format.json { render json: @dashboard_data }
    end
  end

  def analytics
    @analytics_data = {
      roi_analysis: calculate_roi_analysis,
      feature_performance: analyze_feature_performance,
      customer_segments: analyze_revenue_by_segments,
      optimization_opportunities: identify_optimization_opportunities,
      market_insights: generate_market_insights
    }

    respond_to do |format|
      format.html
      format.json { render json: @analytics_data }
    end
  end

  def export
    format = params[:format] || "csv"
    period = params[:period]&.to_i&.days || 90.days

    case format.downcase
    when "csv"
      csv_data = generate_revenue_csv(period)
      send_data csv_data,
                filename: "agent_revenue_#{@current_account.id}_#{Date.current}.csv",
                type: "text/csv"
    when "json"
      json_data = generate_detailed_revenue_data(period)
      render json: json_data
    else
      render json: { error: "Unsupported format" }, status: :bad_request
    end
  end

  def mrr_analysis
    @mrr_data = {
      current_mrr: calculate_current_mrr,
      mrr_trend: calculate_mrr_trend(12.months),
      mrr_by_source: calculate_mrr_by_source,
      churn_analysis: analyze_mrr_churn,
      expansion_revenue: calculate_expansion_revenue,
      forecasted_mrr: forecast_mrr(6.months)
    }

    respond_to do |format|
      format.html
      format.json { render json: @mrr_data }
    end
  end

  def subscription_analysis
    @subscription_data = {
      active_subscriptions: count_active_subscriptions,
      subscription_tiers: analyze_subscription_tiers,
      upgrade_patterns: analyze_upgrade_patterns,
      retention_rates: calculate_subscription_retention,
      satisfaction_scores: calculate_satisfaction_scores
    }

    respond_to do |format|
      format.html
      format.json { render json: @subscription_data }
    end
  end

  private

  def calculate_comprehensive_revenue_summary
    {
      total_revenue: @current_account.agent_revenues.sum(:amount_cents) / 100.0,
      monthly_revenue: @current_account.agent_revenue_this_month,
      weekly_revenue: calculate_weekly_revenue,
      daily_average: calculate_daily_average_revenue,
      growth_rate: calculate_revenue_growth_rate,
      active_revenue_streams: count_active_revenue_streams
    }
  end

  def calculate_monthly_trend
    12.months.ago.to_date.beginning_of_month.step(Date.current.end_of_month, 1.month).map do |date|
      month_start = date.beginning_of_month
      month_end = date.end_of_month

      revenue = @current_account.agent_revenues
                                .where(created_at: month_start..month_end)
                                .sum(:amount_cents) / 100.0

      {
        month: date.strftime("%Y-%m"),
        revenue: revenue,
        formatted_month: date.strftime("%b %Y")
      }
    end
  end

  def calculate_revenue_by_source
    @current_account.agent_revenues
                   .group(:revenue_source)
                   .sum(:amount_cents)
                   .transform_values { |cents| cents / 100.0 }
                   .map { |source, amount|
                     {
                       source: source.humanize,
                       amount: amount,
                       percentage: calculate_source_percentage(amount)
                     }
                   }
  end

  def calculate_source_percentage(amount)
    total = @current_account.agent_revenues.sum(:amount_cents) / 100.0
    return 0 if total.zero?
    ((amount / total) * 100).round(1)
  end

  def get_top_performing_recommendations
    @current_account.agent_recommendations
                   .implemented
                   .order(revenue_generated_cents: :desc)
                   .limit(10)
                   .map { |rec|
                     {
                       id: rec.id,
                       title: rec.title,
                       type: rec.recommendation_type.humanize,
                       revenue_generated: rec.revenue_generated_cents / 100.0,
                       confidence_score: rec.confidence_score,
                       implemented_at: rec.implemented_at
                     }
                   }
  end

  def calculate_growth_metrics
    current_month = @current_account.agent_revenue_this_month
    last_month = calculate_last_month_revenue

    growth_rate = calculate_month_over_month_growth(current_month, last_month)

    {
      month_over_month: growth_rate,
      quarter_over_quarter: calculate_quarter_growth,
      year_over_year: calculate_year_growth,
      trend_direction: growth_rate > 0 ? "up" : (growth_rate < 0 ? "down" : "flat")
    }
  end

  def calculate_weekly_revenue
    @current_account.agent_revenues
                   .where(created_at: 1.week.ago..)
                   .sum(:amount_cents) / 100.0
  end

  def calculate_daily_average_revenue
    total_days = (@current_account.agent_revenues.minimum(:created_at)&.to_date || Date.current)
                 .days_since(Date.current).abs + 1
    return 0 if total_days.zero?

    total_revenue = @current_account.agent_revenues.sum(:amount_cents) / 100.0
    (total_revenue / total_days).round(2)
  end

  def calculate_revenue_growth_rate
    current_month = @current_account.agent_revenue_this_month
    last_month = calculate_last_month_revenue

    calculate_month_over_month_growth(current_month, last_month)
  end

  def calculate_last_month_revenue
    @current_account.agent_revenues
                   .where(created_at: 2.months.ago..1.month.ago)
                   .sum(:amount_cents) / 100.0
  end

  def calculate_month_over_month_growth(current, previous)
    return 0 if previous.zero?
    (((current - previous) / previous) * 100).round(1)
  end

  def calculate_quarter_growth
    # Implementation for quarterly growth calculation
    current_quarter = calculate_current_quarter_revenue
    last_quarter = calculate_last_quarter_revenue

    calculate_month_over_month_growth(current_quarter, last_quarter)
  end

  def calculate_year_growth
    # Implementation for yearly growth calculation
    current_year = calculate_current_year_revenue
    last_year = calculate_last_year_revenue

    calculate_month_over_month_growth(current_year, last_year)
  end

  def count_active_revenue_streams
    @current_account.agent_revenues
                   .where(created_at: 30.days.ago..)
                   .distinct
                   .count(:revenue_source)
  end

  def generate_revenue_overview(period)
    {
      total_for_period: calculate_period_revenue(period),
      average_daily: calculate_period_average_daily(period),
      peak_day: find_peak_revenue_day(period),
      growth_trend: calculate_period_trend(period),
      milestone_progress: calculate_milestone_progress
    }
  end

  def generate_chart_data(period)
    {
      daily_revenue: generate_daily_revenue_chart(period),
      revenue_sources: generate_revenue_sources_chart,
      recommendation_performance: generate_recommendation_performance_chart,
      cumulative_revenue: generate_cumulative_revenue_chart(period)
    }
  end

  def generate_revenue_insights(period)
    [
      analyze_peak_performance(period),
      identify_growth_opportunities(period),
      assess_revenue_consistency(period),
      evaluate_feature_adoption(period)
    ].compact
  end

  def generate_revenue_forecasts(period)
    {
      next_month_forecast: forecast_next_month_revenue,
      quarterly_projection: forecast_quarterly_revenue,
      confidence_interval: calculate_forecast_confidence,
      key_assumptions: list_forecast_assumptions
    }
  end

  def calculate_roi_analysis
    total_investment = calculate_agent_development_cost
    total_return = @current_account.agent_revenues.sum(:amount_cents) / 100.0

    {
      total_investment: total_investment,
      total_return: total_return,
      net_roi: total_return - total_investment,
      roi_percentage: calculate_roi_percentage(total_return, total_investment),
      payback_period: calculate_payback_period,
      break_even_analysis: analyze_break_even_point
    }
  end

  def analyze_feature_performance
    AgentRevenue.revenue_sources.keys.map do |source|
      source_revenues = @current_account.agent_revenues.where(revenue_source: source)
      {
        feature: source.humanize,
        total_revenue: source_revenues.sum(:amount_cents) / 100.0,
        transaction_count: source_revenues.count,
        average_per_transaction: calculate_average_transaction(source_revenues),
        growth_trend: calculate_feature_growth_trend(source)
      }
    end
  end

  def analyze_revenue_by_segments
    # Segment analysis based on account characteristics
    {
      by_plan: segment_by_subscription_plan,
      by_company_size: segment_by_company_size,
      by_industry: segment_by_industry,
      by_usage_pattern: segment_by_usage_pattern
    }
  end

  def identify_optimization_opportunities
    [
      analyze_underperforming_features,
      identify_pricing_opportunities,
      assess_upsell_potential,
      evaluate_churn_risks
    ].compact
  end

  def generate_market_insights
    {
      benchmark_performance: compare_to_benchmarks,
      competitive_position: assess_competitive_position,
      market_trends: identify_market_trends,
      expansion_opportunities: identify_expansion_opportunities
    }
  end

  def generate_revenue_csv(period)
    require "csv"

    CSV.generate do |csv|
      csv << [ "Date", "Revenue Source", "Amount", "Description", "Recommendation ID" ]

      @current_account.agent_revenues
                     .where(created_at: period.ago..Time.current)
                     .order(:created_at)
                     .find_each do |revenue|
        csv << [
          revenue.created_at.to_date,
          revenue.revenue_source.humanize,
          revenue.amount,
          revenue.description,
          revenue.agent_recommendation_id
        ]
      end
    end
  end

  def generate_detailed_revenue_data(period)
    {
      summary: calculate_comprehensive_revenue_summary,
      transactions: @current_account.agent_revenues
                                   .where(created_at: period.ago..Time.current)
                                   .order(:created_at)
                                   .as_json(include: :agent_recommendation),
      analytics: {
        by_source: calculate_revenue_by_source,
        trends: calculate_monthly_trend,
        growth_metrics: calculate_growth_metrics
      }
    }
  end

  def calculate_current_mrr
    @current_account.agent_revenues
                   .joins("LEFT JOIN agent_recommendations ON agent_revenues.agent_recommendation_id = agent_recommendations.id")
                   .where(revenue_source: [ :monitoring_subscription, :compliance_subscription ])
                   .where(status: :active)
                   .sum { |revenue| revenue.calculate_mrr }
  end

  def calculate_mrr_trend(period)
    # Calculate MRR for each month in the period
    period.ago.to_date.beginning_of_month.step(Date.current.end_of_month, 1.month).map do |date|
      # This would need more sophisticated MRR calculation in production
      {
        month: date.strftime("%Y-%m"),
        mrr: calculate_mrr_for_month(date),
        net_new_mrr: calculate_net_new_mrr(date)
      }
    end
  end

  def calculate_mrr_by_source
    # Group MRR by revenue source
    AgentRevenue.revenue_sources.keys.map do |source|
      {
        source: source.humanize,
        mrr: calculate_source_mrr(source),
        growth_rate: calculate_source_mrr_growth(source)
      }
    end
  end

  # Helper methods with simplified implementations
  # In production, these would need more sophisticated business logic

  def calculate_period_revenue(period)
    @current_account.agent_revenues
                   .where(created_at: period.ago..Time.current)
                   .sum(:amount_cents) / 100.0
  end

  def calculate_agent_development_cost
    # Simplified calculation - would be based on actual development costs
    5000.0 # $5000 estimated development investment
  end

  def calculate_roi_percentage(return_amount, investment)
    return 0 if investment.zero?
    (((return_amount - investment) / investment) * 100).round(2)
  end

  def calculate_average_transaction(revenues)
    return 0 if revenues.empty?
    total = revenues.sum(:amount_cents) / 100.0
    (total / revenues.count).round(2)
  end

  # Placeholder methods that would need full implementation
  def calculate_current_quarter_revenue; 0; end
  def calculate_last_quarter_revenue; 0; end
  def calculate_current_year_revenue; 0; end
  def calculate_last_year_revenue; 0; end
  def calculate_milestone_progress; {}; end
  def generate_daily_revenue_chart(period); {}; end
  def generate_revenue_sources_chart; {}; end
  def generate_recommendation_performance_chart; {}; end
  def generate_cumulative_revenue_chart(period); {}; end
  def forecast_next_month_revenue; 0; end
  def forecast_quarterly_revenue; 0; end
  def calculate_forecast_confidence; 95; end
  def list_forecast_assumptions; []; end
  def calculate_mrr_for_month(date); 0; end
  def calculate_net_new_mrr(date); 0; end
  def calculate_source_mrr(source); 0; end
  def calculate_source_mrr_growth(source); 0; end
end
