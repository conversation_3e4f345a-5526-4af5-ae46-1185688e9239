class Agent::TemplatesController < Agent::BaseController
  before_action :set_template, only: [ :show, :purchase, :preview ]

  def index
    @templates = PipelineTemplate.published
                                .includes(:creator_account)
                                .page(params[:page])
                                .per(12)

    # Filter by category
    if params[:category].present?
      @templates = @templates.where(category: params[:category])
    end

    # Filter by industry
    if params[:industry].present?
      @templates = @templates.where(industry: params[:industry])
    end

    # Filter by price range
    if params[:price_range].present?
      case params[:price_range]
      when "free"
        @templates = @templates.where(price_cents: 0)
      when "under_10"
        @templates = @templates.where(price_cents: 1..999)
      when "under_25"
        @templates = @templates.where(price_cents: 1000..2499)
      when "over_25"
        @templates = @templates.where(price_cents: 2500..)
      end
    end

    # Search functionality
    if params[:search].present?
      search_term = "%#{params[:search]}%"
      @templates = @templates.where(
        "name ILIKE ? OR description ILIKE ? OR use_cases::text ILIKE ?",
        search_term, search_term, search_term
      )
    end

    # Sort options
    case params[:sort]
    when "popular"
      @templates = @templates.order(purchases_count: :desc)
    when "rating"
      @templates = @templates.order(average_rating: :desc)
    when "price_low"
      @templates = @templates.order(:price_cents)
    when "price_high"
      @templates = @templates.order(price_cents: :desc)
    when "newest"
      @templates = @templates.order(created_at: :desc)
    else
      @templates = @templates.order(:name)
    end

    @categories = PipelineTemplate.categories.keys
    @industries = PipelineTemplate.industries.keys
    @featured_templates = PipelineTemplate.featured.limit(3)

    respond_to do |format|
      format.html
      format.json {
        render json: {
          templates: @templates.as_json(include: :creator_account),
          categories: @categories,
          industries: @industries
        }
      }
    end
  end

  def show
    @similar_templates = find_similar_templates
    @creator = @template.creator_account
    @reviews = load_template_reviews
    @can_purchase = can_purchase_template?
    @personalized_recommendations = get_personalized_recommendations

    # Track template view for analytics
    track_template_view

    respond_to do |format|
      format.html
      format.json {
        render json: @template.as_json(
          include: { creator_account: { only: [ :name ] } },
          methods: [ :can_purchase_for_account ]
        )
      }
    end
  end

  def purchase
    unless can_purchase_template?
      render json: {
        error: "Cannot purchase this template",
        reasons: purchase_restriction_reasons
      }, status: :forbidden
      return
    end

    marketplace_service = TemplateMarketplaceService.new(account: @current_account)
    result = marketplace_service.process_template_purchase(@template, @current_account)

    if result[:success]
      track_template_purchase

      respond_to do |format|
        format.html {
          flash[:success] = result[:message]
          redirect_to pipelines_path
        }
        format.json {
          render json: {
            success: true,
            message: result[:message],
            pipeline: result[:pipeline].as_json,
            redirect_url: pipeline_path(result[:pipeline])
          }
        }
      end
    else
      respond_to do |format|
        format.html {
          flash[:error] = result[:error]
          redirect_to agent_template_path(@template)
        }
        format.json {
          render json: { success: false, error: result[:error] }, status: :unprocessable_entity
        }
      end
    end
  end

  def preview
    # Generate a preview of what the template would create
    preview_data = {
      pipeline_name: @template.name,
      source_config: @template.source_config_template,
      destination_config: @template.destination_config_template,
      transformation_rules: @template.transformation_template,
      schedule_template: @template.schedule_template,
      estimated_setup_time: calculate_setup_time,
      required_credentials: extract_required_credentials
    }

    render json: preview_data
  end

  def recommendations
    # Get personalized template recommendations for the current account
    marketplace_service = TemplateMarketplaceService.new(account: @current_account)
    recommendations = marketplace_service.get_personalized_recommendations(@current_account, {
      limit: params[:limit]&.to_i || 10,
      category: params[:category],
      industry: params[:industry]
    })

    render json: {
      recommendations: recommendations.map { |rec|
        {
          template: rec[:template].as_json(include: :creator_account),
          relevance_score: rec[:relevance_score],
          reasons: rec[:reasons]
        }
      }
    }
  end

  def marketplace_analytics
    # Admin/creator analytics for template marketplace
    unless current_user.admin? || has_published_templates?
      redirect_to agent_templates_path, alert: "Access denied."
      return
    end

    marketplace_service = TemplateMarketplaceService.new
    @analytics = marketplace_service.analyze_marketplace_performance

    respond_to do |format|
      format.html
      format.json { render json: @analytics }
    end
  end

  def my_templates
    # Show templates created by the current account
    @my_templates = PipelineTemplate.where(creator_account: @current_account)
                                   .order(created_at: :desc)
                                   .page(params[:page])
                                   .per(10)

    @revenue_summary = calculate_template_revenue_summary

    respond_to do |format|
      format.html
      format.json {
        render json: {
          templates: @my_templates,
          revenue_summary: @revenue_summary
        }
      }
    end
  end

  def create_from_pipeline
    # Create a template from an existing successful pipeline
    pipeline = @current_account.pipelines.find(params[:pipeline_id])

    unless pipeline.eligible_for_template?
      render json: {
        error: "Pipeline is not eligible for template creation",
        requirements: pipeline.template_eligibility_requirements
      }, status: :unprocessable_entity
      return
    end

    marketplace_service = TemplateMarketplaceService.new(
      account: @current_account,
      pipeline: pipeline
    )

    template_data = marketplace_service.create_template_from_pipeline(pipeline)

    if template_data
      render json: {
        success: true,
        template: template_data,
        message: "Template created successfully and submitted for review"
      }
    else
      render json: {
        success: false,
        error: "Failed to create template from pipeline"
      }, status: :unprocessable_entity
    end
  end

  private

  def set_template
    @template = PipelineTemplate.published.find(params[:id])
  end

  def find_similar_templates
    PipelineTemplate.published
                   .where.not(id: @template.id)
                   .where(
                     category: @template.category,
                     source_type: @template.source_type,
                     destination_type: @template.destination_type
                   )
                   .limit(3)
  end

  def load_template_reviews
    # In a full implementation, this would load actual reviews
    # For now, return mock data based on average_rating
    return [] unless @template.average_rating

    rating = @template.average_rating
    [
      {
        user_name: "Anonymous User",
        rating: [ rating.round, 5 ].min,
        comment: "Great template! #{rating > 4 ? 'Highly recommended.' : 'Works well for our needs.'}",
        created_at: 2.weeks.ago
      }
    ]
  end

  def can_purchase_template?
    return false if @template.creator_account_id == @current_account.id
    return false unless @template.published?
    return false if already_purchased?
    true
  end

  def already_purchased?
    @current_account.pipelines.exists?(source_pipeline_id: @template.id)
  end

  def purchase_restriction_reasons
    reasons = []
    reasons << "You cannot purchase your own template" if @template.creator_account_id == @current_account.id
    reasons << "Template is not available for purchase" unless @template.published?
    reasons << "You have already purchased this template" if already_purchased?
    reasons
  end

  def track_template_view
    UsageMetric.create!(
      account: @current_account,
      metric_type: "template_view",
      value: 1,
      recorded_at: Time.current,
      metadata: {
        template_id: @template.id,
        template_name: @template.name,
        template_category: @template.category,
        template_price: @template.price
      }
    )
  end

  def track_template_purchase
    UsageMetric.create!(
      account: @current_account,
      metric_type: "template_purchase",
      value: @template.price || 0,
      recorded_at: Time.current,
      metadata: {
        template_id: @template.id,
        template_name: @template.name,
        template_category: @template.category,
        purchase_type: @template.price_cents > 0 ? "paid" : "free"
      }
    )
  end

  def calculate_setup_time
    # Estimate setup time based on template complexity
    base_time = 10 # 10 minutes base

    # Add time for transformations
    if @template.transformation_template.present?
      base_time += @template.transformation_template.keys.count * 2
    end

    # Add time for complex configurations
    source_complexity = @template.source_config_template.keys.count
    dest_complexity = @template.destination_config_template.keys.count

    base_time += (source_complexity + dest_complexity) * 1

    "#{base_time} minutes"
  end

  def extract_required_credentials
    credentials = []

    # Extract from source config
    if @template.source_config_template.present?
      @template.source_config_template.each do |key, value|
        if value.is_a?(String) && value.start_with?("{{") && value.end_with?("}}")
          credentials << {
            field: key,
            placeholder: value,
            required: true,
            type: infer_credential_type(key)
          }
        end
      end
    end

    # Extract from destination config
    if @template.destination_config_template.present?
      @template.destination_config_template.each do |key, value|
        if value.is_a?(String) && value.start_with?("{{") && value.end_with?("}}")
          credentials << {
            field: key,
            placeholder: value,
            required: true,
            type: infer_credential_type(key)
          }
        end
      end
    end

    credentials.uniq { |c| c[:field] }
  end

  def infer_credential_type(field_name)
    case field_name.downcase
    when /password|secret|token|key/
      "password"
    when /email/
      "email"
    when /url|endpoint|host/
      "url"
    else
      "text"
    end
  end

  def has_published_templates?
    PipelineTemplate.where(creator_account: @current_account).published.exists?
  end

  def calculate_template_revenue_summary
    my_template_revenues = AgentRevenue
      .joins(
        "JOIN pipeline_templates ON agent_revenues.performance_metrics->>'template_id' = pipeline_templates.id::text"
      )
      .where("pipeline_templates.creator_account_id = ?", @current_account.id)
      .where(revenue_source: :template_sale)

    {
      total_revenue: my_template_revenues.sum(:amount_cents) / 100.0,
      this_month_revenue: my_template_revenues.where(created_at: 1.month.ago..).sum(:amount_cents) / 100.0,
      total_sales: my_template_revenues.count,
      average_per_sale: calculate_average_template_revenue(my_template_revenues)
    }
  end

  def calculate_average_template_revenue(revenues)
    return 0 if revenues.empty?

    total = revenues.sum(:amount_cents) / 100.0
    count = revenues.count
    (total / count).round(2)
  end

  def get_personalized_recommendations
    marketplace_service = TemplateMarketplaceService.new(account: @current_account)
    marketplace_service.get_personalized_recommendations(@current_account, limit: 3)
  rescue => e
    Rails.logger.error "Failed to get personalized recommendations: #{e.message}"
    []
  end
end
