# frozen_string_literal: true

Rails.configuration.stripe = {
  publishable_key: Rails.application.credentials.dig(:stripe, :publishable_key) || "pk_test_your_publishable_key_here",
  secret_key: Rails.application.credentials.dig(:stripe, :secret_key) || "sk_test_your_secret_key_here",
  webhook_secret: Rails.application.credentials.dig(:stripe, :webhook_secret) || "whsec_your_webhook_secret_here"
}

Stripe.api_key = Rails.configuration.stripe[:secret_key]

# Set API version to ensure consistent behavior
Stripe.api_version = "2023-10-16"

Rails.logger.info "Stripe initialized with API version #{Stripe.api_version}"
