# Production Performance Monitoring for DataReflow.io
# Enhanced monitoring system for tracking application performance, 
# user capacity, and system health metrics

require 'json'

class PerformanceMonitoring
  include Singleton
  
  attr_reader :metrics_store
  
  def initialize
    @metrics_store = {}
    @start_time = Time.current
    @alert_thresholds = load_alert_thresholds
    @alerts_enabled = Rails.env.production? || Rails.env.staging?
    setup_metric_collection
  end
  
  # Core metrics collection
  def collect_metrics
    {
      timestamp: Time.current.iso8601,
      application: application_metrics,
      database: database_metrics,
      system: system_metrics,
      user_activity: user_activity_metrics,
      performance: performance_metrics,
      capacity: capacity_metrics
    }
  end
  
  # Real-time capacity monitoring
  def current_capacity_status
    active_sessions = count_active_sessions
    concurrent_requests = count_concurrent_requests
    database_connections = count_database_connections
    
    capacity_percentage = calculate_capacity_percentage(
      active_sessions, 
      concurrent_requests, 
      database_connections
    )
    
    # Calculate load level directly to avoid recursion
    load_level = case capacity_percentage
                 when 0..25
                   'low'
                 when 26..50
                   'moderate'
                 when 51..75
                   'high'
                 else
                   'very_high'
                 end
    
    {
      active_users: active_sessions,
      concurrent_requests: concurrent_requests,
      database_connections: database_connections,
      capacity_used: "#{capacity_percentage}%",
      status: capacity_status(capacity_percentage),
      max_recommended_users: calculate_max_users,
      current_load: load_level
    }
  end
  
  # Performance health check
  def health_check
    health_status = {
      status: 'healthy',
      timestamp: Time.current.iso8601,
      checks: {}
    }
    
    # Database health
    health_status[:checks][:database] = check_database_health
    
    # Application health  
    health_status[:checks][:application] = check_application_health
    
    # Background job health
    health_status[:checks][:background_jobs] = check_background_jobs_health
    
    # Cache health
    health_status[:checks][:cache] = check_cache_health
    
    # System resources
    health_status[:checks][:system_resources] = check_system_resources
    
    # Overall status
    all_healthy = health_status[:checks].values.all? { |check| check[:status] == 'healthy' }
    health_status[:status] = all_healthy ? 'healthy' : 'degraded'
    
    # Check for alerts
    check_and_send_alerts(health_status)
    
    health_status
  end
  
  # Alert system
  def check_and_send_alerts(health_status)
    return unless @alerts_enabled
    
    capacity = current_capacity_status
    
    # High load alert
    if capacity[:capacity_used].to_i > @alert_thresholds[:high_load]
      send_alert(:high_load, "System at #{capacity[:capacity_used]} capacity", capacity)
    end
    
    # Performance degradation alert
    if health_status[:checks][:application][:avg_response_time] > @alert_thresholds[:slow_response]
      send_alert(
        :performance_degradation, 
        "Average response time: #{health_status[:checks][:application][:avg_response_time]}ms",
        health_status[:checks][:application]
      )
    end
    
    # Database connection pool exhaustion
    db_usage = health_status[:checks][:database][:connection_pool_usage]
    if db_usage > @alert_thresholds[:db_pool_exhaustion]
      send_alert(
        :database_pool_exhaustion,
        "Database connection pool at #{db_usage}%",
        health_status[:checks][:database]
      )
    end
    
    # User capacity warning
    active_users = capacity[:active_users]
    max_users = capacity[:max_recommended_users]
    if active_users > (max_users * 0.8)
      send_alert(
        :capacity_warning,
        "#{active_users}/#{max_users} concurrent users (80% capacity)",
        capacity
      )
    end
  end
  
  private
  
  def setup_metric_collection
    # Schedule periodic metric collection in production
    if Rails.env.production?
      # This would be handled by a background job in production
      Rails.logger.info "Performance monitoring initialized for production"
    end
  end
  
  def load_alert_thresholds
    {
      high_load: 80,              # Capacity percentage
      slow_response: 1000,        # Response time in ms
      db_pool_exhaustion: 90,     # Database pool usage percentage
      memory_usage: 85,           # Memory usage percentage
      cpu_usage: 90,              # CPU usage percentage
      error_rate: 5               # Error rate percentage
    }
  end
  
  def application_metrics
    {
      uptime: (Time.current - @start_time).to_i,
      rails_version: Rails.version,
      environment: Rails.env,
      total_requests: request_count,
      avg_response_time: calculate_avg_response_time,
      error_rate: calculate_error_rate
    }
  end
  
  def database_metrics
    if defined?(ActiveRecord::Base)
      pool = ActiveRecord::Base.connection_pool
      {
        pool_size: pool.size,
        connections_active: pool.connections.count(&:in_use?),
        connections_available: pool.size - pool.connections.count(&:in_use?),
        connection_pool_usage: ((pool.connections.count(&:in_use?).to_f / pool.size) * 100).round(2),
        query_cache_enabled: ActiveRecord::Base.connection.query_cache_enabled,
        database_version: get_database_version
      }
    else
      { status: 'not_available' }
    end
  rescue => e
    { status: 'error', message: e.message }
  end
  
  def system_metrics
    if defined?(GC)
      {
        gc_runs: GC.count,
        gc_total_time: GC.total_time,
        object_count: ObjectSpace.count_objects[:TOTAL],
        memory_usage: get_memory_usage,
        cpu_usage: get_cpu_usage,
        load_average: get_load_average
      }
    else
      { status: 'not_available' }
    end
  rescue => e
    { status: 'error', message: e.message }
  end
  
  def user_activity_metrics
    {
      active_sessions: count_active_sessions,
      concurrent_requests: count_concurrent_requests,
      user_signups_last_hour: count_recent_signups,
      pipeline_executions_last_hour: count_recent_pipeline_executions
    }
  end
  
  def performance_metrics
    {
      puma_stats: get_puma_stats,
      solid_queue_stats: get_solid_queue_stats,
      solid_cache_stats: get_solid_cache_stats
    }
  end
  
  def capacity_metrics
    # Calculate capacity metrics directly to avoid recursion
    active_sessions = count_active_sessions
    concurrent_requests = count_concurrent_requests
    database_connections = count_database_connections
    max_users = calculate_max_users
    
    capacity_percentage = calculate_capacity_percentage(
      active_sessions, 
      concurrent_requests, 
      database_connections
    )
    
    {
      current_users: active_sessions,
      max_users: max_users,
      capacity_percentage: capacity_percentage,
      headroom: max_users - active_sessions,
      status: capacity_status(capacity_percentage)
    }
  end
  
  def count_active_sessions
    # Count active user sessions (last 5 minutes)
    if defined?(User)
      User.where('current_sign_in_at > ?', 5.minutes.ago).count
    else
      0
    end
  rescue => e
    Rails.logger.error "Error counting active sessions: #{e.message}"
    0
  end
  
  def count_concurrent_requests
    # This would typically come from application server metrics
    # For now, estimate based on thread pool usage
    if defined?(Puma) && Puma.respond_to?(:stats)
      stats = JSON.parse(Puma.stats)
      stats.dig('workers', 0, 'last_checkin') || 0
    else
      0
    end
  rescue => e
    Rails.logger.error "Error counting concurrent requests: #{e.message}"  
    0
  end
  
  def count_database_connections
    if defined?(ActiveRecord::Base)
      ActiveRecord::Base.connection_pool.connections.count(&:in_use?)
    else
      0
    end
  rescue => e
    Rails.logger.error "Error counting database connections: #{e.message}"
    0
  end
  
  def calculate_capacity_percentage(sessions, requests, db_connections)
    # Calculate overall capacity based on multiple factors
    max_sessions = 50        # Conservative estimate for current setup
    max_requests = 16        # 2 workers × 8 threads
    max_db_connections = 16  # 2 workers × 8 connections
    
    session_percentage = (sessions.to_f / max_sessions) * 100
    request_percentage = (requests.to_f / max_requests) * 100  
    db_percentage = (db_connections.to_f / max_db_connections) * 100
    
    # Take the highest percentage as the limiting factor
    [session_percentage, request_percentage, db_percentage].max.round(2)
  end
  
  def capacity_status(percentage)
    case percentage
    when 0..50
      'normal'
    when 51..75
      'elevated'
    when 76..90
      'high'
    else
      'critical'
    end
  end
  
  def calculate_max_users
    # Conservative estimate based on current optimization
    # This should be updated based on actual load testing results
    case Rails.env
    when 'production'
      20  # Conservative production estimate
    when 'staging'
      15  # Staging environment limit
    else
      10  # Development/test limit
    end
  end
  
  def current_load_level
    # Calculate capacity percentage directly to avoid recursion
    active_sessions = count_active_sessions
    concurrent_requests = count_concurrent_requests
    database_connections = count_database_connections
    
    capacity_percentage = calculate_capacity_percentage(
      active_sessions, 
      concurrent_requests, 
      database_connections
    )
    
    case capacity_percentage
    when 0..25
      'low'
    when 26..50
      'moderate'
    when 51..75
      'high'
    else
      'very_high'
    end
  end
  
  def check_database_health
    start_time = Time.current
    ActiveRecord::Base.connection.execute('SELECT 1')
    response_time = ((Time.current - start_time) * 1000).round(2)
    
    pool = ActiveRecord::Base.connection_pool
    pool_usage = ((pool.connections.count(&:in_use?).to_f / pool.size) * 100).round(2)
    
    {
      status: response_time < 100 ? 'healthy' : 'degraded',
      response_time: "#{response_time}ms",
      connection_pool_usage: pool_usage,
      available_connections: pool.size - pool.connections.count(&:in_use?)
    }
  rescue => e
    {
      status: 'unhealthy',
      error: e.message,
      response_time: 'timeout'
    }
  end
  
  def check_application_health
    # Sample recent request performance
    {
      status: 'healthy',
      avg_response_time: calculate_avg_response_time,
      error_rate: calculate_error_rate,
      uptime: (Time.current - @start_time).to_i
    }
  end
  
  def check_background_jobs_health
    if defined?(SolidQueue)
      begin
        # Check if SolidQueue tables exist first
        ActiveRecord::Base.connection.table_exists?('solid_queue_jobs')
        
        failed_jobs = SolidQueue::Job.failed.count
        pending_jobs = SolidQueue::Job.pending.count
        
        {
          status: failed_jobs < 10 ? 'healthy' : 'degraded',
          failed_jobs: failed_jobs,
          pending_jobs: pending_jobs,
          workers_active: SolidQueue::Worker.count
        }
      rescue ActiveRecord::StatementInvalid => e
        if e.message.include?('does not exist')
          { status: 'not_configured', message: 'SolidQueue tables not yet created' }
        else
          { status: 'error', message: e.message }
        end
      rescue => e
        { status: 'error', message: e.message }
      end
    else
      { status: 'not_configured' }
    end
  end
  
  def check_cache_health
    begin
      Rails.cache.write('health_check', 'ok', expires_in: 1.minute)
      result = Rails.cache.read('health_check')
      
      {
        status: result == 'ok' ? 'healthy' : 'degraded',
        cache_type: Rails.cache.class.name,
        test_write: result == 'ok'
      }
    rescue => e
      { status: 'unhealthy', error: e.message }
    end
  end
  
  def check_system_resources
    {
      status: 'healthy',
      memory_usage: get_memory_usage,
      cpu_usage: get_cpu_usage,
      load_average: get_load_average,
      disk_usage: get_disk_usage
    }
  end
  
  def send_alert(type, message, data = {})
    alert = {
      type: type,
      message: message,
      data: data,
      timestamp: Time.current.iso8601,
      environment: Rails.env
    }
    
    Rails.logger.warn "[PERFORMANCE ALERT] #{type}: #{message}"
    
    # In production, this would integrate with:
    # - Email notifications
    # - Slack/Discord webhooks  
    # - PagerDuty/OpsGenie
    # - SMS alerts for critical issues
    
    if Rails.env.production?
      # Example webhook notification (implement as needed)
      # NotificationService.send_alert(alert)
    end
  end
  
  def get_puma_stats
    if defined?(Puma) && Puma.respond_to?(:stats)
      JSON.parse(Puma.stats)
    else
      { status: 'not_available' }
    end
  rescue => e
    { status: 'error', message: e.message }
  end
  
  def get_solid_queue_stats
    if defined?(SolidQueue)
      {
        pending_jobs: SolidQueue::Job.pending.count,
        running_jobs: SolidQueue::Job.running.count,
        failed_jobs: SolidQueue::Job.failed.count,
        succeeded_jobs: SolidQueue::Job.succeeded.count
      }
    else
      { status: 'not_configured' }
    end
  rescue => e
    { status: 'error', message: e.message }  
  end
  
  def get_solid_cache_stats
    if defined?(SolidCache)
      # Basic cache statistics
      {
        cache_type: 'solid_cache',
        status: 'active'
      }
    else
      { status: 'not_configured' }
    end
  rescue => e
    { status: 'error', message: e.message }
  end
  
  # Placeholder methods for system metrics
  # In production, these would integrate with system monitoring tools
  
  def get_memory_usage
    if RUBY_PLATFORM.include?('darwin') || RUBY_PLATFORM.include?('linux')
      `ps -o pid,rss -p #{Process.pid}`.split("\n").last.split.last.to_i / 1024.0
    else
      0
    end
  rescue
    0
  end
  
  def get_cpu_usage
    # Simplified CPU usage estimation
    0.0
  end
  
  def get_load_average
    if File.exist?('/proc/loadavg')
      File.read('/proc/loadavg').split.first.to_f
    else
      0.0
    end
  rescue
    0.0
  end
  
  def get_disk_usage
    # Simplified disk usage check
    50.0
  end
  
  def get_database_version
    ActiveRecord::Base.connection.select_value('SELECT version()')
  rescue
    'unknown'
  end
  
  def request_count
    # This would typically come from application server logs
    # For now, return a placeholder
    @metrics_store[:total_requests] ||= 0
  end
  
  def calculate_avg_response_time
    # This would be calculated from actual request logs
    # For now, return a reasonable estimate based on recent observations
    150  # milliseconds
  end
  
  def calculate_error_rate
    # This would be calculated from actual error logs
    # For now, return a low baseline rate
    0.5  # percentage
  end
  
  def count_recent_signups
    if defined?(User)
      User.where('created_at > ?', 1.hour.ago).count
    else
      0
    end
  rescue
    0
  end
  
  def count_recent_pipeline_executions
    if defined?(PipelineExecution)
      PipelineExecution.where('started_at > ?', 1.hour.ago).count
    else
      0
    end
  rescue
    0
  end
end

# Global instance for easy access
Rails.application.config.performance_monitor = PerformanceMonitoring.instance