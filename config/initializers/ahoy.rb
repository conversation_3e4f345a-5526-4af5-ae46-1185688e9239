class Ahoy::Store < Ahoy::DatabaseStore
  # Add account_id to visits and events for multi-tenancy
  def track_visit(data)
    if current_account
      data[:properties] ||= {}
      data[:properties][:account_id] = current_account.id
    end
    super(data)
  end

  def track_event(data)
    if current_account
      data[:properties] ||= {}
      data[:properties][:account_id] = current_account.id
    end
    super(data)
  end

  private

  def current_account
    controller&.current_account if controller.respond_to?(:current_account)
  end
end

# set to true for JavaScript tracking
Ahoy.api = false

# set to true for geocoding (and add the geocoder gem to your Gemfile)
# we recommend configuring local geocoding as well
# see https://github.com/ankane/ahoy#geocoding
Ahoy.geocode = true
