# frozen_string_literal: true

require "flipper"
require "flipper/adapters/active_record"

# Configure Flipper with ActiveRecord adapter
Flipper.configure do |config|
  config.adapter { Flipper::Adapters::ActiveRecord.new }
end

# Define groups for role-based feature flags
Flipper.register(:admin) do |actor|
  actor.respond_to?(:admin?) && actor.admin?
end

Flipper.register(:owner) do |actor|
  actor.respond_to?(:owner?) && actor.owner?
end

Flipper.register(:paid_accounts) do |actor|
  if actor.respond_to?(:account) && actor.account
    !actor.account.subscription&.free?
  end
end

# Setup Flipper UI if in development
if Rails.env.development?
  require "flipper/ui"

  # Mount Flipper UI at /admin/flipper
  Rails.application.routes.append do
    mount Flipper::UI.app(Flipper) => "/admin/flipper"
  end
end
