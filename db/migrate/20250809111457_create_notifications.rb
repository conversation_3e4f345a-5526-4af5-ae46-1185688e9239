class CreateNotifications < ActiveRecord::Migration[8.0]
  def change
    create_table :notifications do |t|
      t.references :account, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.string :title, null: false
      t.text :message, null: false
      t.string :notification_type, null: false
      t.string :priority, null: false, default: 'medium'
      t.datetime :read_at
      t.string :action_url
      t.references :notifiable, polymorphic: true, null: false

      t.timestamps
    end

    add_index :notifications, [ :account_id, :user_id ]
    add_index :notifications, [ :account_id, :read_at ]
    add_index :notifications, [ :account_id, :notification_type ]
    add_index :notifications, [ :account_id, :priority ]
    add_index :notifications, [ :account_id, :created_at ]
    add_index :notifications, [ :notifiable_type, :notifiable_id ]
  end
end
