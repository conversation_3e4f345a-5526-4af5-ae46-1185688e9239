class CreateAccountInvitations < ActiveRecord::Migration[8.0]
  def change
    create_table :account_invitations do |t|
      t.references :account, null: false, foreign_key: true
      t.references :invited_by, null: false, foreign_key: { to_table: :users }
      t.string :email
      t.string :token
      t.integer :role
      t.integer :status
      t.datetime :expires_at
      t.datetime :accepted_at

      t.timestamps
    end
    add_index :account_invitations, :token
  end
end
