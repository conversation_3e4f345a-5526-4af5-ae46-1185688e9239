class CreateProjects < ActiveRecord::Migration[8.0]
  def change
    create_table :projects do |t|
      t.references :account, null: false, foreign_key: true
      t.references :created_by, null: false, foreign_key: { to_table: :users }
      t.string :name, null: false, limit: 100
      t.text :description
      t.integer :status, default: 0, null: false
      t.jsonb :settings, default: {}

      t.timestamps
    end

    add_index :projects, [:account_id, :name], unique: true
    add_index :projects, [:account_id, :status]
    add_index :projects, [:account_id, :created_at]
  end
end
