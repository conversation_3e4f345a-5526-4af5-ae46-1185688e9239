class CreatePipelines < ActiveRecord::Migration[8.0]
  def change
    create_table :pipelines do |t|
      t.references :account, null: false, foreign_key: true
      t.bigint :created_by, null: false
      t.string :name, null: false
      t.text :description
      t.integer :status, default: 0, null: false

      # Configuration
      t.jsonb :source_config, null: false, default: {}
      t.jsonb :destination_config, null: false, default: {}
      t.jsonb :transformation_rules, default: {}

      # Scheduling
      t.integer :schedule_type, default: 0, null: false
      t.jsonb :schedule_config, default: {}

      # Performance tracking
      t.integer :execution_count, default: 0, null: false
      t.datetime :last_executed_at
      t.integer :last_execution_status
      t.decimal :avg_execution_time, precision: 10, scale: 2

      t.timestamps
    end

    # Add foreign key for created_by
    add_foreign_key :pipelines, :users, column: :created_by

    # Add indexes for performance
    add_index :pipelines, [ :account_id, :status ]
    add_index :pipelines, [ :account_id, :created_at ]
    add_index :pipelines, :last_executed_at
    add_index :pipelines, :created_by
    add_index :pipelines, :schedule_type
  end
end
