class CreateTeamInvitations < ActiveRecord::Migration[8.0]
  def change
    create_table :team_invitations do |t|
      t.string :email
      t.integer :role
      t.references :invited_by, null: false, foreign_key: { to_table: :users }
      t.references :account, null: false, foreign_key: true
      t.integer :status
      t.string :token
      t.datetime :expires_at

      t.timestamps
    end
  end
end
