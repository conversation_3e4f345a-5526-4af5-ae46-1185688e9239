class CreateAgentRevenues < ActiveRecord::Migration[8.0]
  def change
    create_table :agent_revenues do |t|
      t.references :account, null: false, foreign_key: true
      t.references :agent_recommendation, foreign_key: true
      t.references :pipeline, foreign_key: true

      # Revenue source: optimization_fee, template_sale, quality_monitoring, compliance_subscription, integration_commission
      t.integer :revenue_source, null: false
      t.integer :amount_cents, null: false
      t.string :currency, default: 'USD'

      # Tracking details
      t.string :description
      t.jsonb :performance_metrics, default: {}
      t.integer :billing_period # For subscriptions: monthly, annual
      t.datetime :period_start
      t.datetime :period_end

      # Status for recurring revenues
      t.integer :status, default: 0 # active, cancelled, expired

      t.timestamps
    end

    add_index :agent_revenues, [ :account_id, :created_at ]
    add_index :agent_revenues, [ :account_id, :revenue_source ]
    add_index :agent_revenues, :revenue_source
    add_index :agent_revenues, :status
    add_index :agent_revenues, [ :period_start, :period_end ]
  end
end
