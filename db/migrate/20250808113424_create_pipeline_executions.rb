class CreatePipelineExecutions < ActiveRecord::Migration[8.0]
  def change
    create_table :pipeline_executions do |t|
      t.references :pipeline, null: false, foreign_key: true
      t.integer :status, default: 0, null: false

      # Execution timing
      t.datetime :started_at, null: false
      t.datetime :completed_at
      t.decimal :execution_time, precision: 10, scale: 3

      # Results
      t.integer :records_processed, default: 0
      t.integer :records_success, default: 0
      t.integer :records_failed, default: 0

      # Error handling
      t.text :error_message
      t.text :execution_log
      t.jsonb :metadata, default: {}

      t.timestamps
    end

    # Add indexes for performance and analytics
    add_index :pipeline_executions, [ :pipeline_id, :status ]
    add_index :pipeline_executions, [ :pipeline_id, :started_at ]
    add_index :pipeline_executions, [ :pipeline_id, :created_at ]
    add_index :pipeline_executions, :status
    add_index :pipeline_executions, :started_at
  end
end
