class CreateSubscriptions < ActiveRecord::Migration[8.0]
  def change
    create_table :subscriptions do |t|
      t.references :account, null: false, foreign_key: true
      t.integer :plan
      t.integer :status
      t.string :stripe_subscription_id
      t.string :stripe_status
      t.datetime :current_period_start
      t.datetime :current_period_end
      t.boolean :cancel_at_period_end
      t.datetime :trial_ends_at

      t.timestamps
    end
  end
end
