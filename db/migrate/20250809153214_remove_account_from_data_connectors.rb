class RemoveAccountFromDataConnectors < ActiveRecord::Migration[8.0]
  def up
    # Remove the foreign key constraint first
    remove_foreign_key :data_connectors, :accounts if foreign_key_exists?(:data_connectors, :accounts)

    # Remove the account_id column
    remove_column :data_connectors, :account_id
  end

  def down
    # Add the account_id column back
    add_reference :data_connectors, :account, null: false, foreign_key: true

    # Populate account_id from project.account_id for existing records
    execute <<-SQL
      UPDATE data_connectors
      SET account_id = projects.account_id
      FROM projects
      WHERE data_connectors.project_id = projects.id
    SQL
  end
end
