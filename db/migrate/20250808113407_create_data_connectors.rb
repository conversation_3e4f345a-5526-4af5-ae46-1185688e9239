class CreateDataConnectors < ActiveRecord::Migration[8.0]
  def change
    create_table :data_connectors do |t|
      t.references :account, null: false, foreign_key: true
      t.bigint :created_by, null: false
      t.string :name, null: false
      t.string :connector_type, null: false
      t.jsonb :connection_config, null: false, default: {}
      t.integer :status, default: 0, null: false

      # Connection health tracking
      t.datetime :last_tested_at
      t.text :test_result
      t.integer :test_status, default: 0

      t.timestamps
    end

    # Add foreign key for created_by
    add_foreign_key :data_connectors, :users, column: :created_by

    # Add indexes for performance
    add_index :data_connectors, [ :account_id, :connector_type ]
    add_index :data_connectors, [ :account_id, :status ]
    add_index :data_connectors, :created_by
    add_index :data_connectors, :connector_type
  end
end
