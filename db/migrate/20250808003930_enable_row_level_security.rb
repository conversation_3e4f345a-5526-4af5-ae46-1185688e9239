class EnableRowLevelSecurity < ActiveRecord::Migration[8.0]
  def up
    # Enable RLS on tables that need tenant isolation
    execute "ALTER TABLE users ENABLE ROW LEVEL SECURITY;"
    execute "ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;"
    execute "ALTER TABLE account_invitations ENABLE ROW LEVEL SECURITY;"
    execute "ALTER TABLE api_tokens ENABLE ROW LEVEL SECURITY;"

    # Create RLS policies for users table
    execute <<-SQL
      CREATE POLICY users_tenant_isolation ON users
        USING (account_id = current_setting('rls.account_id', true)::bigint);
    SQL

    # Create RLS policies for subscriptions table
    execute <<-SQL
      CREATE POLICY subscriptions_tenant_isolation ON subscriptions
        USING (account_id = current_setting('rls.account_id', true)::bigint);
    SQL

    # Create RLS policies for account_invitations table
    execute <<-SQL
      CREATE POLICY account_invitations_tenant_isolation ON account_invitations
        USING (account_id = current_setting('rls.account_id', true)::bigint);
    SQL

    # Create RLS policies for api_tokens table
    execute <<-SQL
      CREATE POLICY api_tokens_tenant_isolation ON api_tokens
        USING (account_id = current_setting('rls.account_id', true)::bigint);
    SQL

    # Create a superuser bypass policy for system operations
    execute <<-SQL
      CREATE POLICY bypass_rls ON users
        USING (current_setting('rls.bypass', true)::boolean = true);
    SQL

    execute <<-SQL
      CREATE POLICY bypass_rls ON subscriptions
        USING (current_setting('rls.bypass', true)::boolean = true);
    SQL

    execute <<-SQL
      CREATE POLICY bypass_rls ON account_invitations
        USING (current_setting('rls.bypass', true)::boolean = true);
    SQL

    execute <<-SQL
      CREATE POLICY bypass_rls ON api_tokens
        USING (current_setting('rls.bypass', true)::boolean = true);
    SQL
  end

  def down
    # Drop policies first
    execute "DROP POLICY IF EXISTS users_tenant_isolation ON users;"
    execute "DROP POLICY IF EXISTS subscriptions_tenant_isolation ON subscriptions;"
    execute "DROP POLICY IF EXISTS account_invitations_tenant_isolation ON account_invitations;"
    execute "DROP POLICY IF EXISTS api_tokens_tenant_isolation ON api_tokens;"

    execute "DROP POLICY IF EXISTS bypass_rls ON users;"
    execute "DROP POLICY IF EXISTS bypass_rls ON subscriptions;"
    execute "DROP POLICY IF EXISTS bypass_rls ON account_invitations;"
    execute "DROP POLICY IF EXISTS bypass_rls ON api_tokens;"

    # Disable RLS
    execute "ALTER TABLE users DISABLE ROW LEVEL SECURITY;"
    execute "ALTER TABLE subscriptions DISABLE ROW LEVEL SECURITY;"
    execute "ALTER TABLE account_invitations DISABLE ROW LEVEL SECURITY;"
    execute "ALTER TABLE api_tokens DISABLE ROW LEVEL SECURITY;"
  end
end
