class CreatePipelineTemplates < ActiveRecord::Migration[8.0]
  def change
    create_table :pipeline_templates do |t|
      # Creator info (null if system-generated)
      t.references :creator_account, foreign_key: { to_table: :accounts }
      t.references :source_pipeline, foreign_key: { to_table: :pipelines }

      # Template metadata
      t.string :name, null: false
      t.text :description
      t.string :category # e-commerce, marketing, finance, operations, analytics
      t.string :industry # retail, saas, healthcare, manufacturing, etc.
      t.string :source_type, null: false # postgresql, mysql, api, csv, etc.
      t.string :destination_type, null: false

      # Template configuration (anonymized)
      t.jsonb :source_config_template, default: {}
      t.jsonb :destination_config_template, default: {}
      t.jsonb :transformation_template, default: {}
      t.jsonb :schedule_template, default: {}

      # Marketplace info
      t.integer :price_cents, default: 0 # 0 = free
      t.string :currency, default: 'USD'
      t.integer :purchases_count, default: 0
      t.decimal :average_rating, precision: 3, scale: 2
      t.integer :ratings_count, default: 0

      # Performance metrics
      t.jsonb :performance_metrics, default: {} # avg execution time, success rate, etc.
      t.jsonb :use_cases, default: [] # Array of use case descriptions
      t.jsonb :requirements, default: [] # Prerequisites for using template

      # Status
      t.integer :status, default: 0 # draft, published, archived
      t.boolean :featured, default: false
      t.datetime :published_at

      t.timestamps
    end

    add_index :pipeline_templates, :name
    add_index :pipeline_templates, :category
    add_index :pipeline_templates, :industry
    add_index :pipeline_templates, [ :source_type, :destination_type ]
    add_index :pipeline_templates, :status
    add_index :pipeline_templates, :featured
    add_index :pipeline_templates, :purchases_count
    add_index :pipeline_templates, :average_rating
  end
end
