class MigrateDataConnectorsToProjects < ActiveRecord::Migration[8.0]
  def up
    # Create default projects for each account that has data connectors
    accounts_with_connectors = execute(
      "SELECT DISTINCT account_id FROM data_connectors WHERE project_id IS NULL"
    ).to_a

    accounts_with_connectors.each do |row|
      account_id = row['account_id']

      # Get the account owner to be the project creator
      owner_id = execute(
        "SELECT id FROM users WHERE account_id = #{account_id} AND role = 0 LIMIT 1"
      ).first&.dig('id')

      # If no owner found, use the first user in the account
      owner_id ||= execute(
        "SELECT id FROM users WHERE account_id = #{account_id} ORDER BY created_at ASC LIMIT 1"
      ).first&.dig('id')

      next unless owner_id

      # Create default project
      project_id = execute(
        "INSERT INTO projects (account_id, created_by, name, description, status, settings, created_at, updated_at)
         VALUES (#{account_id}, #{owner_id}, 'Default Project', 'Default project for organizing your data connectors', 0, '{}', NOW(), NOW())
         RETURNING id"
      ).first['id']

      # Move all data connectors for this account to the default project
      execute(
        "UPDATE data_connectors SET project_id = #{project_id} WHERE account_id = #{account_id} AND project_id IS NULL"
      )
    end

    # Make project_id non-nullable after migration
    change_column_null :data_connectors, :project_id, false
  end

  def down
    # Make project_id nullable again
    change_column_null :data_connectors, :project_id, true

    # Set project_id to null for all data connectors
    execute("UPDATE data_connectors SET project_id = NULL")

    # Delete all projects (this will cascade to data connectors if foreign key is set)
    execute("DELETE FROM projects")
  end
end
