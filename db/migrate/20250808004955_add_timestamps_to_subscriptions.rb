class AddTimestampsToSubscriptions < ActiveRecord::Migration[8.0]
  def change
    # Only add columns that don't exist yet
    add_column :subscriptions, :trial_start, :datetime unless column_exists?(:subscriptions, :trial_start)
    add_column :subscriptions, :trial_end, :datetime unless column_exists?(:subscriptions, :trial_end)
    add_column :subscriptions, :canceled_at, :datetime unless column_exists?(:subscriptions, :canceled_at)
  end
end
