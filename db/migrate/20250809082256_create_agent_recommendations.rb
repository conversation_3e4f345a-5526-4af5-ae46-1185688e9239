class CreateAgentRecommendations < ActiveRecord::Migration[8.0]
  def change
    create_table :agent_recommendations do |t|
      t.references :account, null: false, foreign_key: true
      t.references :pipeline, foreign_key: true

      # Recommendation type: optimization, quality_fix, compliance, integration, template
      t.integer :recommendation_type, null: false
      t.string :title, null: false
      t.text :description
      t.integer :status, default: 0 # pending, accepted, rejected, implemented, expired

      # Value proposition
      t.decimal :estimated_value, precision: 10, scale: 2 # Estimated monthly savings/revenue
      t.decimal :confidence_score, precision: 5, scale: 2 # 0-100 confidence percentage
      t.integer :priority, default: 0 # 0=low, 1=medium, 2=high, 3=critical

      # Implementation details
      t.jsonb :implementation_steps, default: {}
      t.jsonb :ai_analysis, default: {}
      t.jsonb :before_metrics, default: {} # Metrics before implementation
      t.jsonb :after_metrics, default: {} # Metrics after implementation

      # Revenue tracking
      t.integer :revenue_generated_cents, default: 0
      t.datetime :implemented_at
      t.datetime :expires_at # Recommendation expiry

      t.timestamps
    end

    add_index :agent_recommendations, [ :account_id, :status ]
    add_index :agent_recommendations, [ :account_id, :created_at ]
    add_index :agent_recommendations, :recommendation_type
    add_index :agent_recommendations, :confidence_score
    add_index :agent_recommendations, :priority
    add_index :agent_recommendations, :expires_at
  end
end
