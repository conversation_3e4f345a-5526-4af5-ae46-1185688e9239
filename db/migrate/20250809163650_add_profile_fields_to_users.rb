class AddProfileFieldsToUsers < ActiveRecord::Migration[8.0]
  def change
    # Contact information
    add_column :users, :phone, :string
    add_column :users, :bio, :text

    # Interface preferences
    add_column :users, :theme, :string, default: 'light'
    add_column :users, :language, :string, default: 'en'
    add_column :users, :timezone, :string, default: 'UTC'
    add_column :users, :date_format, :string, default: 'US'

    # Email notification preferences
    add_column :users, :email_pipeline_notifications, :boolean, default: true
    add_column :users, :email_team_notifications, :boolean, default: true
    add_column :users, :email_account_notifications, :boolean, default: true

    # In-app notification preferences
    add_column :users, :desktop_notifications, :boolean, default: true
    add_column :users, :sound_notifications, :boolean, default: false
    add_column :users, :notification_frequency, :string, default: 'immediate'

    # Dashboard preferences
    add_column :users, :auto_refresh_dashboard, :boolean, default: true
    add_column :users, :show_advanced_metrics, :boolean, default: false
    add_column :users, :compact_view, :boolean, default: false

    # Privacy preferences
    add_column :users, :allow_analytics, :boolean, default: true
    add_column :users, :product_updates, :boolean, default: true
  end
end
