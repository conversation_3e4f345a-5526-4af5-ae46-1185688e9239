# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

puts "🌱 Seeding database..."

# Create subscription plans
subscription_plans = [
  {
    name: "Starter",
    stripe_product_id: "prod_starter", # Replace with actual Stripe product ID
    stripe_price_id: "price_starter_monthly", # Replace with actual Stripe price ID
    price_cents: 9900, # $99.00
    billing_cycle: "month",
    features: [
      "10 pipelines",
      "10,000 executions/month",
      "All connectors",
      "Email support",
      "Basic transformations"
    ].to_json,
    active: true
  },
  {
    name: "Professional",
    stripe_product_id: "prod_professional", # Replace with actual Stripe product ID
    stripe_price_id: "price_professional_monthly", # Replace with actual Stripe price ID
    price_cents: 29900, # $299.00
    billing_cycle: "month",
    features: [
      "50 pipelines",
      "100,000 executions/month",
      "All connectors",
      "Priority support",
      "Advanced transformations",
      "API access",
      "Custom webhooks"
    ].to_json,
    active: true
  },
  {
    name: "Enterprise",
    stripe_product_id: "prod_enterprise", # Replace with actual Stripe product ID
    stripe_price_id: "price_enterprise_monthly", # Replace with actual Stripe price ID
    price_cents: 59900, # $599.00
    billing_cycle: "month",
    features: [
      "Unlimited pipelines",
      "Unlimited executions",
      "All connectors",
      "Dedicated support",
      "Custom integrations",
      "SLA guarantee",
      "On-premise option",
      "White-label solution"
    ].to_json,
    active: true
  }
]

subscription_plans.each do |plan_attrs|
  plan = SubscriptionPlan.find_or_initialize_by(name: plan_attrs[:name])
  plan.assign_attributes(plan_attrs)
  if plan.save
    puts "✅ Created/updated subscription plan: #{plan.name}"
  else
    puts "❌ Failed to create subscription plan: #{plan.name} - #{plan.errors.full_messages.join(', ')}"
  end
end

puts "🌱 Seeding completed!"
