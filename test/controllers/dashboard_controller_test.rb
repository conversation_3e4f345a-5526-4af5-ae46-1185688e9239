require "test_helper"

class DashboardControllerTest < ActionDispatch::IntegrationTest
  include Devise::Test::IntegrationHelpers

  setup do
    @account = accounts(:one)
    @user = users(:one)
    @user.update!(account: @account)
    sign_in @user

    # Set subdomain for multi-tenant routing
    host! "#{@account.subdomain}.example.com"
  end

  test "should get index" do
    get subdomain_root_url
    assert_response :success
  end

  test "should render dashboard layout" do
    get subdomain_root_url
    assert_response :success
    assert_select "title", /Dashboard - DataReflow/
  end

  test "should display user welcome message" do
    get subdomain_root_url
    assert_response :success
    assert_select "h1", /Welcome back, #{@user.full_name}/
  end

  test "should display account information" do
    get subdomain_root_url
    assert_response :success
    assert_select "[data-metric='pipelines']"
  end

  test "should show key metrics section" do
    get subdomain_root_url
    assert_response :success
    assert_select "section[aria-labelledby='key-metrics-heading']"
    assert_select "h2#key-metrics-heading", "Key Metrics"
  end

  test "should show quick actions section" do
    get subdomain_root_url
    assert_response :success
    assert_select "section[aria-labelledby='quick-actions-heading']"
    assert_select "h2#quick-actions-heading", "Quick Actions"
  end

  test "should display plan usage for account managers" do
    @user.update!(role: :owner)
    get subdomain_root_url
    assert_response :success
    assert_select "section[aria-labelledby='plan-usage-heading']"
  end

  test "should not display plan usage for regular members" do
    @user.update!(role: :member)
    get subdomain_root_url
    assert_response :success
    assert_select "section[aria-labelledby='plan-usage-heading']", count: 0
  end

  test "should include accessibility features" do
    get subdomain_root_url
    assert_response :success

    # Check for skip link
    assert_select "a[href='#main-content']", "Skip to main content"

    # Check for main content area
    assert_select "main#main-content[role='main']"

    # Check for navigation landmarks
    assert_select "nav[role='navigation'][aria-label='Main navigation']"
  end

  test "should include responsive design classes" do
    get subdomain_root_url
    assert_response :success

    # Check for responsive grid classes
    assert_select ".grid.grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-4"
    assert_select ".grid.grid-cols-2.sm\\:grid-cols-3.lg\\:grid-cols-5"
  end

  test "should load dashboard controller data" do
    get subdomain_root_url
    assert_response :success

    # Verify controller instance variables are set
    assert assigns(:account)
    assert assigns(:user)
    assert assigns(:pipeline_metrics)
    assert assigns(:connector_metrics)
    assert assigns(:usage_metrics)
    assert assigns(:system_health)
  end

  test "should handle missing models gracefully" do
    # This tests the fallback methods in the controller
    get subdomain_root_url
    assert_response :success

    # Should not raise errors even if Pipeline/DataConnector models don't exist
    assert_not_nil assigns(:pipeline_metrics)
    assert_not_nil assigns(:connector_metrics)
  end
end
