[2025-08-09 18:00:19] Continuous monitoring initialized for --once
[2025-08-09 18:00:19] Starting continuous monitoring with 30s intervals
[2025-08-09 18:00:19] Error in monitoring loop: undefined method 'current' for class Time
[2025-08-09 18:00:49] Error in monitoring loop: undefined method 'current' for class Time
[2025-08-09 18:01:19] Error in monitoring loop: undefined method 'current' for class Time
[2025-08-09 18:01:49] Error in monitoring loop: undefined method 'current' for class Time
[2025-08-09 18:02:19] Error in monitoring loop: undefined method 'current' for class Time
[2025-08-09 18:22:32] Continuous monitoring initialized for --once
[2025-08-09 18:22:32] Starting continuous monitoring with 30s intervals
[2025-08-09 18:22:32] Error in monitoring loop: undefined method 'current' for class Time
[2025-08-09 18:23:02] Error in monitoring loop: undefined method 'current' for class Time
[2025-08-09 18:23:32] Error in monitoring loop: undefined method 'current' for class Time
[2025-08-09 18:24:02] Error in monitoring loop: undefined method 'current' for class Time
[2025-08-09 18:24:32] Error in monitoring loop: undefined method 'current' for class Time
[2025-08-09 18:25:57] Continuous monitoring initialized for http://localhost:3000
[2025-08-09 18:25:57] Running single monitoring check
[2025-08-09 18:27:25] Continuous monitoring initialized for http://localhost:3000
[2025-08-09 18:27:25] Running single monitoring check
[2025-08-09 18:27:26] HTTP 404 for /admin/monitoring/capacity
[2025-08-09 18:27:26] Failed to collect metrics from http://localhost:3000
