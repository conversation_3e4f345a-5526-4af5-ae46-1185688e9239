#!/usr/bin/env ruby

require 'net/http'
require 'json'
require 'uri'

def test_endpoint(url, endpoint)
  uri = URI("#{url}#{endpoint}")
  
  begin
    response = Net::HTTP.get_response(uri)
    
    case response.code.to_i
    when 200
      data = JSON.parse(response.body)
      puts "✅ #{endpoint}: OK (#{response.code})"
      return { status: :success, data: data }
    when 404
      puts "❌ #{endpoint}: Not Found (404)"
      return { status: :error, message: "Endpoint not found" }
    when 500
      puts "⚠️  #{endpoint}: Server Error (500)"
      return { status: :error, message: "Server error" }
    else
      puts "⚠️  #{endpoint}: Unexpected status (#{response.code})"
      return { status: :warning, code: response.code }
    end
  rescue => e
    puts "❌ #{endpoint}: Connection failed - #{e.message}"
    return { status: :error, message: e.message }
  end
end

def main
  base_url = ARGV[0] || 'http://localhost:3000'
  
  puts "Testing DataReflow.io Monitoring Endpoints"
  puts "Base URL: #{base_url}"
  puts "=" * 50
  
  # Test core monitoring endpoints
  health = test_endpoint(base_url, '/health')
  metrics = test_endpoint(base_url, '/metrics')
  
  puts "=" * 50
  
  if health[:status] == :success && metrics[:status] == :success
    puts "✅ All core monitoring endpoints are working!"
    
    # Display key metrics
    health_data = health[:data]
    metrics_data = metrics[:data]
    
    puts "\n📊 Current Status:"
    puts "  Application: #{health_data.dig('checks', 'application', 'status')}"
    puts "  Database: #{health_data.dig('checks', 'database', 'status')}"
    puts "  Memory Usage: #{metrics_data.dig('system', 'memory_usage')}MB"
    puts "  Active Users: #{metrics_data.dig('system', 'active_users')}"
    puts "  Uptime: #{metrics_data.dig('application', 'uptime')}s"
    
    exit 0
  else
    puts "❌ Some monitoring endpoints failed!"
    exit 1
  end
end

main if __FILE__ == $0