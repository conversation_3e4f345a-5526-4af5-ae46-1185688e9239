import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
const loginFailureRate = new Rate('login_failures');
const pipelineViewRate = new Rate('pipeline_view_success');
const pipelineCreateRate = new Rate('pipeline_create_success');

// Test configuration matching our production test data
export const options = {
  stages: [
    // Ramp-up
    { duration: '2m', target: 5 },   // Ramp up to 5 users over 2 minutes
    { duration: '5m', target: 5 },   // Stay at 5 users for 5 minutes
    { duration: '2m', target: 10 },  // Ramp up to 10 users over 2 minutes
    { duration: '5m', target: 10 },  // Stay at 10 users for 5 minutes
    { duration: '2m', target: 20 },  // Ramp up to 20 users over 2 minutes
    { duration: '10m', target: 20 }, // Stay at 20 users for 10 minutes
    { duration: '2m', target: 30 },  // Ramp up to 30 users over 2 minutes
    { duration: '5m', target: 30 },  // Stay at 30 users for 5 minutes
    { duration: '2m', target: 50 },  // Ramp up to 50 users over 2 minutes
    { duration: '1m', target: 50 },  // Stay at 50 users for 1 minute
    
    // Ramp-down
    { duration: '30s', target: 0 },   // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests must complete below 2s
    http_req_failed: ['rate<0.05'],    // Error rate must be below 5%
    login_failures: ['rate<0.10'],     // Login failure rate below 10%
    pipeline_view_success: ['rate>0.90'], // Pipeline view success rate above 90%
  }
};

// Application configuration
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
const TOTAL_TEST_USERS = 25; // We created 25 test users

export function setup() {
  console.log('Starting load test setup...');
  
  // Health check - use root path instead of /health_check
  const healthResponse = http.get(`${BASE_URL}/`);
  if (healthResponse.status !== 200) {
    console.log('Application health check failed');
    throw new Error('Application is not healthy');
  }
  
  console.log('Application health check passed');
  console.log(`Base URL: ${BASE_URL}`);
  console.log('Load test ready to start...');
  
  return { baseUrl: BASE_URL };
}

export default function (data) {
  // Select a user from our test data (loadtest0@example.<NAME_EMAIL>)
  const userId = Math.floor(Math.random() * TOTAL_TEST_USERS);
  const email = `loadtest${userId}@example.com`;
  const password = 'password123';
  
  console.log(`VU ${__VU}: Starting user journey for ${email}`);
  
  // Step 1: Login
  const loginSuccess = login(data.baseUrl, email, password);
  if (!loginSuccess) {
    return; // Skip the rest if login fails
  }
  
  // Step 2: View dashboard/pipelines
  viewDashboard(data.baseUrl);
  
  // Step 3: View pipelines list
  viewPipelines(data.baseUrl);
  
  // Step 4: Create a new pipeline (occasionally)
  if (Math.random() < 0.3) { // 30% chance to create pipeline
    createPipeline(data.baseUrl);
  }
  
  // Step 5: View account settings (occasionally)
  if (Math.random() < 0.2) { // 20% chance to view settings
    viewAccountSettings(data.baseUrl);
  }
  
  // Step 6: Logout
  logout(data.baseUrl);
  
  // Think time between iterations
  sleep(Math.random() * 3 + 2); // 2-5 seconds
}

function login(baseUrl, email, password) {
  // Get the login page to extract CSRF token
  const loginPageResponse = http.get(`${baseUrl}/users/sign_in`);
  
  let csrfToken = '';
  const csrfMatch = loginPageResponse.body.match(/name="authenticity_token"[^>]+value="([^"]+)"/);
  if (csrfMatch) {
    csrfToken = csrfMatch[1];
  }
  
  const loginData = {
    'authenticity_token': csrfToken,
    'user[email]': email,
    'user[password]': password,
    'commit': 'Log in'
  };
  
  const params = {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  };
  
  const loginResponse = http.post(`${baseUrl}/users/sign_in`, loginData, params);
  
  const loginSuccessful = check(loginResponse, {
    'login successful': (r) => r.status === 302 && (r.headers['Location'] || '').includes('/dashboard'),
    'login status ok': (r) => [200, 302].includes(r.status),
  });
  
  if (!loginSuccessful) {
    console.log(`VU ${__VU}: Login failed for ${email}, status: ${loginResponse.status}`);
    loginFailureRate.add(1);
    return false;
  }
  
  loginFailureRate.add(0);
  sleep(1);
  return true;
}

function viewDashboard(baseUrl) {
  const dashboardResponse = http.get(`${baseUrl}/dashboard`);
  
  check(dashboardResponse, {
    'dashboard loads': (r) => r.status === 200,
    'dashboard has content': (r) => r.body.includes('Dashboard') || r.body.includes('pipeline'),
  });
  
  sleep(Math.random() * 2 + 1); // 1-3 seconds
}

function viewPipelines(baseUrl) {
  const pipelinesResponse = http.get(`${baseUrl}/pipelines`);
  
  const pipelineViewSuccess = check(pipelinesResponse, {
    'pipelines page loads': (r) => r.status === 200,
    'pipelines content present': (r) => r.body.includes('Pipelines') || r.body.includes('New Pipeline'),
  });
  
  pipelineViewRate.add(pipelineViewSuccess ? 0 : 1);
  sleep(Math.random() * 2 + 1); // 1-3 seconds
}

function createPipeline(baseUrl) {
  // Get new pipeline form
  const newPipelineResponse = http.get(`${baseUrl}/pipelines/new`);
  
  if (newPipelineResponse.status !== 200) {
    return;
  }
  
  // Extract CSRF token
  let csrfToken = '';
  const csrfMatch = newPipelineResponse.body.match(/name="authenticity_token"[^>]+value="([^"]+)"/);
  if (csrfMatch) {
    csrfToken = csrfMatch[1];
  }
  
  // Create pipeline data
  const pipelineData = {
    'authenticity_token': csrfToken,
    'pipeline[name]': `Load Test Pipeline ${Date.now()}`,
    'pipeline[description]': 'Created during load testing',
    'pipeline[template_type]': 'etl',
    'pipeline[status]': 'draft',
    'pipeline[schedule_type]': 'manual',
    'pipeline[source_config][type]': 'database',
    'pipeline[source_config][host]': 'localhost',
    'pipeline[destination_config][type]': 'database',
    'pipeline[destination_config][host]': 'localhost',
    'commit': 'Create Pipeline'
  };
  
  const params = {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  };
  
  const createResponse = http.post(`${baseUrl}/pipelines`, pipelineData, params);
  
  const pipelineCreateSuccess = check(createResponse, {
    'pipeline creation response': (r) => [200, 201, 302].includes(r.status),
    'pipeline created successfully': (r) => {
      if (r.status === 302) {
        return (r.headers['Location'] || '').includes('/pipelines/');
      }
      return r.status === 201 || (r.status === 200 && r.body.includes('successfully'));
    },
  });
  
  pipelineCreateRate.add(pipelineCreateSuccess ? 0 : 1);
  sleep(1);
}

function viewAccountSettings(baseUrl) {
  const settingsResponse = http.get(`${baseUrl}/account/settings`);
  
  check(settingsResponse, {
    'settings page loads': (r) => [200, 302].includes(r.status),
  });
  
  sleep(Math.random() * 2 + 1); // 1-3 seconds
}

function logout(baseUrl) {
  // Many Rails apps use DELETE for logout
  const logoutResponse = http.del(`${baseUrl}/users/sign_out`);
  
  check(logoutResponse, {
    'logout successful': (r) => [200, 302, 204].includes(r.status),
  });
  
  sleep(0.5);
}

export function teardown(data) {
  console.log('Load test completed');
}