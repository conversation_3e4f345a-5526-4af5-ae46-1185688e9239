{"timestamp":"2025-08-09T16:30:03+01:00","elapsed_time":1957.66,"system":{"memory_mb":71.94,"cpu_percent":1.8,"virtual_memory_mb":425745.69,"load_average":{"one_minute":7.7,"five_minutes":5.16,"fifteen_minutes":5.15},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":511949,"heap_free_slots":90327,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":90326,"string":215352,"array":45211,"hash":11237}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":0},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:30:08+01:00","elapsed_time":1962.74,"system":{"memory_mb":72.98,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":7.64,"five_minutes":5.19,"fifteen_minutes":5.16},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":515441,"heap_free_slots":86835,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":86834,"string":217272,"array":45627,"hash":11385}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":0},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:30:13+01:00","elapsed_time":1967.79,"system":{"memory_mb":15.02,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":7.27,"five_minutes":5.16,"fifteen_minutes":5.15},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":518933,"heap_free_slots":83343,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":83342,"string":219192,"array":46043,"hash":11533}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":0},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:30:18+01:00","elapsed_time":1972.88,"system":{"memory_mb":71.97,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.85,"five_minutes":5.1,"fifteen_minutes":5.13},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":522425,"heap_free_slots":79851,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":79850,"string":221112,"array":46459,"hash":11681}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":0},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:30:23+01:00","elapsed_time":1977.94,"system":{"memory_mb":72.91,"cpu_percent":0.3,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.7,"five_minutes":5.1,"fifteen_minutes":5.13},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":525917,"heap_free_slots":76359,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":76358,"string":223032,"array":46875,"hash":11829}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":0},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:30:28+01:00","elapsed_time":1983.0,"system":{"memory_mb":73.94,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.4,"five_minutes":5.07,"fifteen_minutes":5.12},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":529414,"heap_free_slots":72862,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":72861,"string":224952,"array":47296,"hash":11977}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":0},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:30:33+01:00","elapsed_time":1988.07,"system":{"memory_mb":72.53,"cpu_percent":0.3,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.37,"five_minutes":5.08,"fifteen_minutes":5.12},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":532906,"heap_free_slots":69370,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":69369,"string":226872,"array":47712,"hash":12125}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":0},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:30:39+01:00","elapsed_time":1993.15,"system":{"memory_mb":73.23,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.26,"five_minutes":5.08,"fifteen_minutes":5.12},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":536427,"heap_free_slots":65849,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":65848,"string":228821,"array":48128,"hash":12273}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":0},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:30:44+01:00","elapsed_time":1998.21,"system":{"memory_mb":72.41,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.24,"five_minutes":5.09,"fifteen_minutes":5.13},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":539919,"heap_free_slots":62357,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":62356,"string":230741,"array":48544,"hash":12421}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":0},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:30:49+01:00","elapsed_time":2003.27,"system":{"memory_mb":72.78,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.82,"five_minutes":5.03,"fifteen_minutes":5.1},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":543411,"heap_free_slots":58865,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":58864,"string":232661,"array":48960,"hash":12569}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":0},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:30:54+01:00","elapsed_time":2008.33,"system":{"memory_mb":72.22,"cpu_percent":1.3,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.67,"five_minutes":5.01,"fifteen_minutes":5.09},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":546903,"heap_free_slots":55373,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":55372,"string":234581,"array":49376,"hash":12717}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":0},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:30:59+01:00","elapsed_time":2013.39,"system":{"memory_mb":14.81,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.46,"five_minutes":4.98,"fifteen_minutes":5.08},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":550395,"heap_free_slots":51881,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":51880,"string":236501,"array":49792,"hash":12865}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":0},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:31:04+01:00","elapsed_time":2018.47,"system":{"memory_mb":71.8,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.34,"five_minutes":4.96,"fifteen_minutes":5.08},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":553887,"heap_free_slots":48389,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":48388,"string":238421,"array":50208,"hash":13013}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":0},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:31:09+01:00","elapsed_time":2023.53,"system":{"memory_mb":14.61,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.31,"five_minutes":4.96,"fifteen_minutes":5.07},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":557379,"heap_free_slots":44897,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":44896,"string":240341,"array":50624,"hash":13161}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":0},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:31:14+01:00","elapsed_time":2028.61,"system":{"memory_mb":72.28,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.77,"five_minutes":5.06,"fifteen_minutes":5.11},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":560871,"heap_free_slots":41405,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":41404,"string":242261,"array":51040,"hash":13309}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":0},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:31:19+01:00","elapsed_time":2033.67,"system":{"memory_mb":72.3,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.39,"five_minutes":4.99,"fifteen_minutes":5.08},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":564363,"heap_free_slots":37913,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":37912,"string":244181,"array":51456,"hash":13457}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":0},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:31:24+01:00","elapsed_time":2038.73,"system":{"memory_mb":72.44,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.76,"five_minutes":5.07,"fifteen_minutes":5.11},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":567855,"heap_free_slots":34421,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":34420,"string":246101,"array":51872,"hash":13605}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":7},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:31:29+01:00","elapsed_time":2043.79,"system":{"memory_mb":72.02,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.62,"five_minutes":5.06,"fifteen_minutes":5.11},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":571352,"heap_free_slots":30924,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":30923,"string":248021,"array":52293,"hash":13753}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":12},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:31:34+01:00","elapsed_time":2048.85,"system":{"memory_mb":72.17,"cpu_percent":1.1,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.89,"five_minutes":5.12,"fifteen_minutes":5.13},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":574844,"heap_free_slots":27432,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":27431,"string":249941,"array":52709,"hash":13901}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":14},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:31:39+01:00","elapsed_time":2053.9,"system":{"memory_mb":72.52,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.73,"five_minutes":5.1,"fifteen_minutes":5.12},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":27,"heap_allocated_pages":734,"heap_live_slots":578365,"heap_free_slots":23911,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":21},"objects":{"total":602276,"free":23910,"string":251890,"array":53125,"hash":14049}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":18},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:31:44+01:00","elapsed_time":2058.97,"system":{"memory_mb":19.72,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.52,"five_minutes":5.07,"fifteen_minutes":5.11},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":571841,"heap_free_slots":30435,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":30434,"string":247673,"array":52762,"hash":13808}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":21},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:31:49+01:00","elapsed_time":2064.04,"system":{"memory_mb":78.64,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.71,"five_minutes":5.12,"fifteen_minutes":5.13},"disk":{"total":"926Gi","used":"476Gi","available":"422Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":565387,"heap_free_slots":36889,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":36888,"string":243277,"array":52396,"hash":13553}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":24},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:31:54+01:00","elapsed_time":2069.1,"system":{"memory_mb":78.67,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.06,"five_minutes":5.2,"fifteen_minutes":5.15},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":568879,"heap_free_slots":33397,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":33396,"string":245197,"array":52812,"hash":13701}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:32:00+01:00","elapsed_time":2074.16,"system":{"memory_mb":80.61,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.81,"five_minutes":5.16,"fifteen_minutes":5.14},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":561236,"heap_free_slots":41040,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":41039,"string":240122,"array":52406,"hash":13347}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:32:05+01:00","elapsed_time":2079.22,"system":{"memory_mb":80.66,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":7.46,"five_minutes":5.55,"fifteen_minutes":5.28},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":564728,"heap_free_slots":37548,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":37547,"string":242042,"array":52822,"hash":13495}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:32:10+01:00","elapsed_time":2084.27,"system":{"memory_mb":14.45,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":8.54,"five_minutes":5.8,"fifteen_minutes":5.37},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":555487,"heap_free_slots":46789,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":46788,"string":236401,"array":52350,"hash":12992}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:32:15+01:00","elapsed_time":2089.35,"system":{"memory_mb":75.47,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":8.58,"five_minutes":5.86,"fifteen_minutes":5.39},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":545263,"heap_free_slots":57013,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":57012,"string":230585,"array":51773,"hash":12470}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:32:20+01:00","elapsed_time":2094.4,"system":{"memory_mb":75.52,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":8.53,"five_minutes":5.89,"fifteen_minutes":5.41},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":548755,"heap_free_slots":53521,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":53520,"string":232505,"array":52189,"hash":12618}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:32:25+01:00","elapsed_time":2099.47,"system":{"memory_mb":15.53,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":8.25,"five_minutes":5.88,"fifteen_minutes":5.4},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":538950,"heap_free_slots":63326,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":63325,"string":226816,"array":51726,"hash":12101}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:32:30+01:00","elapsed_time":2104.53,"system":{"memory_mb":75.33,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":8.07,"five_minutes":5.88,"fifteen_minutes":5.41},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":528966,"heap_free_slots":73310,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":73309,"string":221063,"array":51222,"hash":11583}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:32:35+01:00","elapsed_time":2109.58,"system":{"memory_mb":14.11,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":7.58,"five_minutes":5.81,"fifteen_minutes":5.39},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":532458,"heap_free_slots":69818,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":69817,"string":222983,"array":51638,"hash":11731}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:32:40+01:00","elapsed_time":2114.65,"system":{"memory_mb":73.69,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":7.3,"five_minutes":5.78,"fifteen_minutes":5.38},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":522532,"heap_free_slots":79744,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":79743,"string":217253,"array":51120,"hash":11221}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:32:45+01:00","elapsed_time":2119.71,"system":{"memory_mb":75.61,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.95,"five_minutes":5.74,"fifteen_minutes":5.37},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":513677,"heap_free_slots":88599,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":88598,"string":212719,"array":50479,"hash":10713}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:32:50+01:00","elapsed_time":2124.77,"system":{"memory_mb":73.83,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":7.12,"five_minutes":5.79,"fifteen_minutes":5.39},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":517169,"heap_free_slots":85107,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":85106,"string":214639,"array":50895,"hash":10861}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:32:55+01:00","elapsed_time":2129.83,"system":{"memory_mb":74.38,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":7.11,"five_minutes":5.81,"fifteen_minutes":5.4},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":510284,"heap_free_slots":91992,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":91991,"string":211998,"array":50300,"hash":10356}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:33:00+01:00","elapsed_time":2134.88,"system":{"memory_mb":71.56,"cpu_percent":0.6,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.86,"five_minutes":5.78,"fifteen_minutes":5.39},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":513776,"heap_free_slots":88500,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":88499,"string":213918,"array":50716,"hash":10504}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:33:05+01:00","elapsed_time":2139.94,"system":{"memory_mb":16.44,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.71,"five_minutes":5.77,"fifteen_minutes":5.39},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":506228,"heap_free_slots":96048,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":96047,"string":210944,"array":49924,"hash":9981}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:33:10+01:00","elapsed_time":2145.01,"system":{"memory_mb":72.5,"cpu_percent":0.9,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.33,"five_minutes":5.7,"fifteen_minutes":5.37},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":509720,"heap_free_slots":92556,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":92555,"string":212864,"array":50340,"hash":10129}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:33:15+01:00","elapsed_time":2150.06,"system":{"memory_mb":16.92,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.22,"five_minutes":5.69,"fifteen_minutes":5.36},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":502140,"heap_free_slots":100136,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":100135,"string":209889,"array":49492,"hash":9622}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:33:20+01:00","elapsed_time":2155.13,"system":{"memory_mb":72.58,"cpu_percent":1.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.13,"five_minutes":5.68,"fifteen_minutes":5.36},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":505632,"heap_free_slots":96644,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":96643,"string":211809,"array":49908,"hash":9770}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:33:26+01:00","elapsed_time":2160.19,"system":{"memory_mb":73.67,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.8,"five_minutes":5.62,"fifteen_minutes":5.34},"disk":{"total":"926Gi","used":"476Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":509129,"heap_free_slots":93147,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":93146,"string":213729,"array":50329,"hash":9918}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:33:31+01:00","elapsed_time":2165.24,"system":{"memory_mb":75.77,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.45,"five_minutes":5.76,"fifteen_minutes":5.39},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":502512,"heap_free_slots":99764,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":99763,"string":210168,"array":49412,"hash":9398}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:33:36+01:00","elapsed_time":2170.31,"system":{"memory_mb":74.58,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.1,"five_minutes":5.69,"fifteen_minutes":5.37},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":506004,"heap_free_slots":96272,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":96271,"string":212088,"array":49828,"hash":9546}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:33:41+01:00","elapsed_time":2175.37,"system":{"memory_mb":76.23,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.69,"five_minutes":5.62,"fifteen_minutes":5.34},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":500943,"heap_free_slots":101333,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":101332,"string":208722,"array":48999,"hash":9030}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:33:46+01:00","elapsed_time":2180.42,"system":{"memory_mb":76.38,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.55,"five_minutes":5.59,"fifteen_minutes":5.34},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":504435,"heap_free_slots":97841,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":97840,"string":210642,"array":49415,"hash":9178}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:33:51+01:00","elapsed_time":2185.48,"system":{"memory_mb":74.63,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.51,"five_minutes":5.58,"fifteen_minutes":5.33},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":503021,"heap_free_slots":99255,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":99254,"string":210081,"array":48605,"hash":9326}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:33:56+01:00","elapsed_time":2190.57,"system":{"memory_mb":72.81,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.31,"five_minutes":5.54,"fifteen_minutes":5.32},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":506513,"heap_free_slots":95763,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":95762,"string":212001,"array":49021,"hash":9474}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:34:01+01:00","elapsed_time":2195.64,"system":{"memory_mb":14.28,"cpu_percent":1.7,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.28,"five_minutes":5.53,"fifteen_minutes":5.32},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":505099,"heap_free_slots":97177,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":97176,"string":211386,"array":48215,"hash":9622}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:34:06+01:00","elapsed_time":2200.73,"system":{"memory_mb":72.78,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.34,"five_minutes":5.54,"fifteen_minutes":5.32},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":508591,"heap_free_slots":93685,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":93684,"string":213306,"array":48631,"hash":9770}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:34:11+01:00","elapsed_time":2205.78,"system":{"memory_mb":13.69,"cpu_percent":0.5,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.47,"five_minutes":5.56,"fifteen_minutes":5.33},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":512083,"heap_free_slots":90193,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":90192,"string":215226,"array":49047,"hash":9918}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:34:16+01:00","elapsed_time":2210.85,"system":{"memory_mb":73.63,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.27,"five_minutes":5.52,"fifteen_minutes":5.32},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":510669,"heap_free_slots":91607,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":91606,"string":214604,"array":48253,"hash":10066}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:34:21+01:00","elapsed_time":2215.92,"system":{"memory_mb":13.73,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.17,"five_minutes":5.49,"fifteen_minutes":5.31},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":514161,"heap_free_slots":88115,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":88114,"string":216524,"array":48669,"hash":10214}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:34:26+01:00","elapsed_time":2220.99,"system":{"memory_mb":74.2,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.92,"five_minutes":5.43,"fifteen_minutes":5.29},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":512751,"heap_free_slots":89525,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":89524,"string":215980,"array":47854,"hash":10362}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:34:31+01:00","elapsed_time":2226.04,"system":{"memory_mb":74.44,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.92,"five_minutes":5.43,"fifteen_minutes":5.29},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":516243,"heap_free_slots":86033,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":86032,"string":217900,"array":48270,"hash":10510}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:34:36+01:00","elapsed_time":2231.1,"system":{"memory_mb":69.16,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.69,"five_minutes":5.37,"fifteen_minutes":5.27},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":514833,"heap_free_slots":87443,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":87442,"string":217292,"array":47456,"hash":10658}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:34:42+01:00","elapsed_time":2236.17,"system":{"memory_mb":74.17,"cpu_percent":1.1,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.31,"five_minutes":5.28,"fifteen_minutes":5.24},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":518354,"heap_free_slots":83922,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":83921,"string":219241,"array":47872,"hash":10806}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:34:47+01:00","elapsed_time":2241.22,"system":{"memory_mb":78.23,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.29,"five_minutes":5.26,"fifteen_minutes":5.23},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":502212,"heap_free_slots":100064,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":100063,"string":211041,"array":43404,"hash":10954}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:34:52+01:00","elapsed_time":2246.27,"system":{"memory_mb":78.34,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.26,"five_minutes":5.24,"fifteen_minutes":5.22},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":505704,"heap_free_slots":96572,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":96571,"string":212961,"array":43820,"hash":11102}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:34:57+01:00","elapsed_time":2251.32,"system":{"memory_mb":17.39,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.12,"five_minutes":5.4,"fifteen_minutes":5.28},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":509196,"heap_free_slots":93080,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":93079,"string":214881,"array":44236,"hash":11250}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:35:02+01:00","elapsed_time":2256.41,"system":{"memory_mb":71.58,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.79,"five_minutes":5.32,"fifteen_minutes":5.25},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":512688,"heap_free_slots":89588,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":89587,"string":216801,"array":44652,"hash":11398}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:35:07+01:00","elapsed_time":2261.48,"system":{"memory_mb":71.97,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.49,"five_minutes":5.25,"fifteen_minutes":5.23},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":516180,"heap_free_slots":86096,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":86095,"string":218721,"array":45068,"hash":11546}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:35:12+01:00","elapsed_time":2266.54,"system":{"memory_mb":72.47,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.29,"five_minutes":5.2,"fifteen_minutes":5.21},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":519672,"heap_free_slots":82604,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":82603,"string":220641,"array":45484,"hash":11694}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:35:17+01:00","elapsed_time":2271.6,"system":{"memory_mb":72.83,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.95,"five_minutes":5.11,"fifteen_minutes":5.18},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":523164,"heap_free_slots":79112,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":79111,"string":222561,"array":45900,"hash":11842}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:35:22+01:00","elapsed_time":2276.66,"system":{"memory_mb":73.16,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.95,"five_minutes":5.09,"fifteen_minutes":5.17},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":526656,"heap_free_slots":75620,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":75619,"string":224481,"array":46316,"hash":11990}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:35:27+01:00","elapsed_time":2281.72,"system":{"memory_mb":74.2,"cpu_percent":2.5,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.79,"five_minutes":5.04,"fifteen_minutes":5.15},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":530153,"heap_free_slots":72123,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":72122,"string":226401,"array":46737,"hash":12138}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:35:32+01:00","elapsed_time":2286.78,"system":{"memory_mb":74.61,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.49,"five_minutes":4.96,"fifteen_minutes":5.12},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":533645,"heap_free_slots":68631,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":68630,"string":228321,"array":47153,"hash":12286}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:35:37+01:00","elapsed_time":2291.85,"system":{"memory_mb":75.0,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.21,"five_minutes":4.88,"fifteen_minutes":5.09},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":537137,"heap_free_slots":65139,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":65138,"string":230241,"array":47569,"hash":12434}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:35:42+01:00","elapsed_time":2296.92,"system":{"memory_mb":75.31,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.19,"five_minutes":4.84,"fifteen_minutes":5.08},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":540658,"heap_free_slots":61618,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":61617,"string":232190,"array":47985,"hash":12582}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:35:47+01:00","elapsed_time":2301.98,"system":{"memory_mb":75.55,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.1,"five_minutes":4.8,"fifteen_minutes":5.06},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":544150,"heap_free_slots":58126,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":58125,"string":234110,"array":48401,"hash":12730}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:35:52+01:00","elapsed_time":2307.04,"system":{"memory_mb":75.84,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.09,"five_minutes":4.77,"fifteen_minutes":5.05},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":547642,"heap_free_slots":54634,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":54633,"string":236030,"array":48817,"hash":12878}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:35:57+01:00","elapsed_time":2312.1,"system":{"memory_mb":75.42,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.24,"five_minutes":4.77,"fifteen_minutes":5.05},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":551134,"heap_free_slots":51142,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":51141,"string":237950,"array":49233,"hash":13026}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:36:03+01:00","elapsed_time":2317.18,"system":{"memory_mb":75.69,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":2.98,"five_minutes":4.69,"fifteen_minutes":5.02},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":554626,"heap_free_slots":47650,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":47649,"string":239870,"array":49649,"hash":13174}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:36:08+01:00","elapsed_time":2322.23,"system":{"memory_mb":72.28,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":2.82,"five_minutes":4.63,"fifteen_minutes":5.0},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":558118,"heap_free_slots":44158,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":44157,"string":241790,"array":50065,"hash":13322}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:36:13+01:00","elapsed_time":2327.3,"system":{"memory_mb":72.28,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":2.68,"five_minutes":4.57,"fifteen_minutes":4.97},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":561610,"heap_free_slots":40666,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":40665,"string":243710,"array":50481,"hash":13470}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:36:18+01:00","elapsed_time":2332.36,"system":{"memory_mb":54.44,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":2.46,"five_minutes":4.49,"fifteen_minutes":4.94},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":565102,"heap_free_slots":37174,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":37173,"string":245630,"array":50897,"hash":13618}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:36:23+01:00","elapsed_time":2337.43,"system":{"memory_mb":71.16,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":2.59,"five_minutes":4.48,"fifteen_minutes":4.94},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":568594,"heap_free_slots":33682,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":33681,"string":247550,"array":51313,"hash":13766}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:36:28+01:00","elapsed_time":2342.5,"system":{"memory_mb":32.2,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":2.46,"five_minutes":4.43,"fifteen_minutes":4.91},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":572091,"heap_free_slots":30185,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":30184,"string":249470,"array":51734,"hash":13914}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:36:33+01:00","elapsed_time":2347.57,"system":{"memory_mb":72.61,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":2.5,"five_minutes":4.4,"fifteen_minutes":4.9},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":28,"heap_allocated_pages":734,"heap_live_slots":575583,"heap_free_slots":26693,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":22},"objects":{"total":602276,"free":26692,"string":251390,"array":52150,"hash":14062}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:36:38+01:00","elapsed_time":2352.65,"system":{"memory_mb":77.69,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":2.38,"five_minutes":4.34,"fifteen_minutes":4.88},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":569059,"heap_free_slots":33217,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":33216,"string":247151,"array":51844,"hash":13813}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:36:43+01:00","elapsed_time":2357.72,"system":{"memory_mb":79.67,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":2.27,"five_minutes":4.29,"fifteen_minutes":4.86},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":562630,"heap_free_slots":39646,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":39645,"string":242753,"array":51516,"hash":13567}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:36:48+01:00","elapsed_time":2362.78,"system":{"memory_mb":79.27,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":2.49,"five_minutes":4.3,"fifteen_minutes":4.86},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":566122,"heap_free_slots":36154,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":36153,"string":244673,"array":51932,"hash":13715}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:36:53+01:00","elapsed_time":2367.84,"system":{"memory_mb":39.22,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":2.29,"five_minutes":4.23,"fifteen_minutes":4.83},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":559170,"heap_free_slots":43106,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":43105,"string":239892,"array":51583,"hash":13392}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:36:58+01:00","elapsed_time":2372.92,"system":{"memory_mb":73.44,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":2.19,"five_minutes":4.18,"fifteen_minutes":4.81},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":562662,"heap_free_slots":39614,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":39613,"string":241812,"array":51999,"hash":13540}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:37:03+01:00","elapsed_time":2377.97,"system":{"memory_mb":75.66,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.69,"five_minutes":4.45,"fifteen_minutes":4.9},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":554448,"heap_free_slots":47828,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":47827,"string":236226,"array":51643,"hash":13043}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:37:08+01:00","elapsed_time":2383.04,"system":{"memory_mb":75.61,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.64,"five_minutes":4.43,"fifteen_minutes":4.89},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":544227,"heap_free_slots":58049,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":58048,"string":230410,"array":51106,"hash":12511}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:37:13+01:00","elapsed_time":2388.11,"system":{"memory_mb":75.77,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.42,"five_minutes":4.37,"fifteen_minutes":4.87},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":547719,"heap_free_slots":54557,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":54556,"string":232330,"array":51522,"hash":12659}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:37:19+01:00","elapsed_time":2393.19,"system":{"memory_mb":76.81,"cpu_percent":2.4,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.23,"five_minutes":4.32,"fifteen_minutes":4.84},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":537914,"heap_free_slots":64362,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":64361,"string":226586,"array":51091,"hash":12149}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:37:24+01:00","elapsed_time":2398.27,"system":{"memory_mb":77.67,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.05,"five_minutes":4.26,"fifteen_minutes":4.82},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":527925,"heap_free_slots":74351,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":74350,"string":220876,"array":50599,"hash":11617}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:37:29+01:00","elapsed_time":2403.33,"system":{"memory_mb":79.28,"cpu_percent":1.8,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.29,"five_minutes":4.29,"fifteen_minutes":4.83},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":531422,"heap_free_slots":70854,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":70853,"string":222796,"array":51020,"hash":11765}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:37:34+01:00","elapsed_time":2408.41,"system":{"memory_mb":80.17,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.5,"five_minutes":4.32,"fifteen_minutes":4.83},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":521468,"heap_free_slots":80808,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":80807,"string":216956,"array":50557,"hash":11258}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:37:39+01:00","elapsed_time":2413.49,"system":{"memory_mb":79.61,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.3,"five_minutes":4.26,"fifteen_minutes":4.81},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":512613,"heap_free_slots":89663,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":89662,"string":212402,"array":49992,"hash":10742}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:37:44+01:00","elapsed_time":2418.57,"system":{"memory_mb":79.67,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.28,"five_minutes":4.24,"fifteen_minutes":4.8},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":516134,"heap_free_slots":86142,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":86141,"string":214351,"array":50408,"hash":10890}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:37:49+01:00","elapsed_time":2423.65,"system":{"memory_mb":80.8,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.1,"five_minutes":4.19,"fifteen_minutes":4.78},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":509249,"heap_free_slots":93027,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":93026,"string":211716,"array":49868,"hash":10349}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:37:54+01:00","elapsed_time":2428.73,"system":{"memory_mb":80.8,"cpu_percent":1.5,"virtual_memory_mb":425745.69,"load_average":{"one_minute":2.93,"five_minutes":4.14,"fifteen_minutes":4.76},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":512741,"heap_free_slots":89535,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":89534,"string":213636,"array":50284,"hash":10497}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:37:59+01:00","elapsed_time":2433.8,"system":{"memory_mb":82.2,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.73,"five_minutes":4.28,"fifteen_minutes":4.8},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":505193,"heap_free_slots":97083,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":97082,"string":210659,"array":49544,"hash":9991}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:38:04+01:00","elapsed_time":2438.87,"system":{"memory_mb":82.13,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.6,"five_minutes":4.24,"fifteen_minutes":4.79},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":508685,"heap_free_slots":93591,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":93590,"string":212579,"array":49960,"hash":10139}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:38:09+01:00","elapsed_time":2443.95,"system":{"memory_mb":81.69,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.47,"five_minutes":4.21,"fifteen_minutes":4.77},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":501142,"heap_free_slots":101134,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":101133,"string":209594,"array":49207,"hash":9609}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:38:14+01:00","elapsed_time":2449.04,"system":{"memory_mb":81.61,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.19,"five_minutes":4.14,"fifteen_minutes":4.74},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":504634,"heap_free_slots":97642,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":97641,"string":211514,"array":49623,"hash":9757}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:38:19+01:00","elapsed_time":2454.12,"system":{"memory_mb":81.61,"cpu_percent":2.8,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.09,"five_minutes":4.1,"fifteen_minutes":4.73},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":508126,"heap_free_slots":94150,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":94149,"string":213434,"array":50039,"hash":9905}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:38:25+01:00","elapsed_time":2459.19,"system":{"memory_mb":81.73,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.57,"five_minutes":4.18,"fifteen_minutes":4.75},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":502263,"heap_free_slots":100013,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":100012,"string":210303,"array":49325,"hash":9389}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:38:30+01:00","elapsed_time":2464.27,"system":{"memory_mb":81.84,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.52,"five_minutes":4.16,"fifteen_minutes":4.74},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":505760,"heap_free_slots":96516,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":96515,"string":212223,"array":49746,"hash":9537}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:38:35+01:00","elapsed_time":2469.35,"system":{"memory_mb":67.2,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.56,"five_minutes":4.16,"fifteen_minutes":4.74},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":500675,"heap_free_slots":101601,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":101600,"string":208760,"array":48927,"hash":9014}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:38:40+01:00","elapsed_time":2474.43,"system":{"memory_mb":74.59,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.59,"five_minutes":4.16,"fifteen_minutes":4.73},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":504167,"heap_free_slots":98109,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":98108,"string":210680,"array":49343,"hash":9162}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:38:45+01:00","elapsed_time":2479.51,"system":{"memory_mb":73.88,"cpu_percent":2.4,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.89,"five_minutes":4.2,"fifteen_minutes":4.74},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":502782,"heap_free_slots":99494,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":99493,"string":210074,"array":48537,"hash":9310}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:38:50+01:00","elapsed_time":2484.58,"system":{"memory_mb":66.78,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.38,"five_minutes":4.3,"fifteen_minutes":4.77},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":506274,"heap_free_slots":96002,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":96001,"string":211994,"array":48953,"hash":9458}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:38:55+01:00","elapsed_time":2489.65,"system":{"memory_mb":38.14,"cpu_percent":1.6,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.91,"five_minutes":4.41,"fifteen_minutes":4.81},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":509766,"heap_free_slots":92510,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":92509,"string":213914,"array":49369,"hash":9606}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:39:00+01:00","elapsed_time":2494.71,"system":{"memory_mb":72.7,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.12,"five_minutes":4.67,"fifteen_minutes":4.9},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":508352,"heap_free_slots":93924,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":93923,"string":213323,"array":48564,"hash":9754}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:39:05+01:00","elapsed_time":2499.77,"system":{"memory_mb":72.88,"cpu_percent":2.7,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.71,"five_minutes":4.6,"fifteen_minutes":4.87},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":511844,"heap_free_slots":90432,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":90431,"string":215243,"array":48980,"hash":9902}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:39:10+01:00","elapsed_time":2504.85,"system":{"memory_mb":73.7,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.49,"five_minutes":4.58,"fifteen_minutes":4.86},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":510429,"heap_free_slots":91847,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":91846,"string":214582,"array":48196,"hash":10050}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:39:15+01:00","elapsed_time":2509.92,"system":{"memory_mb":65.09,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.37,"five_minutes":4.57,"fifteen_minutes":4.86},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":513921,"heap_free_slots":88355,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":88354,"string":216502,"array":48612,"hash":10198}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:39:20+01:00","elapsed_time":2514.98,"system":{"memory_mb":72.95,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.1,"five_minutes":4.52,"fifteen_minutes":4.84},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":512506,"heap_free_slots":89770,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":89769,"string":215884,"array":47796,"hash":10346}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:39:25+01:00","elapsed_time":2520.06,"system":{"memory_mb":73.06,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.77,"five_minutes":4.47,"fifteen_minutes":4.82},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":515998,"heap_free_slots":86278,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":86277,"string":217804,"array":48212,"hash":10494}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:39:30+01:00","elapsed_time":2525.12,"system":{"memory_mb":74.64,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.71,"five_minutes":4.46,"fifteen_minutes":4.81},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":514593,"heap_free_slots":87683,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":87682,"string":217274,"array":47405,"hash":10642}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:39:36+01:00","elapsed_time":2530.19,"system":{"memory_mb":74.81,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.73,"five_minutes":4.47,"fifteen_minutes":4.81},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":518085,"heap_free_slots":84191,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":84190,"string":219194,"array":47821,"hash":10790}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:39:41+01:00","elapsed_time":2535.28,"system":{"memory_mb":74.81,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.51,"five_minutes":4.43,"fifteen_minutes":4.8},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":521577,"heap_free_slots":80699,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":80698,"string":221114,"array":48237,"hash":10938}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:39:46+01:00","elapsed_time":2540.36,"system":{"memory_mb":78.53,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.31,"five_minutes":4.39,"fifteen_minutes":4.78},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":505464,"heap_free_slots":96812,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":96811,"string":212855,"array":43780,"hash":11086}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:39:51+01:00","elapsed_time":2545.44,"system":{"memory_mb":78.42,"cpu_percent":1.3,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.29,"five_minutes":4.38,"fifteen_minutes":4.77},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":508956,"heap_free_slots":93320,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":93319,"string":214775,"array":44196,"hash":11234}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:39:56+01:00","elapsed_time":2550.5,"system":{"memory_mb":76.92,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.1,"five_minutes":4.34,"fifteen_minutes":4.76},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":512448,"heap_free_slots":89828,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":89827,"string":216695,"array":44612,"hash":11382}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:40:01+01:00","elapsed_time":2555.58,"system":{"memory_mb":71.36,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.66,"five_minutes":4.45,"fifteen_minutes":4.79},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":515940,"heap_free_slots":86336,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":86335,"string":218615,"array":45028,"hash":11530}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:40:06+01:00","elapsed_time":2560.66,"system":{"memory_mb":71.97,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.36,"five_minutes":4.39,"fifteen_minutes":4.77},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":519432,"heap_free_slots":82844,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":82843,"string":220535,"array":45444,"hash":11678}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:40:11+01:00","elapsed_time":2565.74,"system":{"memory_mb":72.28,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.49,"five_minutes":4.42,"fifteen_minutes":4.78},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":522924,"heap_free_slots":79352,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":79351,"string":222455,"array":45860,"hash":11826}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:40:16+01:00","elapsed_time":2570.81,"system":{"memory_mb":54.81,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":11.02,"five_minutes":5.77,"fifteen_minutes":5.25},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":526416,"heap_free_slots":75860,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":75859,"string":224375,"array":46276,"hash":11974}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:40:21+01:00","elapsed_time":2575.98,"system":{"memory_mb":72.47,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":12.06,"five_minutes":6.07,"fifteen_minutes":5.36},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":529908,"heap_free_slots":72368,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":72367,"string":226295,"array":46692,"hash":12122}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:40:26+01:00","elapsed_time":2581.06,"system":{"memory_mb":39.72,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":11.65,"five_minutes":6.09,"fifteen_minutes":5.37},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":533405,"heap_free_slots":68871,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":68870,"string":228215,"array":47113,"hash":12270}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:40:31+01:00","elapsed_time":2586.13,"system":{"memory_mb":54.67,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":11.12,"five_minutes":6.07,"fifteen_minutes":5.37},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":536897,"heap_free_slots":65379,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":65378,"string":230135,"array":47529,"hash":12418}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:40:37+01:00","elapsed_time":2591.2,"system":{"memory_mb":38.13,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":10.87,"five_minutes":6.1,"fifteen_minutes":5.38},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":540389,"heap_free_slots":61887,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":61886,"string":232055,"array":47945,"hash":12566}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:40:42+01:00","elapsed_time":2596.27,"system":{"memory_mb":71.7,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":10.88,"five_minutes":6.18,"fifteen_minutes":5.42},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":543881,"heap_free_slots":58395,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":58394,"string":233975,"array":48361,"hash":12714}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:40:47+01:00","elapsed_time":2601.35,"system":{"memory_mb":72.08,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":10.33,"five_minutes":6.15,"fifteen_minutes":5.41},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":547402,"heap_free_slots":54874,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":54873,"string":235924,"array":48777,"hash":12862}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:40:52+01:00","elapsed_time":2606.4,"system":{"memory_mb":71.89,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":11.34,"five_minutes":6.43,"fifteen_minutes":5.51},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":550894,"heap_free_slots":51382,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":51381,"string":237844,"array":49193,"hash":13010}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:40:57+01:00","elapsed_time":2611.5,"system":{"memory_mb":46.47,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":10.68,"five_minutes":6.37,"fifteen_minutes":5.5},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":554386,"heap_free_slots":47890,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":47889,"string":239764,"array":49609,"hash":13158}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:41:02+01:00","elapsed_time":2616.57,"system":{"memory_mb":71.58,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":13.58,"five_minutes":7.04,"fifteen_minutes":5.74},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":557878,"heap_free_slots":44398,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":44397,"string":241684,"array":50025,"hash":13306}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:41:07+01:00","elapsed_time":2621.63,"system":{"memory_mb":13.53,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":15.7,"five_minutes":7.59,"fifteen_minutes":5.94},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":561370,"heap_free_slots":40906,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":40905,"string":243604,"array":50441,"hash":13454}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:41:12+01:00","elapsed_time":2626.72,"system":{"memory_mb":71.41,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":14.84,"five_minutes":7.55,"fifteen_minutes":5.93},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":564862,"heap_free_slots":37414,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":37413,"string":245524,"array":50857,"hash":13602}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:41:17+01:00","elapsed_time":2631.78,"system":{"memory_mb":71.23,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":13.89,"five_minutes":7.47,"fifteen_minutes":5.92},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":568354,"heap_free_slots":33922,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":33921,"string":247444,"array":51273,"hash":13750}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:41:22+01:00","elapsed_time":2636.84,"system":{"memory_mb":13.59,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":13.02,"five_minutes":7.4,"fifteen_minutes":5.9},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":571846,"heap_free_slots":30430,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":30429,"string":249364,"array":51689,"hash":13898}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:41:27+01:00","elapsed_time":2641.92,"system":{"memory_mb":72.0,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":12.14,"five_minutes":7.31,"fifteen_minutes":5.88},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":29,"heap_allocated_pages":734,"heap_live_slots":575343,"heap_free_slots":26933,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":23},"objects":{"total":602276,"free":26932,"string":251284,"array":52110,"hash":14046}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:41:32+01:00","elapsed_time":2646.98,"system":{"memory_mb":77.17,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":11.49,"five_minutes":7.25,"fifteen_minutes":5.87},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":568818,"heap_free_slots":33458,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":33457,"string":247016,"array":51801,"hash":13823}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:41:37+01:00","elapsed_time":2652.04,"system":{"memory_mb":78.55,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":10.73,"five_minutes":7.16,"fifteen_minutes":5.84},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":562360,"heap_free_slots":39916,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":39915,"string":242568,"array":51503,"hash":13564}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:41:42+01:00","elapsed_time":2657.11,"system":{"memory_mb":77.31,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":9.87,"five_minutes":7.05,"fifteen_minutes":5.81},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":565852,"heap_free_slots":36424,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":36423,"string":244488,"array":51919,"hash":13712}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:41:48+01:00","elapsed_time":2662.18,"system":{"memory_mb":79.38,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":9.16,"five_minutes":6.94,"fifteen_minutes":5.78},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":558957,"heap_free_slots":43319,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":43318,"string":239788,"array":51547,"hash":13412}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:41:53+01:00","elapsed_time":2667.25,"system":{"memory_mb":79.38,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":8.5,"five_minutes":6.85,"fifteen_minutes":5.75},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":562449,"heap_free_slots":39827,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":39826,"string":241708,"array":51963,"hash":13560}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:41:58+01:00","elapsed_time":2672.3,"system":{"memory_mb":19.19,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":7.98,"five_minutes":6.77,"fifteen_minutes":5.73},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":554233,"heap_free_slots":48043,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":48042,"string":236098,"array":51650,"hash":13045}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:42:03+01:00","elapsed_time":2677.38,"system":{"memory_mb":73.58,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":7.5,"five_minutes":6.69,"fifteen_minutes":5.71},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":544013,"heap_free_slots":58263,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":58262,"string":230266,"array":51086,"hash":12528}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:42:08+01:00","elapsed_time":2682.44,"system":{"memory_mb":74.08,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":7.14,"five_minutes":6.62,"fifteen_minutes":5.69},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":547505,"heap_free_slots":54771,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":54770,"string":232186,"array":51502,"hash":12676}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:42:13+01:00","elapsed_time":2687.51,"system":{"memory_mb":17.53,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.65,"five_minutes":6.53,"fifteen_minutes":5.66},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":537700,"heap_free_slots":64576,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":64575,"string":226486,"array":51042,"hash":12148}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:42:18+01:00","elapsed_time":2692.59,"system":{"memory_mb":74.69,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.36,"five_minutes":6.47,"fifteen_minutes":5.65},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":527711,"heap_free_slots":74565,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":74564,"string":220718,"array":50583,"hash":11640}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:42:23+01:00","elapsed_time":2697.65,"system":{"memory_mb":74.69,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.93,"five_minutes":6.38,"fifteen_minutes":5.62},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":531203,"heap_free_slots":71073,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":71072,"string":222638,"array":50999,"hash":11788}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:42:28+01:00","elapsed_time":2702.73,"system":{"memory_mb":76.81,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.61,"five_minutes":6.31,"fifteen_minutes":5.6},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":521254,"heap_free_slots":81022,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":81021,"string":216886,"array":50531,"hash":11252}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:42:33+01:00","elapsed_time":2707.81,"system":{"memory_mb":78.05,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.32,"five_minutes":6.24,"fifteen_minutes":5.58},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":512399,"heap_free_slots":89877,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":89876,"string":212230,"array":49979,"hash":10749}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:42:38+01:00","elapsed_time":2712.86,"system":{"memory_mb":78.05,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":6.18,"five_minutes":6.4,"fifteen_minutes":5.64},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":515891,"heap_free_slots":86385,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":86384,"string":214150,"array":50395,"hash":10897}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:42:43+01:00","elapsed_time":2717.93,"system":{"memory_mb":79.11,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.84,"five_minutes":6.32,"fifteen_minutes":5.62},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":509006,"heap_free_slots":93270,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":93269,"string":211586,"array":49825,"hash":10363}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:42:48+01:00","elapsed_time":2722.97,"system":{"memory_mb":79.17,"cpu_percent":0.4,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.46,"five_minutes":6.23,"fifteen_minutes":5.59},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":512527,"heap_free_slots":89749,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":89748,"string":213535,"array":50241,"hash":10511}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:42:53+01:00","elapsed_time":2728.03,"system":{"memory_mb":80.55,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":5.02,"five_minutes":6.13,"fifteen_minutes":5.56},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":504979,"heap_free_slots":97297,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":97296,"string":210507,"array":49535,"hash":9994}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:42:58+01:00","elapsed_time":2733.07,"system":{"memory_mb":20.67,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.78,"five_minutes":6.06,"fifteen_minutes":5.53},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":508471,"heap_free_slots":93805,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":93804,"string":212427,"array":49951,"hash":10142}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:43:03+01:00","elapsed_time":2738.13,"system":{"memory_mb":74.88,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.39,"five_minutes":5.96,"fifteen_minutes":5.5},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":500928,"heap_free_slots":101348,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":101347,"string":209514,"array":49172,"hash":9606}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:43:09+01:00","elapsed_time":2743.2,"system":{"memory_mb":73.38,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":4.12,"five_minutes":5.88,"fifteen_minutes":5.48},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":504420,"heap_free_slots":97856,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":97855,"string":211434,"array":49588,"hash":9754}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
{"timestamp":"2025-08-09T16:43:14+01:00","elapsed_time":2748.25,"system":{"memory_mb":73.41,"cpu_percent":0.0,"virtual_memory_mb":425745.69,"load_average":{"one_minute":3.87,"five_minutes":5.8,"fifteen_minutes":5.45},"disk":{"total":"926Gi","used":"477Gi","available":"421Gi","usage_percent":"54%"}},"ruby":{"version":"3.4.3","platform":"arm64-darwin24","gc":{"count":30,"heap_allocated_pages":734,"heap_live_slots":507912,"heap_free_slots":94364,"heap_final_slots":0,"total_allocated_pages":734,"major_gc_count":6,"minor_gc_count":24},"objects":{"total":602276,"free":94363,"string":213354,"array":50004,"hash":9902}},"database":{"error":"undefined method 'checked_out' for an instance of ActiveRecord::ConnectionAdapters::ConnectionPool"},"web_server":{"error":"undefined method 'stats' for nil"},"application":{"rails_version":"8.0.2","environment":"development","solid_queue_error":"undefined method 'pending' for class SolidQueue::Job","accounts":{"total":51,"active":41},"users":{"total":47,"active":25},"pipelines":{"total":180,"active":62,"running":0}}}
