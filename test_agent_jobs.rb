#!/usr/bin/env ruby

# Test script for AI Agent background jobs
puts '=== Testing AI Agent Background Jobs ==='

# Create test account with starter subscription
account = Account.create!(
  name: 'Job Test Company',
  subdomain: 'jobtestai' + rand(1000).to_s
)
subscription = account.subscription
subscription.update!(plan: :starter, stripe_customer_id: "cus_job_test_#{rand(10000)}")

puts "Test account created: #{account.name}"
puts "Subscription plan: #{subscription.plan}"

# Test GenerateRecommendationsJob
puts "\n--- Testing GenerateRecommendationsJob ---"
begin
  GenerateRecommendationsJob.perform_now(account.id)
  puts "✅ GenerateRecommendationsJob executed successfully"
  puts "Generated recommendations: #{account.agent_recommendations.count}"
rescue => e
  puts "❌ GenerateRecommendationsJob failed: #{e.message}"
end

# Create a test recommendation for other jobs
recommendation = account.agent_recommendations.create!(
  recommendation_type: :optimization,
  title: 'Test Performance Optimization',
  description: 'A test recommendation for job testing',
  estimated_value: 150.0,
  confidence_score: 90
)

# Test RevenueTrackingJob
puts "\n--- Testing RevenueTrackingJob ---"
begin
  RevenueTrackingJob.perform_now(account.id)
  puts "✅ RevenueTrackingJob executed successfully"

  # Check if cache was updated
  cache_key = "agent_revenue_metrics:#{account.id}"
  cached_data = Rails.cache.read(cache_key)
  if cached_data
    puts "Cache updated with revenue data: $#{cached_data[:total_revenue]}"
  else
    puts "No cache data found"
  end
rescue => e
  puts "❌ RevenueTrackingJob failed: #{e.message}"
end

# Test NotificationJob
puts "\n--- Testing NotificationJob ---"
begin
  NotificationJob.perform_now(
    account_id: account.id,
    type: 'new_recommendation',
    data: { recommendation_id: recommendation.id }
  )
  puts "✅ NotificationJob executed successfully"
rescue => e
  puts "❌ NotificationJob failed: #{e.message}"
end

# Test ImplementRecommendationJob
puts "\n--- Testing ImplementRecommendationJob ---"
begin
  recommendation.update!(status: :accepted) # Accept first
  ImplementRecommendationJob.perform_now(recommendation.id)
  puts "✅ ImplementRecommendationJob executed successfully"

  recommendation.reload
  puts "Recommendation status after implementation: #{recommendation.status}"
rescue => e
  puts "❌ ImplementRecommendationJob failed: #{e.message}"
end

puts "\n=== Background Jobs Test Complete ==="

# Clean up
account.destroy!
puts "Test account cleaned up"
